/**
 * Enhanced messaging service with comprehensive fallback modes and retry mechanisms.
 * 
 * This service provides robust handling of messaging operations with:
 * - Multiple fallback modes for different failure scenarios
 * - Automatic retry with exponential backoff
 * - Detailed error messages with actionable information
 * - Offline message queuing
 * - Network quality-based optimizations
 */

import axios from 'axios';
import networkMonitor from './NetworkMonitor';
import { toast } from 'react-toastify';
import { getToken } from './AuthService';
import { PrometheusMetricsCollector } from '../utils/PrometheusMetricsCollector';
import { MessageEncryptionService } from '../utils/MessageEncryptionService';
import { MessageDeduplicationService } from '../utils/MessageDeduplicationService';

// Configuration constants
const CONFIG = {
  // WebSocket configuration
  WS_RECONNECT_ATTEMPTS: 10,
  WS_INITIAL_RECONNECT_INTERVAL: 3000,
  WS_MAX_RECONNECT_INTERVAL: 60000,
  WS_RECONNECT_BACKOFF_FACTOR: 1.5,
  WS_SEND_TIMEOUT: 10000,

  // Polling configuration
  POLL_INTERVAL: 10000,

  // Cache configuration
  CACHE_TTL: 3600000, // 1 hour

  // Circuit breaker configuration
  ERROR_THRESHOLDS: {
    websocket: 3,
    api: 5,
    send: 3
  },
  CIRCUIT_BREAKER_RESET_TIMEOUT: 300000, // 5 minutes

  // Message queue configuration
  MAX_RETRY_ATTEMPTS: 5,
  INITIAL_RETRY_DELAY: 5000,
  MAX_RETRY_DELAY: 300000, // 5 minutes
  RETRY_BACKOFF_FACTOR: 2,

  // Notification configuration
  NOTIFICATION_AUTO_CLOSE: 5000,

  // Enhancement configurations
  METRICS_ENABLED: true,
  ENCRYPTION_ENABLED: process.env.NODE_ENV === 'production' || process.env.REACT_APP_ENCRYPTION_ENABLED === 'true',
  DEDUPLICATION_ENABLED: true,
  DEDUPLICATION_WINDOW_MS: 30000, // 30 seconds

  // Environment
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production'
};

// Enhanced logging utility
const logger = {
  debug: (message, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.log(`[MessagingService] ${message}`, ...args);
    }
  },
  info: (message, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.info(`[MessagingService] ${message}`, ...args);
    }
  },
  warn: (message, ...args) => {
    console.warn(`[MessagingService] ${message}`, ...args);
  },
  error: (message, error, ...args) => {
    console.error(`[MessagingService] ${message}`, error, ...args);

    // In production, you might want to send errors to a logging service
    if (CONFIG.IS_PRODUCTION && window.analytics) {
      window.analytics.track('Messaging Service Error', {
        message,
        error: error?.message || error,
        stack: error?.stack,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Error message templates with actionable information
const ERROR_MESSAGES = {
  CONNECTION_LOST: {
    title: 'Connection Lost',
    message: 'Your connection to the messaging server has been lost. Messages will be queued and sent when connection is restored.',
    action: 'Reconnecting automatically...',
    type: 'warning'
  },
  MESSAGE_SEND_FAILED: {
    title: 'Message Not Sent',
    message: 'Your message could not be sent at this time.',
    action: 'We\'ll automatically retry sending it when possible.',
    type: 'error'
  },
  WEBSOCKET_ERROR: {
    title: 'Real-time Updates Unavailable',
    message: 'Unable to establish real-time connection. Falling back to regular updates.',
    action: 'Click to try reconnecting now',
    type: 'warning'
  },
  API_ERROR: {
    title: 'Service Temporarily Unavailable',
    message: 'We\'re having trouble reaching the messaging service.',
    action: 'Using cached data. Will automatically reconnect when available.',
    type: 'error'
  },
  RATE_LIMIT: {
    title: 'Rate Limit Reached',
    message: 'You\'ve sent too many messages in a short period.',
    action: 'Please wait a moment before sending more messages.',
    type: 'warning'
  },
  ATTACHMENT_ERROR: {
    title: 'Attachment Error',
    message: 'There was a problem with your attachment.',
    action: 'Please try a different file or format.',
    type: 'error'
  },
  SYNC_ERROR: {
    title: 'Sync Error',
    message: 'Unable to sync with social media platform.',
    action: 'We\'ll automatically retry. Click to try manually.',
    type: 'warning'
  }
};

class EnhancedMessagingService {
  constructor() {
    // WebSocket connection
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = CONFIG.WS_RECONNECT_ATTEMPTS;
    this.reconnectInterval = CONFIG.WS_INITIAL_RECONNECT_INTERVAL;
    this.reconnectTimeoutId = null;
    this.messageQueue = [];
    this.pendingMessages = new Map(); // Map of message IDs to pending messages
    this.listeners = new Map(); // Map of event types to listener arrays
    this.useWebSocket = true; // Whether to use WebSocket or fallback to REST
    this.lastMessageTimestamp = null;
    this.isPolling = false;
    this.pollInterval = CONFIG.POLL_INTERVAL;
    this.pollTimeoutId = null;
    this.localCache = new Map(); // Cache for conversations and messages
    this.cacheTTL = CONFIG.CACHE_TTL;
    this.errorCounts = {}; // Count of errors by type
    this.errorThresholds = CONFIG.ERROR_THRESHOLDS;
    this.circuitBreakers = {
      websocket: { open: false, resetTime: null },
      api: { open: false, resetTime: null },
      send: { open: false, resetTime: null }
    };
    this.circuitBreakerResetTimeout = CONFIG.CIRCUIT_BREAKER_RESET_TIMEOUT;

    // Initialize enhancement services
    this.metricsCollector = null;
    this.encryptionService = null;
    this.deduplicationService = null;
    this.correlationId = this.generateCorrelationId();
    this.subscriptionTier = 'creator'; // Default tier, will be updated from user context

    // Bind methods
    this.connect = this.connect.bind(this);
    this.disconnect = this.disconnect.bind(this);
    this.reconnect = this.reconnect.bind(this);
    this.sendMessage = this.sendMessage.bind(this);
    this.startPolling = this.startPolling.bind(this);
    this.stopPolling = this.stopPolling.bind(this);
    this.handleNetworkChange = this.handleNetworkChange.bind(this);
    this.showErrorNotification = this.showErrorNotification.bind(this);
    this.generateCorrelationId = this.generateCorrelationId.bind(this);

    // Initialize enhancement services
    this.initializeEnhancementServices();

    // Initialize network monitoring
    this.initNetworkMonitoring();
  }

  /**
   * Initialize enhancement services (Prometheus metrics, encryption, deduplication)
   *
   * @private
   */
  initializeEnhancementServices() {
    try {
      // Initialize Prometheus metrics collector
      if (CONFIG.METRICS_ENABLED) {
        this.metricsCollector = new PrometheusMetricsCollector({
          enabled: true,
          endpoint: '/api/metrics/messaging',
          batchSize: 50,
          flushInterval: 30000
        });

        logger.info('Prometheus metrics collector initialized');
      }

      // Initialize message encryption service
      if (CONFIG.ENCRYPTION_ENABLED) {
        this.encryptionService = new MessageEncryptionService({
          enabled: true,
          algorithm: 'AES-256-GCM',
          keyRotationInterval: 86400000 // 24 hours
        });

        logger.info('Message encryption service initialized');
      }

      // Initialize message deduplication service
      if (CONFIG.DEDUPLICATION_ENABLED) {
        this.deduplicationService = new MessageDeduplicationService({
          enabled: true,
          windowMs: CONFIG.DEDUPLICATION_WINDOW_MS,
          maxCacheSize: 1000,
          useRedis: true
        });

        logger.info('Message deduplication service initialized');
      }

    } catch (error) {
      logger.error('Error initializing enhancement services:', error);

      // Record initialization error in metrics if available
      if (this.metricsCollector) {
        this.metricsCollector.recordError('service_initialization', error.message, {
          correlation_id: this.correlationId
        });
      }
    }
  }

  /**
   * Generate correlation ID for request tracking
   *
   * @returns {string} Unique correlation ID
   */
  generateCorrelationId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Set user context for subscription tier tracking
   *
   * @param {Object} user - User object with subscription information
   */
  setUserContext(user) {
    if (user && user.subscription) {
      this.subscriptionTier = user.subscription.plan || 'creator';

      // Update metrics collector with user context
      if (this.metricsCollector) {
        this.metricsCollector.setUserContext(user);
      }

      logger.debug('User context updated:', {
        user_id: user.id,
        subscription_tier: this.subscriptionTier
      });
    }
  }

  /**
   * Initialize network monitoring
   */
  initNetworkMonitoring() {
    networkMonitor.addEventListener('statusChange', this.handleNetworkChange);
    networkMonitor.addEventListener('reconnect', () => {
      this.reconnect();
      this.processMessageQueue();
    });
    networkMonitor.startMonitoring();
  }

  /**
   * Handle network status changes
   * 
   * @param {Object} data - Network status data
   */
  handleNetworkChange(data) {
    if (data.isOnline) {
      // Network is back online
      if (!this.isConnected && this.useWebSocket) {
        this.reconnect();
      }
      this.processMessageQueue();
    } else {
      // Network is offline
      if (this.isConnected) {
        this.disconnect();
      }
      this.stopPolling();
      
      // Show offline notification
      this.showErrorNotification(ERROR_MESSAGES.CONNECTION_LOST);
    }
  }

  /**
   * Connect to the WebSocket server
   *
   * @returns {Promise<boolean>} - Whether connection was successful
   */
  async connect() {
    const startTime = Date.now();
    const connectionId = this.generateCorrelationId();

    // Record connection attempt
    if (this.metricsCollector) {
      this.metricsCollector.recordWebSocketConnection('attempt', this.subscriptionTier, {
        correlation_id: connectionId
      });
    }

    // Check if circuit breaker is open
    if (this.isCircuitBreakerOpen('websocket')) {
      logger.info('WebSocket circuit breaker is open, using REST fallback');

      // Record circuit breaker block
      if (this.metricsCollector) {
        this.metricsCollector.recordWebSocketConnection('circuit_breaker_blocked', this.subscriptionTier, {
          correlation_id: connectionId,
          duration: Date.now() - startTime
        });
      }

      this.useWebSocket = false;
      this.startPolling();
      return false;
    }

    try {
      // Get authentication token using AuthService
      const token = getToken();
      if (!token) {
        logger.error('No authentication token found');

        // Record authentication failure
        if (this.metricsCollector) {
          this.metricsCollector.recordWebSocketConnection('auth_failed', this.subscriptionTier, {
            correlation_id: connectionId,
            duration: Date.now() - startTime
          });
        }

        return false;
      }
      
      // Create WebSocket connection
      const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/api/messaging/ws?token=${token}`;
      this.socket = new WebSocket(wsUrl);
      
      // Set up event handlers
      this.socket.onopen = () => {
        const connectionDuration = Date.now() - startTime;

        logger.info('WebSocket connection established', {
          correlation_id: connectionId,
          duration: connectionDuration
        });

        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.reconnectInterval = CONFIG.WS_INITIAL_RECONNECT_INTERVAL; // Reset reconnect interval
        this.useWebSocket = true;
        this.resetCircuitBreaker('websocket');

        // Record successful connection
        if (this.metricsCollector) {
          this.metricsCollector.recordWebSocketConnection('success', this.subscriptionTier, {
            correlation_id: connectionId,
            duration: connectionDuration,
            reconnect_attempts: this.reconnectAttempts
          });
        }

        this.processMessageQueue();
        this.emit('connected');
      };

      this.socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleWebSocketMessage(data);
        } catch (error) {
          logger.error('Error parsing WebSocket message:', error);
        }
      };

      this.socket.onclose = (event) => {
        logger.info(`WebSocket connection closed: ${event.code} ${event.reason}`);
        this.isConnected = false;
        this.emit('disconnected');

        // Attempt to reconnect unless explicitly closed
        if (this.useWebSocket) {
          this.incrementErrorCount('websocket');
          this.scheduleReconnect();
        }
      };

      this.socket.onerror = (error) => {
        logger.error('WebSocket error:', error);
        this.incrementErrorCount('websocket');

        // Show error notification
        this.showErrorNotification(ERROR_MESSAGES.WEBSOCKET_ERROR);
      };
      
      return true;
    } catch (error) {
      logger.error('Error connecting to WebSocket server:', error);
      this.incrementErrorCount('websocket');
      this.showErrorNotification(ERROR_MESSAGES.WEBSOCKET_ERROR);

      // Fall back to REST API
      if (networkMonitor.isOnline) {
        this.useWebSocket = false;
        this.startPolling();
      }

      return false;
    }
  }

  /**
   * Disconnect from the WebSocket server
   */
  disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    
    this.isConnected = false;
    
    // Clear any pending reconnect
    if (this.reconnectTimeoutId) {
      clearTimeout(this.reconnectTimeoutId);
      this.reconnectTimeoutId = null;
    }
  }

  /**
   * Schedule a reconnection attempt with exponential backoff
   */
  scheduleReconnect() {
    // Clear any existing reconnect timeout
    if (this.reconnectTimeoutId) {
      clearTimeout(this.reconnectTimeoutId);
    }
    
    // Check if we've exceeded max reconnect attempts
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.info('Maximum reconnect attempts reached, falling back to REST API');
      this.useWebSocket = false;
      this.openCircuitBreaker('websocket');
      this.startPolling();
      return;
    }

    // Calculate backoff time with jitter
    const backoffTime = Math.min(
      this.reconnectInterval * Math.pow(CONFIG.WS_RECONNECT_BACKOFF_FACTOR, this.reconnectAttempts),
      CONFIG.WS_MAX_RECONNECT_INTERVAL
    );
    const jitter = Math.random() * 1000;
    const reconnectTime = backoffTime + jitter;

    logger.debug(`Scheduling reconnect in ${Math.round(reconnectTime / 1000)} seconds`);
    
    // Schedule reconnect
    this.reconnectTimeoutId = setTimeout(() => {
      this.reconnect();
    }, reconnectTime);
    
    // Increment reconnect attempts
    this.reconnectAttempts++;
  }

  /**
   * Reconnect to the WebSocket server
   */
  reconnect() {
    // Only reconnect if we're using WebSocket and not already connected
    if (this.useWebSocket && !this.isConnected) {
      logger.debug('Attempting to reconnect to WebSocket server');
      this.connect();
    }
  }

  /**
   * Start polling for updates (fallback when WebSocket is unavailable)
   */
  startPolling() {
    if (this.isPolling) {
      return;
    }

    logger.info('Starting polling for updates');
    this.isPolling = true;
    
    // Define polling function
    const poll = async () => {
      if (!this.isPolling || !networkMonitor.isOnline) {
        return;
      }
      
      try {
        // Get last message timestamp
        const timestamp = this.lastMessageTimestamp || new Date().toISOString();
        
        // Fetch new messages
        const response = await axios.get(`/api/messaging/messages/since/${timestamp}`);
        
        if (response.data && response.data.messages) {
          // Process new messages
          response.data.messages.forEach(message => {
            this.emit('message', message);
          });
          
          // Update last message timestamp
          if (response.data.messages.length > 0) {
            this.lastMessageTimestamp = response.data.messages[response.data.messages.length - 1].created_at;
          }
        }
        
        // Reset error count for API
        this.resetErrorCount('api');
      } catch (error) {
        logger.error('Error polling for messages:', error);
        this.incrementErrorCount('api');

        // Show error notification if circuit breaker opens
        if (this.isCircuitBreakerOpen('api')) {
          this.showErrorNotification(ERROR_MESSAGES.API_ERROR);
        }
      }
      
      // Schedule next poll if still polling
      if (this.isPolling) {
        this.pollTimeoutId = setTimeout(poll, this.pollInterval);
      }
    };
    
    // Start polling
    poll();
  }

  /**
   * Stop polling for updates
   */
  stopPolling() {
    if (!this.isPolling) {
      return;
    }

    logger.info('Stopping polling for updates');
    this.isPolling = false;
    
    // Clear polling timeout
    if (this.pollTimeoutId) {
      clearTimeout(this.pollTimeoutId);
      this.pollTimeoutId = null;
    }
  }

  /**
   * Handle incoming WebSocket message
   * 
   * @param {Object} data - Message data
   */
  handleWebSocketMessage(data) {
    // Handle different message types
    switch (data.type) {
      case 'new_message':
        this.emit('message', data.message);
        break;
        
      case 'message_updated':
        this.emit('messageUpdated', data.message);
        break;
        
      case 'message_deleted':
        this.emit('messageDeleted', data.message_id);
        break;
        
      case 'conversation_updated':
        this.emit('conversationUpdated', data.conversation);
        break;
        
      case 'typing_indicator':
        this.emit('typing', {
          conversation_id: data.conversation_id,
          user_id: data.user_id,
          is_typing: data.is_typing
        });
        break;
        
      case 'error':
        logger.error('WebSocket error message:', data.error);
        this.showErrorNotification({
          title: 'Error',
          message: data.error.message || 'An error occurred',
          type: 'error'
        });
        break;

      default:
        logger.warn('Unknown WebSocket message type:', data.type);
    }
  }

  /**
   * Send a message with encryption and deduplication
   *
   * @param {Object} messageData - Message data
   * @returns {Promise<Object>} - Sent message
   */
  async sendMessage(messageData) {
    const startTime = Date.now();
    const messageCorrelationId = this.generateCorrelationId();

    // Generate temporary ID for the message
    const tempId = `temp-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    // Create message object with temporary ID
    let message = {
      ...messageData,
      id: tempId,
      status: 'sending',
      created_at: new Date().toISOString(),
      correlation_id: messageCorrelationId
    };

    // Record message send attempt
    if (this.metricsCollector) {
      this.metricsCollector.recordMessageSend(
        messageData.platform || 'unknown',
        this.subscriptionTier,
        'attempt',
        0,
        {
          temp_id: tempId,
          correlation_id: messageCorrelationId,
          content_length: messageData.content?.length || 0
        }
      );
    }

    try {
      // Check for duplicate message
      if (CONFIG.DEDUPLICATION_ENABLED && this.deduplicationService) {
        const isDuplicate = await this.deduplicationService.isDuplicate(message);
        if (isDuplicate) {
          logger.debug('Duplicate message detected:', {
            temp_id: tempId,
            correlation_id: messageCorrelationId
          });

          // Record deduplication block
          if (this.metricsCollector) {
            this.metricsCollector.recordMessageSend(
              messageData.platform || 'unknown',
              this.subscriptionTier,
              'duplicate_blocked',
              Date.now() - startTime,
              {
                temp_id: tempId,
                correlation_id: messageCorrelationId,
                reason: 'deduplication'
              }
            );
          }

          return {
            ...message,
            status: 'duplicate',
            duplicate_detected: true
          };
        }
      }

      // Encrypt message content if encryption is enabled
      if (CONFIG.ENCRYPTION_ENABLED && this.encryptionService) {
        try {
          message = await this.encryptionService.encryptMessage(message);

          logger.debug('Message encrypted:', {
            temp_id: tempId,
            correlation_id: messageCorrelationId,
            encryption_level: message._encryption?.encryption_level
          });

        } catch (encryptionError) {
          logger.error('Message encryption failed:', encryptionError, {
            temp_id: tempId,
            correlation_id: messageCorrelationId
          });

          // Record encryption failure but continue with unencrypted message
          if (this.metricsCollector) {
            this.metricsCollector.recordError('encryption_failed', encryptionError.message, {
              temp_id: tempId,
              correlation_id: messageCorrelationId
            });
          }
        }
      }

    } catch (preprocessingError) {
      logger.error('Message preprocessing failed:', preprocessingError, {
        temp_id: tempId,
        correlation_id: messageCorrelationId
      });

      // Record preprocessing failure
      if (this.metricsCollector) {
        this.metricsCollector.recordMessageSend(
          messageData.platform || 'unknown',
          this.subscriptionTier,
          'preprocessing_failed',
          Date.now() - startTime,
          {
            temp_id: tempId,
            correlation_id: messageCorrelationId,
            error: preprocessingError.message
          }
        );
      }
    }
    
    // Add to pending messages
    this.pendingMessages.set(tempId, message);

    // Mark message as sent in deduplication service
    if (CONFIG.DEDUPLICATION_ENABLED && this.deduplicationService) {
      try {
        await this.deduplicationService.markAsSent(message);
      } catch (deduplicationError) {
        logger.warn('Failed to mark message as sent in deduplication service:', deduplicationError);
      }
    }

    // Emit message event with temporary message
    this.emit('messageSending', message);

    // Check if we're online
    if (!networkMonitor.isOnline) {
      // Queue message for later
      this.queueMessage(message);

      // Record offline queue
      if (this.metricsCollector) {
        this.metricsCollector.recordMessageSend(
          messageData.platform || 'unknown',
          this.subscriptionTier,
          'queued_offline',
          Date.now() - startTime,
          {
            temp_id: tempId,
            correlation_id: messageCorrelationId
          }
        );
      }

      // Show notification
      this.showErrorNotification(ERROR_MESSAGES.CONNECTION_LOST);

      return message;
    }

    // Check if circuit breaker is open
    if (this.isCircuitBreakerOpen('send')) {
      // Queue message for later
      this.queueMessage(message);

      // Record circuit breaker block
      if (this.metricsCollector) {
        this.metricsCollector.recordMessageSend(
          messageData.platform || 'unknown',
          this.subscriptionTier,
          'circuit_breaker_blocked',
          Date.now() - startTime,
          {
            temp_id: tempId,
            correlation_id: messageCorrelationId
          }
        );
      }

      // Show notification
      this.showErrorNotification(ERROR_MESSAGES.MESSAGE_SEND_FAILED);

      return message;
    }
    
    try {
      let sentMessage;
      
      // Try to send via WebSocket if connected
      if (this.isConnected && this.useWebSocket) {
        sentMessage = await this.sendMessageViaWebSocket(message);
      } else {
        // Fall back to REST API
        sentMessage = await this.sendMessageViaRest(message);
      }
      
      // Remove from pending messages
      this.pendingMessages.delete(tempId);

      // Reset error count
      this.resetErrorCount('send');

      // Record successful send
      if (this.metricsCollector) {
        this.metricsCollector.recordMessageSend(
          messageData.platform || 'unknown',
          this.subscriptionTier,
          'success',
          Date.now() - startTime,
          {
            temp_id: tempId,
            correlation_id: messageCorrelationId,
            message_id: sentMessage.id,
            content_length: messageData.content?.length || 0,
            encrypted: !!message._encryption
          }
        );
      }

      // Emit message sent event
      this.emit('messageSent', {
        tempId,
        message: sentMessage
      });

      return sentMessage;
    } catch (error) {
      logger.error('Error sending message:', error, {
        temp_id: tempId,
        correlation_id: messageCorrelationId
      });

      // Increment error count
      this.incrementErrorCount('send');

      // Record send failure
      if (this.metricsCollector) {
        this.metricsCollector.recordMessageSend(
          messageData.platform || 'unknown',
          this.subscriptionTier,
          'failed',
          Date.now() - startTime,
          {
            temp_id: tempId,
            correlation_id: messageCorrelationId,
            error: error.message,
            error_code: error.code || 'unknown',
            content_length: messageData.content?.length || 0
          }
        );
      }

      // Update message status
      message.status = 'failed';
      message.error = error.message;
      this.pendingMessages.set(tempId, message);

      // Emit message failed event
      this.emit('messageFailed', {
        tempId,
        error,
        correlationId: messageCorrelationId
      });

      // Queue message for retry
      this.queueMessage(message);

      // Show error notification
      this.showErrorNotification(ERROR_MESSAGES.MESSAGE_SEND_FAILED);

      return message;
    }
  }

  /**
   * Send a message via WebSocket
   * 
   * @param {Object} message - Message to send
   * @returns {Promise<Object>} - Sent message
   */
  sendMessageViaWebSocket(message) {
    return new Promise((resolve, reject) => {
      // Set timeout for acknowledgment
      const timeoutId = setTimeout(() => {
        reject(new Error('WebSocket send timeout'));
      }, CONFIG.WS_SEND_TIMEOUT);

      // Create message ID for acknowledgment
      const messageId = `msg-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      
      // Create one-time listener for acknowledgment
      const listener = (data) => {
        if (data.message_id === messageId) {
          clearTimeout(timeoutId);
          this.removeListener('messageSent', listener);
          resolve(data.message);
        }
      };
      
      // Add listener
      this.addListener('messageSent', listener);
      
      // Send message
      try {
        this.socket.send(JSON.stringify({
          type: 'new_message',
          message_id: messageId,
          data: {
            conversation_id: message.conversation_id,
            content: message.content,
            attachments: message.attachments || [],
            metadata: message.metadata || {}
          }
        }));
      } catch (error) {
        clearTimeout(timeoutId);
        this.removeListener('messageSent', listener);
        reject(error);
      }
    });
  }

  /**
   * Send a message via REST API
   * 
   * @param {Object} message - Message to send
   * @returns {Promise<Object>} - Sent message
   */
  async sendMessageViaRest(message) {
    try {
      const response = await axios.post('/api/messaging/messages', {
        conversation_id: message.conversation_id,
        content: message.content,
        attachments: message.attachments || [],
        metadata: message.metadata || {}
      });
      
      return response.data;
    } catch (error) {
      // Check for specific error types
      if (error.response) {
        if (error.response.status === 429) {
          // Rate limit error
          this.showErrorNotification(ERROR_MESSAGES.RATE_LIMIT);
        } else if (error.response.status === 413) {
          // Payload too large (likely attachment issue)
          this.showErrorNotification(ERROR_MESSAGES.ATTACHMENT_ERROR);
        }
      }
      
      throw error;
    }
  }

  /**
   * Queue a message for later sending
   * 
   * @param {Object} message - Message to queue
   */
  queueMessage(message) {
    // Add to queue if not already there
    if (!this.messageQueue.some(m => m.id === message.id)) {
      this.messageQueue.push({
        ...message,
        retryCount: 0,
        nextRetryTime: Date.now() + CONFIG.INITIAL_RETRY_DELAY
      });

      logger.debug(`Message queued for later sending: ${message.id}`);
    }
  }

  /**
   * Process the message queue with metrics tracking
   */
  async processMessageQueue() {
    if (!networkMonitor.isOnline || this.messageQueue.length === 0) {
      return;
    }

    const queueProcessingStart = Date.now();
    const queueSize = this.messageQueue.length;

    logger.debug(`Processing message queue (${queueSize} messages)`, {
      correlation_id: this.correlationId
    });

    // Record queue processing start
    if (this.metricsCollector) {
      this.metricsCollector.recordQueueProcessing('start', this.subscriptionTier, {
        queue_size: queueSize,
        correlation_id: this.correlationId
      });
    }

    // Get messages that are ready to be sent
    const currentTime = Date.now();
    const readyMessages = this.messageQueue.filter(m => m.nextRetryTime <= currentTime);
    
    // Process each message
    for (const queuedMessage of readyMessages) {
      try {
        // Remove from queue
        this.messageQueue = this.messageQueue.filter(m => m.id !== queuedMessage.id);
        
        // Update status
        queuedMessage.status = 'sending';
        this.emit('messageStatusChanged', queuedMessage);
        
        // Send message
        let sentMessage;
        if (this.isConnected && this.useWebSocket) {
          sentMessage = await this.sendMessageViaWebSocket(queuedMessage);
        } else {
          sentMessage = await this.sendMessageViaRest(queuedMessage);
        }
        
        // Remove from pending messages
        this.pendingMessages.delete(queuedMessage.id);
        
        // Emit message sent event
        this.emit('messageSent', {
          tempId: queuedMessage.id,
          message: sentMessage
        });
      } catch (error) {
        logger.error(`Error sending queued message ${queuedMessage.id}:`, error);

        // Increment retry count
        queuedMessage.retryCount = (queuedMessage.retryCount || 0) + 1;

        // Calculate next retry time with exponential backoff
        const backoffTime = Math.min(
          CONFIG.INITIAL_RETRY_DELAY * Math.pow(CONFIG.RETRY_BACKOFF_FACTOR, queuedMessage.retryCount),
          CONFIG.MAX_RETRY_DELAY
        );
        const jitter = Math.random() * 2000; // Add jitter
        queuedMessage.nextRetryTime = Date.now() + backoffTime + jitter;

        // If we haven't reached max retries, add back to queue
        if (queuedMessage.retryCount < CONFIG.MAX_RETRY_ATTEMPTS) {
          this.messageQueue.push(queuedMessage);
        } else {
          // Mark as permanently failed
          queuedMessage.status = 'failed';
          this.emit('messageStatusChanged', queuedMessage);
          
          // Show error notification
          this.showErrorNotification({
            ...ERROR_MESSAGES.MESSAGE_SEND_FAILED,
            message: 'Message could not be sent after multiple attempts.'
          });
        }
      }
    }

    // Record queue processing completion
    if (this.metricsCollector) {
      const processingDuration = Date.now() - queueProcessingStart;
      const processedCount = queueSize - this.messageQueue.length;

      this.metricsCollector.recordQueueProcessing('complete', this.subscriptionTier, {
        initial_queue_size: queueSize,
        processed_count: processedCount,
        remaining_count: this.messageQueue.length,
        processing_duration: processingDuration,
        correlation_id: this.correlationId
      });
    }
  }

  /**
   * Increment error count for a specific type
   * 
   * @param {string} type - Error type
   */
  incrementErrorCount(type) {
    this.errorCounts[type] = (this.errorCounts[type] || 0) + 1;
    
    // Check if we should open circuit breaker
    if (this.errorCounts[type] >= this.errorThresholds[type]) {
      this.openCircuitBreaker(type);
    }
  }

  /**
   * Reset error count for a specific type
   * 
   * @param {string} type - Error type
   */
  resetErrorCount(type) {
    this.errorCounts[type] = 0;
  }

  /**
   * Open circuit breaker for a specific type
   *
   * @param {string} type - Circuit breaker type
   */
  openCircuitBreaker(type) {
    logger.info(`Opening circuit breaker for ${type}`, {
      correlation_id: this.correlationId,
      error_count: this.errorCounts[type] || 0
    });

    this.circuitBreakers[type] = {
      open: true,
      resetTime: Date.now() + this.circuitBreakerResetTimeout
    };

    // Record circuit breaker opening
    if (this.metricsCollector) {
      this.metricsCollector.recordCircuitBreakerState(type, 'opened', this.subscriptionTier, {
        error_count: this.errorCounts[type] || 0,
        reset_timeout: this.circuitBreakerResetTimeout,
        correlation_id: this.correlationId
      });
    }

    // Schedule circuit breaker reset
    setTimeout(() => {
      this.resetCircuitBreaker(type);
    }, this.circuitBreakerResetTimeout);
  }

  /**
   * Reset circuit breaker for a specific type
   *
   * @param {string} type - Circuit breaker type
   */
  resetCircuitBreaker(type) {
    logger.info(`Resetting circuit breaker for ${type}`, {
      correlation_id: this.correlationId
    });

    this.circuitBreakers[type] = {
      open: false,
      resetTime: null
    };

    this.resetErrorCount(type);

    // Record circuit breaker reset
    if (this.metricsCollector) {
      this.metricsCollector.recordCircuitBreakerState(type, 'reset', this.subscriptionTier, {
        correlation_id: this.correlationId
      });
    }

    // If websocket circuit breaker is reset, try to reconnect
    if (type === 'websocket') {
      this.useWebSocket = true;
      this.reconnect();
    }
  }

  /**
   * Check if circuit breaker is open for a specific type
   * 
   * @param {string} type - Circuit breaker type
   * @returns {boolean} - Whether circuit breaker is open
   */
  isCircuitBreakerOpen(type) {
    return this.circuitBreakers[type]?.open || false;
  }

  /**
   * Show error notification
   * 
   * @param {Object} errorInfo - Error information
   */
  showErrorNotification(errorInfo) {
    // Use toast for notifications
    toast.error(
      <div>
        <strong>{errorInfo.title}</strong>
        <p>{errorInfo.message}</p>
        <p className="action-text">{errorInfo.action}</p>
      </div>,
      {
        autoClose: CONFIG.NOTIFICATION_AUTO_CLOSE,
        closeOnClick: true,
        pauseOnHover: true
      }
    );
  }

  /**
   * Add event listener
   * 
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  addListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    
    this.listeners.get(event).push(callback);
  }

  /**
   * Remove event listener
   * 
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  removeListener(event, callback) {
    if (!this.listeners.has(event)) {
      return;
    }
    
    const callbacks = this.listeners.get(event);
    this.listeners.set(event, callbacks.filter(cb => cb !== callback));
  }

  /**
   * Emit event to all listeners
   * 
   * @param {string} event - Event name
   * @param {any} data - Event data
   */
  emit(event, data) {
    if (!this.listeners.has(event)) {
      return;
    }
    
    const callbacks = this.listeners.get(event);
    callbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        logger.error(`Error in ${event} listener:`, error);
      }
    });
  }

  /**
   * Get comprehensive service statistics and health information
   *
   * @returns {Object} Service statistics
   */
  getServiceStats() {
    const stats = {
      // Connection status
      connection: {
        isConnected: this.isConnected,
        useWebSocket: this.useWebSocket,
        reconnectAttempts: this.reconnectAttempts,
        isPolling: this.isPolling
      },

      // Circuit breaker status
      circuitBreakers: Object.keys(this.circuitBreakers).reduce((acc, type) => {
        acc[type] = {
          isOpen: this.circuitBreakers[type].open,
          resetTime: this.circuitBreakers[type].resetTime,
          errorCount: this.errorCounts[type] || 0
        };
        return acc;
      }, {}),

      // Queue status
      queue: {
        size: this.messageQueue.length,
        pendingMessages: this.pendingMessages.size
      },

      // Enhancement services status
      enhancements: {
        metricsEnabled: !!this.metricsCollector,
        encryptionEnabled: !!this.encryptionService,
        deduplicationEnabled: !!this.deduplicationService
      },

      // User context
      userContext: {
        subscriptionTier: this.subscriptionTier,
        correlationId: this.correlationId
      },

      // Network status
      network: {
        isOnline: networkMonitor.isOnline,
        connectionQuality: networkMonitor.connectionQuality
      }
    };

    // Add metrics collector stats if available
    if (this.metricsCollector) {
      stats.metrics = this.metricsCollector.getStats();
    }

    // Add deduplication stats if available
    if (this.deduplicationService) {
      stats.deduplication = this.deduplicationService.getStats();
    }

    return stats;
  }

  /**
   * Cleanup method for service shutdown
   */
  cleanup() {
    // Disconnect WebSocket
    this.disconnect();

    // Stop polling
    this.stopPolling();

    // Clear message queue
    this.messageQueue = [];
    this.pendingMessages.clear();

    // Clear listeners
    this.listeners.clear();

    // Cleanup enhancement services
    if (this.metricsCollector) {
      this.metricsCollector.flush();
    }

    if (this.deduplicationService) {
      this.deduplicationService.cleanup();
    }

    logger.info('Enhanced messaging service cleaned up');
  }
}

// Create singleton instance
const enhancedMessagingService = new EnhancedMessagingService();

export default enhancedMessagingService;
