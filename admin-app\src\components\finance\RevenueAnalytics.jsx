import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  CircularProgress,
  Alert,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { format, subMonths, startOfMonth, endOfMonth } from 'date-fns';

// API
import api from '../../api';

const RevenueAnalytics = ({ dateRange, onDateRangeChange }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [metrics, setMetrics] = useState(null);
  const [timeframe, setTimeframe] = useState('monthly');

  useEffect(() => {
    if (dateRange.start && dateRange.end) {
      loadRevenueMetrics();
    }
  }, [dateRange]);

  const loadRevenueMetrics = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.get('/api/admin/finance/revenue/metrics', {
        params: {
          period_start: dateRange.start.toISOString(),
          period_end: dateRange.end.toISOString(),
        },
      });

      setMetrics(response.data);
    } catch (err) {
      console.error('Error loading revenue metrics:', err);
      setError(err.response?.data?.detail || 'Failed to load revenue metrics');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const handleDateRangeChange = (field, value) => {
    onDateRangeChange({
      ...dateRange,
      [field]: value,
    });
  };

  const setQuickRange = (months) => {
    const end = endOfMonth(new Date());
    const start = startOfMonth(subMonths(end, months - 1));
    onDateRangeChange({ start, end });
  };

  return (
    <Box>
      {/* Controls */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <DatePicker
            label="Start Date"
            value={dateRange.start}
            onChange={(value) => handleDateRangeChange('start', value)}
            slotProps={{ textField: { fullWidth: true, size: 'small' } }}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <DatePicker
            label="End Date"
            value={dateRange.end}
            onChange={(value) => handleDateRangeChange('end', value)}
            slotProps={{ textField: { fullWidth: true, size: 'small' } }}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel>Timeframe</InputLabel>
            <Select
              value={timeframe}
              label="Timeframe"
              onChange={(e) => setTimeframe(e.target.value)}
            >
              <MenuItem value="daily">Daily</MenuItem>
              <MenuItem value="weekly">Weekly</MenuItem>
              <MenuItem value="monthly">Monthly</MenuItem>
              <MenuItem value="quarterly">Quarterly</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Box display="flex" gap={1}>
            <Button size="small" onClick={() => setQuickRange(3)}>
              3M
            </Button>
            <Button size="small" onClick={() => setQuickRange(6)}>
              6M
            </Button>
            <Button size="small" onClick={() => setQuickRange(12)}>
              1Y
            </Button>
          </Box>
        </Grid>
      </Grid>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      ) : metrics ? (
        <Grid container spacing={3}>
          {/* Revenue Summary Cards */}
          <Grid item xs={12} md={6} lg={3}>
            <Card>
              <CardContent>
                <Typography color="text.secondary" gutterBottom variant="body2">
                  Total Revenue
                </Typography>
                <Typography variant="h4" component="div">
                  {formatCurrency(metrics.total_revenue)}
                </Typography>
                {metrics.growth_rate !== null && (
                  <Typography 
                    variant="body2" 
                    color={metrics.growth_rate >= 0 ? "success.main" : "error.main"}
                  >
                    {metrics.growth_rate >= 0 ? '+' : ''}{formatPercentage(metrics.growth_rate)} vs previous period
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6} lg={3}>
            <Card>
              <CardContent>
                <Typography color="text.secondary" gutterBottom variant="body2">
                  Subscription Revenue
                </Typography>
                <Typography variant="h4" component="div">
                  {formatCurrency(metrics.subscription_revenue)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {((metrics.subscription_revenue / metrics.total_revenue) * 100).toFixed(1)}% of total
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6} lg={3}>
            <Card>
              <CardContent>
                <Typography color="text.secondary" gutterBottom variant="body2">
                  Add-on Revenue
                </Typography>
                <Typography variant="h4" component="div">
                  {formatCurrency(metrics.addon_revenue)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {((metrics.addon_revenue / metrics.total_revenue) * 100).toFixed(1)}% of total
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6} lg={3}>
            <Card>
              <CardContent>
                <Typography color="text.secondary" gutterBottom variant="body2">
                  Average Transaction
                </Typography>
                <Typography variant="h4" component="div">
                  {formatCurrency(metrics.average_transaction_value)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {metrics.transaction_count} transactions
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Revenue Breakdown Chart */}
          <Grid item xs={12} lg={8}>
            <Card>
              <CardHeader title="Revenue Breakdown" />
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart
                    data={[
                      { name: 'Subscription', value: metrics.subscription_revenue },
                      { name: 'Add-ons', value: metrics.addon_revenue },
                      { name: 'AppSumo', value: metrics.appsumo_revenue },
                      { name: 'Upgrades', value: metrics.upgrade_revenue },
                    ]}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`} />
                    <Tooltip formatter={(value) => formatCurrency(value)} />
                    <Bar dataKey="value" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Monthly Revenue by Plan */}
          <Grid item xs={12} lg={4}>
            <Card>
              <CardHeader title="Revenue by Plan" />
              <CardContent>
                <Box>
                  {Object.entries(metrics.revenue_by_plan || {}).map(([plan, revenue]) => (
                    <Box key={plan} display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                      <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                        {plan}
                      </Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {formatCurrency(revenue)}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Key Metrics */}
          <Grid item xs={12}>
            <Card>
              <CardHeader title="Key Metrics" />
              <CardContent>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h5" color="primary">
                        {formatCurrency(metrics.mrr)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Monthly Recurring Revenue
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h5" color="primary">
                        {formatCurrency(metrics.arr)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Annual Recurring Revenue
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h5" color="primary">
                        {formatPercentage(metrics.successful_transactions / metrics.transaction_count)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Success Rate
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h5" color="primary">
                        {formatCurrency(metrics.refund_amount)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Refunds
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      ) : (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <Typography variant="body1" color="text.secondary">
            Select a date range to view revenue analytics
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default RevenueAnalytics;
