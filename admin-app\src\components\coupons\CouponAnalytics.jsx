import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
  Alert,
  Skeleton,
  useTheme,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Analytics as AnalyticsIcon,
  LocalOffer as CouponIcon,
  Redeem as RedeemIcon,
  AttachMoney as MoneyIcon,
  Timeline as TimelineIcon,
  Star as StarIcon,
} from '@mui/icons-material';
import { 
  calculateCouponMetrics, 
  formatCurrency, 
  formatDate, 
  formatDiscount,
  getCouponStatus,
  exportToCSV 
} from '../../utils/couponHelpers';

/**
 * Coupon Analytics Dashboard Component
 * Displays real-time analytics with charts and statistics
 */
const CouponAnalytics = ({ 
  data, 
  loading, 
  error, 
  onRefresh,
  className,
  ...props 
}) => {
  const theme = useTheme();
  const [refreshing, setRefreshing] = useState(false);

  // Calculate analytics from data
  const analytics = useMemo(() => {
    if (!data?.coupons || !Array.isArray(data.coupons)) {
      return null;
    }

    const metrics = calculateCouponMetrics(data.coupons);
    
    // Calculate trends (comparing last 7 days vs previous 7 days)
    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const fourteenDaysAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);
    
    const recentCoupons = data.coupons.filter(coupon => {
      const createdAt = new Date(coupon.created_at);
      return createdAt >= sevenDaysAgo;
    });
    
    const previousCoupons = data.coupons.filter(coupon => {
      const createdAt = new Date(coupon.created_at);
      return createdAt >= fourteenDaysAgo && createdAt < sevenDaysAgo;
    });
    
    const weeklyTrend = recentCoupons.length - previousCoupons.length;
    const weeklyTrendPercent = previousCoupons.length > 0 ? 
      ((recentCoupons.length - previousCoupons.length) / previousCoupons.length) * 100 : 0;

    // Top performing coupons
    const topPerforming = [...data.coupons]
      .sort((a, b) => (b.redemption_count || 0) - (a.redemption_count || 0))
      .slice(0, 5);

    return {
      ...metrics,
      weeklyNewCoupons: recentCoupons.length,
      weeklyTrend,
      weeklyTrendPercent,
      topPerforming,
    };
  }, [data]);

  // Handle refresh
  const handleRefresh = async () => {
    if (onRefresh) {
      setRefreshing(true);
      try {
        await onRefresh();
      } finally {
        setRefreshing(false);
      }
    }
  };

  // Handle export
  const handleExport = () => {
    if (!data?.coupons) return;
    
    const exportColumns = [
      { key: 'code', label: 'Code' },
      { key: 'name', label: 'Name' },
      { key: 'discount_type', label: 'Type' },
      { key: 'discount_value', label: 'Value', type: 'discount' },
      { key: 'redemption_count', label: 'Redemptions' },
      { key: 'created_at', label: 'Created At', type: 'date' },
      { key: 'is_active', label: 'Active', type: 'boolean' },
    ];
    
    exportToCSV(data.coupons, `coupon-analytics-${formatDate(new Date())}`, exportColumns);
  };

  // Render loading skeleton
  if (loading && !analytics) {
    return (
      <Box className={className} {...props}>
        <Grid container spacing={3}>
          {[1, 2, 3, 4].map((item) => (
            <Grid item xs={12} sm={6} md={3} key={item}>
              <Card variant="glass">
                <CardContent>
                  <Skeleton variant="text" width="60%" height={24} />
                  <Skeleton variant="text" width="40%" height={32} />
                  <Skeleton variant="text" width="80%" height={20} />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Box className={className} {...props}>
        <Alert 
          severity="error" 
          action={
            <IconButton
              color="inherit"
              size="small"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshIcon />
            </IconButton>
          }
        >
          {error}
        </Alert>
      </Box>
    );
  }

  // Render empty state
  if (!analytics) {
    return (
      <Box className={className} {...props}>
        <Card variant="glass">
          <CardContent sx={{ textAlign: 'center', py: 6 }}>
            <AnalyticsIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No Analytics Data Available
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Analytics will appear here once you have coupons and redemptions.
            </Typography>
          </CardContent>
        </Card>
      </Box>
    );
  }

  return (
    <Box className={className} {...props}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AnalyticsIcon />
          Coupon Analytics
        </Typography>
        <Box>
          <Tooltip title="Export Data">
            <IconButton onClick={handleExport} disabled={!data?.coupons?.length}>
              <DownloadIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Refresh Data">
            <IconButton onClick={handleRefresh} disabled={refreshing}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Analytics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Total Coupons */}
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <Typography variant="body2" color="text.secondary">
                  Total Coupons
                </Typography>
                <CouponIcon color="primary" />
              </Box>
              <Typography variant="h4" fontWeight="bold" color="primary.main">
                {analytics.total.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {analytics.active} active
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Total Redemptions */}
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <Typography variant="body2" color="text.secondary">
                  Total Redemptions
                </Typography>
                <RedeemIcon color="success" />
              </Box>
              <Typography variant="h4" fontWeight="bold" color="success.main">
                {analytics.totalRedemptions.toLocaleString()}
              </Typography>
              <Box display="flex" alignItems="center" gap={1} mt={1}>
                <LinearProgress
                  variant="determinate"
                  value={Math.min(analytics.usageRate * 10, 100)}
                  sx={{ flexGrow: 1, height: 6, borderRadius: 3 }}
                  color="success"
                />
                <Typography variant="body2" color="text.secondary">
                  {analytics.usageRate.toFixed(1)} avg/coupon
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Revenue Impact */}
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <Typography variant="body2" color="text.secondary">
                  Revenue Impact
                </Typography>
                <MoneyIcon color="warning" />
              </Box>
              <Typography variant="h4" fontWeight="bold" color="warning.main">
                {formatCurrency(analytics.totalRevenue)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Estimated discount value
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Weekly Trend */}
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <Typography variant="body2" color="text.secondary">
                  Weekly Trend
                </Typography>
                <TimelineIcon color="info" />
              </Box>
              <Typography variant="h4" fontWeight="bold" color="info.main">
                {analytics.weeklyNewCoupons}
              </Typography>
              <Box display="flex" alignItems="center" gap={1} mt={1}>
                {analytics.weeklyTrend >= 0 ? (
                  <TrendingUpIcon color="success" fontSize="small" />
                ) : (
                  <TrendingDownIcon color="error" fontSize="small" />
                )}
                <Typography 
                  variant="body2" 
                  color={analytics.weeklyTrend >= 0 ? 'success.main' : 'error.main'}
                >
                  {analytics.weeklyTrend >= 0 ? '+' : ''}{analytics.weeklyTrend} 
                  ({analytics.weeklyTrendPercent.toFixed(1)}%)
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Top Performing Coupons */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card variant="glass">
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <StarIcon />
                Top Performing Coupons
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Code</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Discount</TableCell>
                      <TableCell>Redemptions</TableCell>
                      <TableCell>Status</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {analytics.topPerforming.map((coupon, index) => {
                      const status = getCouponStatus(coupon);
                      return (
                        <TableRow key={coupon.id}>
                          <TableCell>
                            <Typography variant="body2" fontFamily="monospace" fontWeight="medium">
                              {coupon.code}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              {coupon.name}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {formatDiscount(coupon)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              {coupon.redemption_count || 0}
                              {coupon.max_redemptions && ` / ${coupon.max_redemptions}`}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip 
                              label={status.status.charAt(0).toUpperCase() + status.status.slice(1)}
                              color={status.color}
                              size="small"
                            />
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Stats */}
        <Grid item xs={12} md={4}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Stats
              </Typography>
              <Box display="flex" flexDirection="column" gap={2}>
                <Box display="flex" justifyContent="space-between" py={1}>
                  <Typography variant="body2" color="text.secondary">
                    Conversion Rate
                  </Typography>
                  <Typography variant="body2" fontWeight="medium">
                    {analytics.conversionRate.toFixed(1)}%
                  </Typography>
                </Box>
                <Box display="flex" justifyContent="space-between" py={1}>
                  <Typography variant="body2" color="text.secondary">
                    Avg. Redemptions
                  </Typography>
                  <Typography variant="body2" fontWeight="medium">
                    {analytics.usageRate.toFixed(1)} per coupon
                  </Typography>
                </Box>
                <Box display="flex" justifyContent="space-between" py={1}>
                  <Typography variant="body2" color="text.secondary">
                    Active Rate
                  </Typography>
                  <Typography variant="body2" fontWeight="medium">
                    {analytics.total > 0 ? ((analytics.active / analytics.total) * 100).toFixed(1) : 0}%
                  </Typography>
                </Box>
                <Box display="flex" justifyContent="space-between" py={1}>
                  <Typography variant="body2" color="text.secondary">
                    This Week
                  </Typography>
                  <Typography variant="body2" fontWeight="medium">
                    {analytics.weeklyNewCoupons} new coupons
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CouponAnalytics;
