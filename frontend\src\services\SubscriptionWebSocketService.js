/**
 * Enhanced Real-time Subscription Change WebSocket Service v2.0.0
 *
 * Enterprise-grade subscription WebSocket service with comprehensive monitoring,
 * security enhancements, real-time configuration updates, message deduplication,
 * and advanced analytics integration.
 *
 * Features:
 * - Prometheus metrics integration for comprehensive monitoring
 * - Enhanced security fingerprinting with platform-specific validation
 * - Real-time configuration updates with dynamic service management
 * - Intelligent message deduplication for improved reliability
 * - Advanced subscription behavior analytics
 * - Circuit breaker pattern for resilience
 * - Comprehensive error handling and recovery
 *
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 * @since 2024-01-01
 *
 * @example
 * ```javascript
 * import subscriptionWebSocketService from './SubscriptionWebSocketService';
 *
 * // Enhanced connection with security fingerprinting
 * await subscriptionWebSocketService.connect(userId, token, userEmail);
 *
 * // Listen for subscription events with analytics
 * subscriptionWebSocketService.on('subscription_changed', (data) => {
 *   console.log('Plan changed:', data.new_plan);
 * });
 * ```
 */

// Import enhanced platform service and utilities
import platformService from './platformService';
import { PrometheusMetricsCollector } from '../utils/PrometheusMetricsCollector';
import { getDeviceFingerprint } from './fingerprint';

class SubscriptionWebSocketService {
  constructor() {
    // Core WebSocket properties
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.listeners = new Map();
    this.isConnected = false;
    this.userId = null;
    this.userEmail = null;
    this.authToken = null;
    this.heartbeatInterval = null;
    this.connectionHealth = null;
    this.connectionStartTime = null;
    this.lastActivityTime = null;

    // Enhanced Platform Service v2.0.0 Integration
    this.platformService = platformService;

    // Prometheus Metrics Integration
    this.metricsCollector = null;
    this.subscriptionMetrics = {
      connectionAttempts: 0,
      successfulConnections: 0,
      subscriptionChanges: 0,
      billingEvents: 0,
      addonPurchases: 0,
      errorCount: 0,
      messagesSent: 0,
      messagesReceived: 0,
      duplicatesDetected: 0
    };

    // Enhanced Security Features
    this.securityConfig = {
      fingerprintingEnabled: true,
      deviceFingerprint: null,
      platformFingerprint: null,
      securityValidationEnabled: true,
      lastSecurityCheck: null
    };

    // Real-time Configuration Management
    this.configurationManager = {
      enabled: true,
      currentVersion: '1.0.0',
      lastUpdate: null,
      dynamicSettings: new Map(),
      configurationCache: new Map()
    };

    // Message Deduplication System
    this.deduplicationService = {
      enabled: true,
      messageCache: new Map(),
      duplicateThreshold: 0.95,
      windowMs: 30000, // 30 seconds
      stats: {
        processed: 0,
        duplicates: 0,
        unique: 0
      }
    };

    // Advanced Analytics
    this.analyticsCollector = {
      enabled: true,
      sessionId: this._generateSessionId(),
      userBehavior: new Map(),
      subscriptionJourney: [],
      engagementMetrics: {
        sessionDuration: 0,
        eventsProcessed: 0,
        userInteractions: 0
      }
    };

    // Circuit Breaker for Resilience
    this.circuitBreaker = {
      state: 'CLOSED', // CLOSED, OPEN, HALF_OPEN
      failureCount: 0,
      failureThreshold: 5,
      resetTimeout: 60000, // 1 minute
      lastFailureTime: null,
      nextAttemptTime: null
    };

    // Performance Monitoring
    this.performanceMetrics = {
      connectionLatency: 0,
      messageLatency: new Map(),
      throughput: {
        messagesPerSecond: 0,
        lastMeasurement: Date.now(),
        messageCount: 0
      }
    };

    // Initialize enhanced services
    this._initializeEnhancedServices();
  }

  /**
   * Initialize enhanced services integration with Platform Service v2.0.0
   *
   * @private
   */
  _initializeEnhancedServices() {
    try {
      // 1. Initialize Prometheus Metrics Integration
      this._initializeMetricsCollector();

      // 2. Initialize Enhanced Security Features
      this._initializeSecurityFeatures();

      // 3. Initialize Real-time Configuration Management
      this._initializeConfigurationManager();

      // 4. Initialize Message Deduplication Service
      this._initializeDeduplicationService();

      // 5. Initialize Advanced Analytics
      this._initializeAnalyticsCollector();

      // 6. Initialize Circuit Breaker
      this._initializeCircuitBreaker();

      console.log('[SubscriptionWebSocketService] Enhanced services initialized successfully');
    } catch (error) {
      console.warn('[SubscriptionWebSocketService] Enhanced services initialization failed:', error);
      // Graceful degradation - service continues with basic functionality
    }
  }

  /**
   * Initialize Prometheus metrics integration
   *
   * @private
   */
  _initializeMetricsCollector() {
    try {
      this.metricsCollector = new PrometheusMetricsCollector({
        enabled: true,
        endpoint: '/api/metrics/subscription-websocket',
        batchSize: 30,
        flushInterval: 45000 // 45 seconds for subscription events
      });

      console.log('[SubscriptionWebSocketService] Prometheus metrics collector initialized');
    } catch (error) {
      console.warn('[SubscriptionWebSocketService] Metrics collector initialization failed:', error);
      this.metricsCollector = null;
    }
  }

  /**
   * Initialize enhanced security features
   *
   * @private
   */
  _initializeSecurityFeatures() {
    try {
      this.securityConfig = {
        ...this.securityConfig,
        fingerprintingEnabled: this.platformService?.fingerprintingConfig?.enabled || true,
        securityValidationEnabled: true,
        lastSecurityCheck: Date.now()
      };

      console.log('[SubscriptionWebSocketService] Security features initialized');
    } catch (error) {
      console.warn('[SubscriptionWebSocketService] Security features initialization failed:', error);
      this.securityConfig.fingerprintingEnabled = false;
    }
  }

  /**
   * Initialize real-time configuration management
   *
   * @private
   */
  _initializeConfigurationManager() {
    try {
      this.configurationManager = {
        ...this.configurationManager,
        enabled: this.platformService?.realTimeConfig?.enabled || true,
        currentVersion: this.platformService?.realTimeConfig?.configurationVersion || '1.0.0',
        lastUpdate: Date.now()
      };

      // Listen for platform service configuration updates
      if (this.platformService && typeof this.platformService.on === 'function') {
        this.platformService.on('configuration_updated', this._handlePlatformConfigurationUpdate.bind(this));
      }

      console.log('[SubscriptionWebSocketService] Configuration manager initialized');
    } catch (error) {
      console.warn('[SubscriptionWebSocketService] Configuration manager initialization failed:', error);
      this.configurationManager.enabled = false;
    }
  }

  /**
   * Initialize message deduplication service
   *
   * @private
   */
  _initializeDeduplicationService() {
    try {
      this.deduplicationService = {
        ...this.deduplicationService,
        enabled: this.platformService?.deduplicationConfig?.enabled || true,
        duplicateThreshold: this.platformService?.deduplicationConfig?.similarityThreshold || 0.95,
        windowMs: 30000 // 30 seconds for subscription events
      };

      console.log('[SubscriptionWebSocketService] Message deduplication service initialized');
    } catch (error) {
      console.warn('[SubscriptionWebSocketService] Deduplication service initialization failed:', error);
      this.deduplicationService.enabled = false;
    }
  }

  /**
   * Initialize advanced analytics collector
   *
   * @private
   */
  _initializeAnalyticsCollector() {
    try {
      this.analyticsCollector = {
        ...this.analyticsCollector,
        sessionId: this._generateSessionId(),
        enabled: true
      };

      console.log('[SubscriptionWebSocketService] Analytics collector initialized');
    } catch (error) {
      console.warn('[SubscriptionWebSocketService] Analytics collector initialization failed:', error);
      this.analyticsCollector.enabled = false;
    }
  }

  /**
   * Initialize circuit breaker for resilience
   *
   * @private
   */
  _initializeCircuitBreaker() {
    try {
      this.circuitBreaker = {
        state: 'CLOSED',
        failureCount: 0,
        failureThreshold: 5,
        resetTimeout: 60000,
        lastFailureTime: null,
        nextAttemptTime: null
      };

      console.log('[SubscriptionWebSocketService] Circuit breaker initialized');
    } catch (error) {
      console.warn('[SubscriptionWebSocketService] Circuit breaker initialization failed:', error);
    }
  }

  /**
   * Validate authentication token
   * @param {string} token - Authentication token
   * @returns {boolean} Token validity
   */
  validateAuthToken(token) {
    if (!token || typeof token !== 'string') {
      console.error('Invalid authentication token provided');
      return false;
    }

    // Basic token format validation (JWT-like structure)
    const tokenParts = token.split('.');
    if (tokenParts.length !== 3) {
      console.error('Authentication token format is invalid');
      return false;
    }

    try {
      // Decode token payload to check expiration
      const payload = JSON.parse(atob(tokenParts[1]));
      const currentTime = Math.floor(Date.now() / 1000);

      if (payload.exp && payload.exp < currentTime) {
        console.error('Authentication token has expired');
        this.emit('token_expired', { userId: this.userId, expiredAt: payload.exp });
        return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to validate authentication token:', error);
      return false;
    }
  }

  /**
   * Initialize enhanced WebSocket connection for subscription updates with comprehensive validation,
   * security fingerprinting, metrics tracking, and circuit breaker protection
   *
   * @param {string} userId - User ID for subscription updates
   * @param {string} token - Authentication token
   * @param {string} userEmail - User email for logging (optional)
   * @returns {Promise<boolean>} Connection success status
   */
  async connect(userId, token, userEmail = null) {
    const connectionStartTime = Date.now();
    const connectionId = this._generateConnectionId();

    try {
      // Check circuit breaker state
      if (!this._canAttemptConnection()) {
        console.warn('[SubscriptionWebSocketService] Circuit breaker is OPEN, connection blocked');
        this._recordMetric('connection_blocked', 'circuit_breaker_open');
        return false;
      }

      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        console.log('[SubscriptionWebSocketService] WebSocket already connected for user:', userId);
        return true;
      }

      // Validate inputs
      if (!userId || !token) {
        console.error('[SubscriptionWebSocketService] Missing required parameters:', { userId: !!userId, token: !!token });
        this.emit('connection_error', { error: 'Missing authentication credentials' });
        this._recordMetric('connection_failed', 'missing_credentials');
        return false;
      }

      // Validate authentication token
      if (!this.validateAuthToken(token)) {
        console.error('[SubscriptionWebSocketService] Authentication token validation failed');
        this.emit('authentication_failed', { userId, reason: 'Invalid or expired token' });
        this._recordMetric('connection_failed', 'invalid_token');
        return false;
      }

      // Enhanced Security: Generate device and platform fingerprints
      await this._generateSecurityFingerprints();

      // Record connection attempt
      this.subscriptionMetrics.connectionAttempts++;
      this._recordMetric('connection_attempt', 'subscription_websocket', {
        user_id: userId,
        connection_id: connectionId,
        has_fingerprint: !!this.securityConfig.deviceFingerprint
      });

      this.userId = userId;
      this.userEmail = userEmail;
      this.authToken = token;
      this.connectionStartTime = connectionStartTime;
      this.lastActivityTime = Date.now();

      // Enhanced WebSocket URL with security fingerprints
      const wsUrl = await this._buildEnhancedWebSocketUrl(userId, token);

      console.log('[SubscriptionWebSocketService] Attempting enhanced WebSocket connection:', {
        userId,
        userEmail: userEmail || 'unknown',
        connectionId,
        hasFingerprint: !!this.securityConfig.deviceFingerprint,
        timestamp: new Date().toISOString()
      });

      // Create WebSocket connection
      this.ws = new WebSocket(wsUrl);

      // Enhanced connection event handlers
      this._setupEnhancedWebSocketHandlers(connectionId, connectionStartTime);

      return true;
    } catch (error) {
      console.error('[SubscriptionWebSocketService] Connection setup failed:', error);
      this._handleConnectionFailure(error, connectionId);
      return false;
    }
  }

  /**
   * Generate security fingerprints for enhanced authentication
   *
   * @private
   */
  async _generateSecurityFingerprints() {
    try {
      if (!this.securityConfig.fingerprintingEnabled) {
        return;
      }

      // Generate device fingerprint
      this.securityConfig.deviceFingerprint = await getDeviceFingerprint();

      // Generate platform-specific fingerprint for subscription service
      if (this.platformService && typeof this.platformService.generatePlatformFingerprint === 'function') {
        this.securityConfig.platformFingerprint = await this.platformService.generatePlatformFingerprint('subscription', {
          enhanced: true,
          sessionId: this.analyticsCollector.sessionId
        });
      }

      this.securityConfig.lastSecurityCheck = Date.now();

      console.log('[SubscriptionWebSocketService] Security fingerprints generated successfully');
    } catch (error) {
      console.warn('[SubscriptionWebSocketService] Security fingerprint generation failed:', error);
      this.securityConfig.fingerprintingEnabled = false;
    }
  }

  /**
   * Build enhanced WebSocket URL with security parameters
   *
   * @private
   * @param {string} userId - User ID
   * @param {string} token - Authentication token
   * @returns {Promise<string>} Enhanced WebSocket URL
   */
  async _buildEnhancedWebSocketUrl(userId, token) {
    const baseUrl = `${process.env.REACT_APP_WS_URL || 'ws://localhost:8000'}/ws/subscription/${userId}`;
    const params = new URLSearchParams();

    // Basic authentication
    params.append('token', token);

    // Enhanced security parameters
    if (this.securityConfig.deviceFingerprint) {
      params.append('device_fingerprint', this.securityConfig.deviceFingerprint);
    }

    if (this.securityConfig.platformFingerprint?.security?.integrityHash) {
      params.append('platform_hash', this.securityConfig.platformFingerprint.security.integrityHash);
    }

    // Analytics session ID
    if (this.analyticsCollector.sessionId) {
      params.append('session_id', this.analyticsCollector.sessionId);
    }

    // Configuration version for real-time updates
    if (this.configurationManager.currentVersion) {
      params.append('config_version', this.configurationManager.currentVersion);
    }

    return `${baseUrl}?${params.toString()}`;
  }

  /**
   * Setup enhanced WebSocket event handlers with comprehensive monitoring
   *
   * @private
   * @param {string} connectionId - Connection identifier
   * @param {number} connectionStartTime - Connection start timestamp
   */
  _setupEnhancedWebSocketHandlers(connectionId, connectionStartTime) {
    this.ws.onopen = () => {
      this._handleConnectionOpen(connectionId, connectionStartTime);
    };

    this.ws.onmessage = (event) => {
      this._handleEnhancedMessage(event, connectionId);
    };

    this.ws.onclose = (event) => {
      this._handleConnectionClose(event, connectionId);
    };

    this.ws.onerror = (error) => {
      this._handleConnectionError(error, connectionId);
    };
  }

  /**
   * Handle successful WebSocket connection with enhanced logging and metrics
   *
   * @private
   * @param {string} connectionId - Connection identifier
   * @param {number} connectionStartTime - Connection start timestamp
   */
  _handleConnectionOpen(connectionId, connectionStartTime) {
    const connectionLatency = Date.now() - connectionStartTime;

    this.isConnected = true;
    this.reconnectAttempts = 0;
    this.performanceMetrics.connectionLatency = connectionLatency;
    this.subscriptionMetrics.successfulConnections++;

    // Reset circuit breaker on successful connection
    this._resetCircuitBreaker();

    // Record successful connection metrics
    this._recordMetric('connection_success', 'subscription_websocket', {
      user_id: this.userId,
      connection_id: connectionId,
      latency_ms: connectionLatency,
      reconnect_attempts: this.reconnectAttempts
    });

    // Start heartbeat monitoring
    this.startHeartbeat();

    // Initialize analytics session
    this._initializeAnalyticsSession();

    console.log('[SubscriptionWebSocketService] Enhanced WebSocket connected successfully:', {
      userId: this.userId,
      userEmail: this.userEmail || 'unknown',
      connectionId,
      latencyMs: connectionLatency,
      hasFingerprint: !!this.securityConfig.deviceFingerprint,
      timestamp: new Date().toISOString()
    });

    this.emit('connected', {
      userId: this.userId,
      connectionId,
      latency: connectionLatency,
      timestamp: Date.now()
    });
  }

  /**
   * Handle enhanced WebSocket messages with deduplication and analytics
   *
   * @private
   * @param {MessageEvent} event - WebSocket message event
   * @param {string} connectionId - Connection identifier
   */
  async _handleEnhancedMessage(event, connectionId) {
    const messageStartTime = Date.now();

    try {
      const data = JSON.parse(event.data);
      this.lastActivityTime = Date.now();
      this.subscriptionMetrics.messagesReceived++;

      // Message deduplication check
      if (await this._isDuplicateMessage(data)) {
        this.subscriptionMetrics.duplicatesDetected++;
        this._recordMetric('message_duplicate', data.type || 'unknown');
        console.log('[SubscriptionWebSocketService] Duplicate message detected and ignored:', data.type);
        return;
      }

      // Record message processing metrics
      const processingTime = Date.now() - messageStartTime;
      this.performanceMetrics.messageLatency.set(data.type || 'unknown', processingTime);

      this._recordMetric('message_received', data.type || 'unknown', {
        user_id: this.userId,
        connection_id: connectionId,
        processing_time_ms: processingTime
      });

      // Update analytics
      this._trackUserEngagement(data);

      // Process message with enhanced handling
      await this._processEnhancedMessage(data);

    } catch (error) {
      console.error('[SubscriptionWebSocketService] Enhanced message processing failed:', error);
      this._recordMetric('message_error', 'processing_failed');
      this._handleCircuitBreakerFailure();
    }
  }

  /**
   * Process enhanced message with analytics and business logic
   *
   * @private
   * @param {Object} data - Message data
   */
  async _processEnhancedMessage(data) {
    const { type, payload } = data;

    // Update throughput metrics
    this._updateThroughputMetrics();

    // Log message for debugging (excluding heartbeat to reduce noise)
    if (type !== 'heartbeat') {
      console.log('[SubscriptionWebSocketService] Processing enhanced message:', {
        type,
        userId: this.userId,
        userEmail: this.userEmail || 'unknown',
        timestamp: new Date().toISOString()
      });
    }

    // Route to appropriate handler with enhanced processing
    switch (type) {
      case 'subscription_changed':
        await this._handleEnhancedSubscriptionChange(payload);
        break;
      case 'feature_access_updated':
        await this._handleEnhancedFeatureAccessUpdate(payload);
        break;
      case 'billing_event':
        await this._handleEnhancedBillingEvent(payload);
        break;
      case 'addon_purchased':
        await this._handleEnhancedAddonPurchase(payload);
        break;
      case 'bundle_purchased':
        await this._handleEnhancedBundlePurchase(payload);
        break;
      case 'plan_upgraded':
        await this._handleEnhancedPlanUpgrade(payload);
        break;
      case 'heartbeat':
        await this._handleEnhancedHeartbeat(payload);
        break;
      case 'authentication_error':
        await this._handleEnhancedAuthenticationError(payload);
        break;
      case 'configuration_update':
        await this._handleConfigurationUpdate(payload);
        break;
      default:
        console.warn('[SubscriptionWebSocketService] Unknown message type:', type);
        this._recordMetric('message_unknown', type);
    }
  }

  /**
   * Handle connection close with enhanced logging and reconnection logic
   *
   * @private
   * @param {CloseEvent} event - WebSocket close event
   * @param {string} connectionId - Connection identifier
   */
  _handleConnectionClose(event, connectionId) {
    this.isConnected = false;
    this.stopHeartbeat();

    console.log('[SubscriptionWebSocketService] WebSocket connection closed:', {
      userId: this.userId,
      userEmail: this.userEmail || 'unknown',
      connectionId,
      code: event.code,
      reason: event.reason,
      wasClean: event.wasClean,
      timestamp: new Date().toISOString()
    });

    // Record disconnection metrics
    this._recordMetric('connection_closed', 'subscription_websocket', {
      user_id: this.userId,
      connection_id: connectionId,
      close_code: event.code,
      was_clean: event.wasClean
    });

    // Emit disconnection event
    this.emit('disconnected', {
      userId: this.userId,
      connectionId,
      code: event.code,
      reason: event.reason,
      timestamp: Date.now()
    });

    // Schedule reconnection if not a clean close
    if (!event.wasClean && event.code !== 1000) {
      this.scheduleReconnect();
    }
  }

  /**
   * Handle connection error with enhanced logging and circuit breaker logic
   *
   * @private
   * @param {Event} error - WebSocket error event
   * @param {string} connectionId - Connection identifier
   */
  _handleConnectionError(error, connectionId) {
    console.error('[SubscriptionWebSocketService] WebSocket connection error:', {
      userId: this.userId,
      userEmail: this.userEmail || 'unknown',
      connectionId,
      error: error.message || 'Unknown WebSocket error',
      timestamp: new Date().toISOString()
    });

    // Record error metrics
    this._recordMetric('connection_error', 'websocket_error', {
      user_id: this.userId,
      connection_id: connectionId,
      error_type: error.type || 'unknown'
    });

    // Handle circuit breaker failure
    this._handleCircuitBreakerFailure();

    // Emit error event
    this.emit('error', {
      userId: this.userId,
      connectionId,
      error: error.message || 'WebSocket connection error',
      timestamp: Date.now()
    });
  }

  /**
   * Handle connection failure during setup
   *
   * @private
   * @param {Error} error - Connection error
   * @param {string} connectionId - Connection identifier
   */
  _handleConnectionFailure(error, connectionId) {
    this.subscriptionMetrics.errorCount++;

    // Record failure metrics
    this._recordMetric('connection_failed', 'setup_error', {
      user_id: this.userId,
      connection_id: connectionId,
      error_message: error.message
    });

    // Handle circuit breaker failure
    this._handleCircuitBreakerFailure();

    // Emit connection error
    this.emit('connection_error', {
      error: error.message,
      userId: this.userId,
      connectionId,
      timestamp: Date.now()
    });
  }

  /**
   * Set up WebSocket event handlers with enhanced logging (Legacy method for backward compatibility)
   */
  setupEventHandlers() {
    this.ws.onopen = () => {
      console.log('Subscription WebSocket connected:', {
        userId: this.userId,
        userEmail: this.userEmail || 'unknown',
        connectionTime: Date.now() - this.connectionStartTime,
        timestamp: new Date().toISOString()
      });

      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.lastActivityTime = Date.now();
      this.startHeartbeat();
      this.emit('connected', {
        userId: this.userId,
        userEmail: this.userEmail,
        connectionTime: Date.now() - this.connectionStartTime
      });
    };

    this.ws.onmessage = (event) => {
      try {
        this.lastActivityTime = Date.now();
        const data = JSON.parse(event.data);
        this.handleMessage(data);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', {
          userId: this.userId,
          userEmail: this.userEmail || 'unknown',
          error: error.message,
          rawData: event.data,
          timestamp: new Date().toISOString()
        });
        this.emit('message_parse_error', { error: error.message, rawData: event.data });
      }
    };

    this.ws.onclose = (event) => {
      const connectionDuration = this.connectionStartTime ? Date.now() - this.connectionStartTime : 0;

      console.log('Subscription WebSocket disconnected:', {
        userId: this.userId,
        userEmail: this.userEmail || 'unknown',
        code: event.code,
        reason: event.reason,
        connectionDuration,
        wasCleanClose: event.code === 1000,
        timestamp: new Date().toISOString()
      });

      this.isConnected = false;
      this.stopHeartbeat();
      this.emit('disconnected', {
        code: event.code,
        reason: event.reason,
        userId: this.userId,
        userEmail: this.userEmail,
        connectionDuration,
        wasCleanClose: event.code === 1000
      });

      // Handle specific close codes
      if (event.code === 1008) { // Policy violation (likely auth issue)
        console.error('WebSocket closed due to authentication failure');
        this.emit('authentication_failed', {
          userId: this.userId,
          reason: 'Authentication policy violation',
          code: event.code
        });
      } else if (event.code !== 1000) { // Not a normal closure
        this.scheduleReconnect();
      }
    };

    this.ws.onerror = (error) => {
      console.error('Subscription WebSocket error:', {
        userId: this.userId,
        userEmail: this.userEmail || 'unknown',
        error: error.message || 'Unknown WebSocket error',
        readyState: this.ws ? this.ws.readyState : 'unknown',
        timestamp: new Date().toISOString()
      });

      this.emit('error', {
        error: error.message || 'Unknown WebSocket error',
        userId: this.userId,
        userEmail: this.userEmail,
        readyState: this.ws ? this.ws.readyState : 'unknown'
      });
    };
  }

  /**
   * Handle incoming WebSocket messages with enhanced logging
   * @param {Object} data - Message data
   */
  handleMessage(data) {
    const { type, payload } = data;

    // Log message for debugging (excluding heartbeat to reduce noise)
    if (type !== 'heartbeat') {
      console.log('WebSocket message received:', {
        type,
        userId: this.userId,
        userEmail: this.userEmail || 'unknown',
        hasPayload: !!payload,
        timestamp: new Date().toISOString()
      });
    }

    switch (type) {
      case 'subscription_changed':
        this.handleSubscriptionChange(payload);
        break;
      case 'feature_access_updated':
        this.handleFeatureAccessUpdate(payload);
        break;
      case 'billing_event':
        this.handleBillingEvent(payload);
        break;
      case 'addon_purchased':
        this.handleAddonPurchased(payload);
        break;
      case 'bundle_purchased':
        this.handleBundlePurchased(payload);
        break;
      case 'plan_upgraded':
        this.handlePlanUpgraded(payload);
        break;
      case 'heartbeat':
        this.handleHeartbeat(payload);
        break;
      case 'authentication_error':
        this.handleAuthenticationError(payload);
        break;
      default:
        console.warn('Unknown WebSocket message type:', {
          type,
          userId: this.userId,
          userEmail: this.userEmail || 'unknown',
          timestamp: new Date().toISOString()
        });
    }

    // Emit the raw message for custom handlers
    this.emit('message', data);
  }

  /**
   * Handle authentication error events
   * @param {Object} payload - Authentication error data
   */
  handleAuthenticationError(payload) {
    console.error('Authentication error received:', {
      userId: this.userId,
      userEmail: this.userEmail || 'unknown',
      error: payload.error || 'Unknown authentication error',
      timestamp: new Date().toISOString()
    });

    // Clear stored authentication data
    this.authToken = null;
    localStorage.removeItem('auth_token');
    sessionStorage.removeItem('auth_token');

    // Emit authentication error event
    this.emit('authentication_failed', {
      userId: this.userId,
      userEmail: this.userEmail,
      error: payload.error || 'Authentication failed',
      requiresReauth: true
    });

    // Show notification if UI is available
    this._showNotification({
      type: 'error',
      title: 'Authentication Error',
      message: 'Your session has expired. Please log in again.',
      duration: 8000,
      actions: [
        {
          label: 'Log In',
          action: () => window.location.href = '/login'
        }
      ]
    });

    // Disconnect to prevent further attempts with invalid token
    this.disconnect();
  }

  // ===================================================================
  // ENHANCED MESSAGE HANDLERS - Platform Service v2.0.0 Integration
  // ===================================================================

  /**
   * Handle enhanced subscription change with comprehensive analytics and metrics
   *
   * @private
   * @param {Object} payload - Subscription change data
   */
  async _handleEnhancedSubscriptionChange(payload) {
    try {
      // Record subscription change metrics
      this.subscriptionMetrics.subscriptionChanges++;
      this._recordMetric('subscription_changed', 'success', {
        old_plan: payload.old_plan,
        new_plan: payload.new_plan,
        change_type: this._getSubscriptionChangeType(payload.old_plan, payload.new_plan)
      });

      // Clear relevant caches
      this.clearFeatureAccessCache();
      this.clearUserCache();

      // Update analytics journey
      this.analyticsCollector.subscriptionJourney.push({
        event: 'subscription_changed',
        timestamp: Date.now(),
        oldPlan: payload.old_plan,
        newPlan: payload.new_plan,
        userId: this.userId
      });

      // Call original handler for backward compatibility
      this.handleSubscriptionChange(payload);

    } catch (error) {
      console.error('[SubscriptionWebSocketService] Enhanced subscription change handling failed:', error);
      this._recordMetric('subscription_changed', 'error');
    }
  }

  /**
   * Handle enhanced feature access update with cache management
   *
   * @private
   * @param {Object} payload - Feature access data
   */
  async _handleEnhancedFeatureAccessUpdate(payload) {
    try {
      this._recordMetric('feature_access_updated', 'success', {
        features_count: Object.keys(payload.features || {}).length
      });

      // Call original handler for backward compatibility
      this.handleFeatureAccessUpdate(payload);

    } catch (error) {
      console.error('[SubscriptionWebSocketService] Enhanced feature access update failed:', error);
      this._recordMetric('feature_access_updated', 'error');
    }
  }

  /**
   * Handle enhanced billing event with comprehensive tracking
   *
   * @private
   * @param {Object} payload - Billing event data
   */
  async _handleEnhancedBillingEvent(payload) {
    try {
      this.subscriptionMetrics.billingEvents++;
      this._recordMetric('billing_event', payload.event_type || 'unknown', {
        amount: payload.amount,
        currency: payload.currency,
        payment_method: payload.payment_method
      });

      // Call original handler for backward compatibility
      this.handleBillingEvent(payload);

    } catch (error) {
      console.error('[SubscriptionWebSocketService] Enhanced billing event handling failed:', error);
      this._recordMetric('billing_event', 'error');
    }
  }

  /**
   * Handle enhanced addon purchase with analytics
   *
   * @private
   * @param {Object} payload - Addon purchase data
   */
  async _handleEnhancedAddonPurchase(payload) {
    try {
      this.subscriptionMetrics.addonPurchases++;
      this._recordMetric('addon_purchased', 'success', {
        addon_id: payload.addon_id,
        addon_name: payload.addon_name,
        price: payload.price
      });

      // Call original handler for backward compatibility
      this.handleAddonPurchased(payload);

    } catch (error) {
      console.error('[SubscriptionWebSocketService] Enhanced addon purchase handling failed:', error);
      this._recordMetric('addon_purchased', 'error');
    }
  }

  /**
   * Handle enhanced bundle purchase with tracking
   *
   * @private
   * @param {Object} payload - Bundle purchase data
   */
  async _handleEnhancedBundlePurchase(payload) {
    try {
      this._recordMetric('bundle_purchased', 'success', {
        bundle_id: payload.bundle_id,
        bundle_name: payload.bundle_name,
        items_count: payload.items?.length || 0
      });

      // Call original handler for backward compatibility
      this.handleBundlePurchased(payload);

    } catch (error) {
      console.error('[SubscriptionWebSocketService] Enhanced bundle purchase handling failed:', error);
      this._recordMetric('bundle_purchased', 'error');
    }
  }

  /**
   * Handle enhanced plan upgrade with comprehensive analytics
   *
   * @private
   * @param {Object} payload - Plan upgrade data
   */
  async _handleEnhancedPlanUpgrade(payload) {
    try {
      this._recordMetric('plan_upgraded', 'success', {
        old_plan: payload.old_plan,
        new_plan: payload.new_plan,
        upgrade_type: this._getUpgradeType(payload.old_plan, payload.new_plan)
      });

      // Call original handler for backward compatibility
      this.handlePlanUpgraded(payload);

    } catch (error) {
      console.error('[SubscriptionWebSocketService] Enhanced plan upgrade handling failed:', error);
      this._recordMetric('plan_upgraded', 'error');
    }
  }

  /**
   * Handle enhanced heartbeat with connection health monitoring
   *
   * @private
   * @param {Object} payload - Heartbeat data
   */
  async _handleEnhancedHeartbeat(payload) {
    try {
      // Update connection health metrics
      const roundTripTime = Date.now() - (payload.client_timestamp || Date.now());

      this.connectionHealth = {
        lastHeartbeat: payload.client_timestamp || Date.now(),
        roundTripTime,
        connectionId: payload.connection_id,
        serverLoad: payload.server_load,
        quality: roundTripTime < 100 ? 'excellent' : roundTripTime < 300 ? 'good' : 'poor'
      };

      // Record performance metrics
      this._recordMetric('heartbeat', 'success', {
        round_trip_time: roundTripTime,
        connection_quality: this.connectionHealth.quality
      });

      // Call original handler for backward compatibility
      this.handleHeartbeat(payload);

    } catch (error) {
      console.error('[SubscriptionWebSocketService] Enhanced heartbeat handling failed:', error);
      this._recordMetric('heartbeat', 'error');
    }
  }

  /**
   * Handle enhanced authentication error with security logging
   *
   * @private
   * @param {Object} payload - Authentication error data
   */
  async _handleEnhancedAuthenticationError(payload) {
    try {
      this._recordMetric('authentication_error', payload.error_type || 'unknown', {
        reason: payload.reason,
        security_context: !!this.securityConfig.deviceFingerprint
      });

      // Call original handler for backward compatibility
      this.handleAuthenticationError(payload);

    } catch (error) {
      console.error('[SubscriptionWebSocketService] Enhanced authentication error handling failed:', error);
      this._recordMetric('authentication_error', 'processing_error');
    }
  }

  /**
   * Handle configuration updates from platform service
   *
   * @private
   * @param {Object} payload - Configuration update data
   */
  async _handleConfigurationUpdate(payload) {
    try {
      this._recordMetric('configuration_update', 'received', {
        version: payload.version,
        settings_count: Object.keys(payload.settings || {}).length
      });

      // Delegate to platform configuration handler
      this._handlePlatformConfigurationUpdate(payload);

    } catch (error) {
      console.error('[SubscriptionWebSocketService] Configuration update handling failed:', error);
      this._recordMetric('configuration_update', 'error');
    }
  }

  // ===================================================================
  // ORIGINAL MESSAGE HANDLERS - Maintained for Backward Compatibility
  // ===================================================================

  /**
   * Handle subscription change events with enhanced logging
   * @param {Object} payload - Subscription change data
   */
  handleSubscriptionChange(payload) {
    console.log('Subscription changed:', {
      userId: this.userId,
      userEmail: this.userEmail || 'unknown',
      oldPlan: payload.old_plan,
      newPlan: payload.new_plan,
      timestamp: new Date().toISOString()
    });

    // Clear feature access cache
    this.clearFeatureAccessCache();

    // Emit subscription change event
    this.emit('subscription_changed', payload);

    // Show notification if UI is available
    this._showNotification({
      type: 'info',
      title: 'Subscription Updated',
      message: `Your subscription has been updated to ${payload.new_plan}`,
      duration: 5000
    });
  }

  /**
   * Handle feature access updates
   * @param {Object} payload - Feature access data
   */
  handleFeatureAccessUpdate(payload) {
    console.log('Feature access updated:', payload);
    
    // Update local feature access cache
    this.updateFeatureAccessCache(payload.features);
    
    // Emit feature access update event
    this.emit('feature_access_updated', payload);
  }

  /**
   * Handle billing events
   * @param {Object} payload - Billing event data
   */
  handleBillingEvent(payload) {
    console.log('Billing event:', payload);
    
    this.emit('billing_event', payload);
    
    // Handle specific billing events
    switch (payload.event_type) {
      case 'payment_successful':
        this.handlePaymentSuccessful(payload);
        break;
      case 'payment_failed':
        this.handlePaymentFailed(payload);
        break;
      case 'subscription_expired':
        this.handleSubscriptionExpired(payload);
        break;
      case 'trial_ending':
        this.handleTrialEnding(payload);
        break;
    }
  }

  /**
   * Handle addon purchase events
   * @param {Object} payload - Addon purchase data
   */
  handleAddonPurchased(payload) {
    console.log('Addon purchased:', payload);
    
    // Update feature access
    this.clearFeatureAccessCache();
    
    this.emit('addon_purchased', payload);
    
    this._showNotification({
      type: 'success',
      title: 'Add-on Purchased',
      message: `Successfully purchased ${payload.addon_name}`,
      duration: 3000
    });
  }

  /**
   * Handle bundle purchase events
   * @param {Object} payload - Bundle purchase data
   */
  handleBundlePurchased(payload) {
    console.log('Bundle purchased:', payload);
    
    // Update feature access
    this.clearFeatureAccessCache();
    
    this.emit('bundle_purchased', payload);
    
    this._showNotification({
      type: 'success',
      title: 'Bundle Purchased',
      message: `Successfully purchased ${payload.bundle_name} bundle!`,
      duration: 3000
    });
  }

  /**
   * Handle plan upgrade events
   * @param {Object} payload - Plan upgrade data
   */
  handlePlanUpgraded(payload) {
    console.log('Plan upgraded:', payload);
    
    // Clear all caches
    this.clearFeatureAccessCache();
    this.clearUserCache();
    
    this.emit('plan_upgraded', payload);
    
    this._showNotification({
      type: 'success',
      title: 'Plan Upgraded',
      message: `Welcome to ${payload.new_plan}! Enjoy your new features.`,
      duration: 5000
    });
  }

  /**
   * Handle heartbeat messages
   * @param {Object} payload - Heartbeat data
   */
  handleHeartbeat(payload) {
    // Extract heartbeat information for connection health monitoring
    const serverTimestamp = payload.timestamp || Date.now();
    const connectionId = payload.connection_id || 'unknown';
    const serverLoad = payload.server_load || 'normal';

    // Calculate round-trip time for connection quality monitoring
    const clientTimestamp = Date.now();
    const roundTripTime = payload.client_timestamp ?
      clientTimestamp - payload.client_timestamp : 0;

    // Update connection health metrics
    this.connectionHealth = {
      lastHeartbeat: clientTimestamp,
      serverTimestamp,
      roundTripTime,
      connectionId,
      serverLoad,
      quality: roundTripTime < 100 ? 'excellent' :
               roundTripTime < 300 ? 'good' :
               roundTripTime < 1000 ? 'fair' : 'poor'
    };

    // Log connection health for monitoring
    console.debug('WebSocket heartbeat received:', {
      roundTripTime,
      serverLoad,
      connectionId,
      quality: this.connectionHealth.quality
    });

    // Send enhanced heartbeat response with client metrics
    this.send({
      type: 'heartbeat_response',
      timestamp: clientTimestamp,
      client_timestamp: clientTimestamp,
      server_timestamp: serverTimestamp,
      round_trip_time: roundTripTime,
      connection_quality: this.connectionHealth.quality,
      client_info: {
        user_agent: navigator.userAgent,
        connection_type: navigator.connection?.effectiveType || 'unknown',
        online: navigator.onLine
      }
    });

    // Trigger connection health event for UI updates
    this.emit('connection_health_update', this.connectionHealth);
  }

  /**
   * Handle payment successful events
   * @param {Object} payload - Payment data
   */
  handlePaymentSuccessful(payload) {
    this._showNotification({
      type: 'success',
      title: 'Payment Successful',
      message: `Payment of ${payload.amount} processed successfully`,
      duration: 3000
    });
  }

  /**
   * Handle payment failed events
   * @param {Object} payload - Payment failure data
   */
  handlePaymentFailed(payload) {
    this._showNotification({
      type: 'error',
      title: 'Payment Failed',
      message: `Payment failed: ${payload.reason}. Please update your payment method.`,
      duration: 8000
    });
  }

  /**
   * Handle subscription expired events
   * @param {Object} payload - Expiration data
   */
  handleSubscriptionExpired(payload) {
    // Extract expiration details from payload
    const expirationDate = payload.expiration_date || new Date().toISOString();
    const subscriptionType = payload.subscription_type || 'Premium';
    const gracePeriodDays = payload.grace_period_days || 0;
    const renewalUrl = payload.renewal_url || '/billing/renew';
    const userId = payload.user_id;
    const subscriptionId = payload.subscription_id;

    // Clear feature access cache
    this.clearFeatureAccessCache();

    // Update subscription status in local storage
    const subscriptionStatus = {
      status: 'expired',
      expiration_date: expirationDate,
      subscription_type: subscriptionType,
      grace_period_days: gracePeriodDays,
      expired_at: new Date().toISOString(),
      user_id: userId,
      subscription_id: subscriptionId
    };

    localStorage.setItem('subscription_status', JSON.stringify(subscriptionStatus));

    // Log expiration event for analytics
    console.warn('Subscription expired:', {
      subscriptionType,
      expirationDate,
      gracePeriodDays,
      userId,
      subscriptionId
    });

    // Show detailed notification with expiration information
    const message = gracePeriodDays > 0 ?
      `Your ${subscriptionType} subscription expired on ${new Date(expirationDate).toLocaleDateString()}. You have ${gracePeriodDays} days of grace period remaining.` :
      `Your ${subscriptionType} subscription expired on ${new Date(expirationDate).toLocaleDateString()}. Please renew to continue using premium features.`;

    this._showNotification({
      type: 'warning',
      title: 'Subscription Expired',
      message,
      duration: 15000,
      actions: [
        {
          label: 'Renew Now',
          action: () => window.location.href = renewalUrl
        },
        {
          label: 'View Details',
          action: () => window.location.href = '/billing/subscription'
        }
      ]
    });

    // Emit subscription expired event for app-wide handling
    this.emit('subscription_expired', {
      subscriptionType,
      expirationDate,
      gracePeriodDays,
      renewalUrl,
      userId,
      subscriptionId
    });

    // Trigger UI updates for expired subscription state
    if (window.updateSubscriptionUI) {
      window.updateSubscriptionUI('expired', subscriptionStatus);
    }
  }

  /**
   * Handle trial ending events
   * @param {Object} payload - Trial ending data
   */
  handleTrialEnding(payload) {
    this._showNotification({
      type: 'info',
      title: 'Trial Ending Soon',
      message: `Your trial ends in ${payload.days_remaining} days. Upgrade now to continue using premium features.`,
      duration: 8000
    });
  }

  /**
   * Start heartbeat to keep connection alive
   */
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.send({
          type: 'heartbeat',
          timestamp: Date.now()
        });
      }
    }, 30000); // Send heartbeat every 30 seconds
  }

  /**
   * Stop heartbeat
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Send message through WebSocket with enhanced validation
   * @param {Object} data - Data to send
   */
  send(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(data));
        this.lastActivityTime = Date.now();
      } catch (error) {
        console.error('Failed to send WebSocket message:', {
          userId: this.userId,
          userEmail: this.userEmail || 'unknown',
          error: error.message,
          data: data,
          timestamp: new Date().toISOString()
        });
        this.emit('send_error', { error: error.message, data });
      }
    } else {
      console.warn('WebSocket not connected, cannot send message:', {
        userId: this.userId,
        userEmail: this.userEmail || 'unknown',
        readyState: this.ws ? this.ws.readyState : 'null',
        data: data,
        timestamp: new Date().toISOString()
      });
      this.emit('send_failed', {
        reason: 'WebSocket not connected',
        readyState: this.ws ? this.ws.readyState : 'null',
        data
      });
    }
  }

  /**
   * Subscribe to specific events
   * @param {string} event - Event name
   * @param {Function} callback - Event callback
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  /**
   * Unsubscribe from events
   * @param {string} event - Event name
   * @param {Function} callback - Event callback to remove
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * Emit event to all listeners
   * @param {string} event - Event name
   * @param {*} data - Event data
   */
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Schedule reconnection attempt with enhanced validation
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached:', {
        userId: this.userId,
        userEmail: this.userEmail || 'unknown',
        attempts: this.reconnectAttempts,
        timestamp: new Date().toISOString()
      });

      this.emit('max_reconnect_attempts_reached', {
        userId: this.userId,
        userEmail: this.userEmail,
        attempts: this.reconnectAttempts
      });
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff

    console.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms:`, {
      userId: this.userId,
      userEmail: this.userEmail || 'unknown',
      attempt: this.reconnectAttempts,
      maxAttempts: this.maxReconnectAttempts,
      delay,
      timestamp: new Date().toISOString()
    });

    setTimeout(() => {
      if (this.userId) {
        const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
        if (token) {
          // Validate token before attempting reconnection
          if (this.validateAuthToken(token)) {
            console.log('Attempting reconnection with valid token:', {
              userId: this.userId,
              attempt: this.reconnectAttempts
            });
            this.connect(this.userId, token, this.userEmail);
          } else {
            console.error('Cannot reconnect: Invalid authentication token');
            this.emit('authentication_failed', {
              userId: this.userId,
              reason: 'Invalid token during reconnection'
            });
          }
        } else {
          console.error('Cannot reconnect: No authentication token found');
          this.emit('authentication_failed', {
            userId: this.userId,
            reason: 'No token available for reconnection'
          });
        }
      }
    }, delay);
  }

  /**
   * Clear feature access cache
   */
  clearFeatureAccessCache() {
    // Clear localStorage cache
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith('feature_access_') || key.startsWith('user_features_')) {
        localStorage.removeItem(key);
      }
    });

    // Clear sessionStorage cache
    const sessionKeys = Object.keys(sessionStorage);
    sessionKeys.forEach(key => {
      if (key.startsWith('feature_access_') || key.startsWith('user_features_')) {
        sessionStorage.removeItem(key);
      }
    });

    console.log('Feature access cache cleared');
  }

  /**
   * Update feature access cache
   * @param {Object} features - Updated features
   */
  updateFeatureAccessCache(features) {
    const cacheKey = `user_features_${this.userId}`;
    const cacheData = {
      features,
      timestamp: Date.now(),
      expires: Date.now() + (15 * 60 * 1000) // 15 minutes
    };
    
    localStorage.setItem(cacheKey, JSON.stringify(cacheData));
    console.log('Feature access cache updated');
  }

  /**
   * Clear user cache
   */
  clearUserCache() {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith('user_') || key.startsWith('subscription_')) {
        localStorage.removeItem(key);
      }
    });
    console.log('User cache cleared');
  }

  /**
   * Disconnect WebSocket with enhanced cleanup
   */
  disconnect() {
    const disconnectInfo = {
      userId: this.userId,
      userEmail: this.userEmail || 'unknown',
      connectionDuration: this.connectionStartTime ? Date.now() - this.connectionStartTime : 0,
      timestamp: new Date().toISOString()
    };

    console.log('Disconnecting Subscription WebSocket:', disconnectInfo);

    this.stopHeartbeat();

    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }

    this.isConnected = false;
    this.userId = null;
    this.userEmail = null;
    this.authToken = null;
    this.connectionStartTime = null;
    this.lastActivityTime = null;
    this.connectionHealth = null;
    this.listeners.clear();

    this.emit('client_disconnected', disconnectInfo);
    console.log('Subscription WebSocket disconnected successfully');
  }

  /**
   * Get connection status with detailed information
   * @returns {Object} Connection status and details
   */
  getConnectionStatus() {
    const isConnected = this.isConnected && this.ws && this.ws.readyState === WebSocket.OPEN;

    return {
      isConnected,
      userId: this.userId,
      userEmail: this.userEmail,
      readyState: this.ws ? this.ws.readyState : null,
      connectionDuration: this.connectionStartTime ? Date.now() - this.connectionStartTime : 0,
      lastActivity: this.lastActivityTime,
      timeSinceLastActivity: this.lastActivityTime ? Date.now() - this.lastActivityTime : null,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts,
      connectionHealth: this.connectionHealth,
      hasValidToken: !!this.authToken
    };
  }

  /**
   * Get connection status (legacy method for backward compatibility)
   * @returns {boolean} Connection status
   */
  isConnectedStatus() {
    return this.isConnected && this.ws && this.ws.readyState === WebSocket.OPEN;
  }

  // ===================================================================
  // ENHANCED UTILITY METHODS - Platform Service v2.0.0 Integration
  // ===================================================================

  /**
   * Check if connection attempt is allowed by circuit breaker
   *
   * @private
   * @returns {boolean} Whether connection can be attempted
   */
  _canAttemptConnection() {
    if (this.circuitBreaker.state === 'CLOSED') {
      return true;
    }

    if (this.circuitBreaker.state === 'OPEN') {
      // Check if reset timeout has passed
      if (Date.now() >= this.circuitBreaker.nextAttemptTime) {
        this.circuitBreaker.state = 'HALF_OPEN';
        return true;
      }
      return false;
    }

    // HALF_OPEN state - allow one attempt
    return this.circuitBreaker.state === 'HALF_OPEN';
  }

  /**
   * Handle circuit breaker failure
   *
   * @private
   */
  _handleCircuitBreakerFailure() {
    this.circuitBreaker.failureCount++;
    this.circuitBreaker.lastFailureTime = Date.now();

    if (this.circuitBreaker.failureCount >= this.circuitBreaker.failureThreshold) {
      this.circuitBreaker.state = 'OPEN';
      this.circuitBreaker.nextAttemptTime = Date.now() + this.circuitBreaker.resetTimeout;

      console.warn('[SubscriptionWebSocketService] Circuit breaker opened due to failures:', {
        failureCount: this.circuitBreaker.failureCount,
        nextAttemptTime: new Date(this.circuitBreaker.nextAttemptTime).toISOString()
      });
    }
  }

  /**
   * Reset circuit breaker on successful operation
   *
   * @private
   */
  _resetCircuitBreaker() {
    this.circuitBreaker.state = 'CLOSED';
    this.circuitBreaker.failureCount = 0;
    this.circuitBreaker.lastFailureTime = null;
    this.circuitBreaker.nextAttemptTime = null;
  }

  /**
   * Record metrics using Prometheus collector
   *
   * @private
   * @param {string} operation - Operation name
   * @param {string} status - Operation status
   * @param {Object} metadata - Additional metadata
   */
  _recordMetric(operation, status, metadata = {}) {
    try {
      if (!this.metricsCollector) return;

      this.metricsCollector.recordMessageSend(
        'subscription_websocket',
        'creator', // Default tier, should be updated from user context
        status,
        0, // Duration will be calculated by collector
        {
          operation,
          user_id: this.userId,
          session_id: this.analyticsCollector.sessionId,
          ...metadata
        }
      );
    } catch (error) {
      console.warn('[SubscriptionWebSocketService] Metrics recording failed:', error);
    }
  }

  /**
   * Check if message is duplicate using platform service deduplication
   *
   * @private
   * @param {Object} data - Message data
   * @returns {Promise<boolean>} Whether message is duplicate
   */
  async _isDuplicateMessage(data) {
    try {
      if (!this.deduplicationService.enabled) return false;

      // Generate message fingerprint using platform service
      if (this.platformService && typeof this.platformService.generateMessageFingerprint === 'function') {
        const fingerprint = await this.platformService.generateMessageFingerprint(
          'subscription',
          JSON.stringify(data),
          {
            userId: this.userId,
            messageType: data.type,
            timestamp: Date.now()
          }
        );

        // Check cache for duplicate
        const cacheKey = `sub_msg_${fingerprint}`;
        const cached = this.deduplicationService.messageCache.get(cacheKey);

        if (cached && (Date.now() - cached.timestamp) < this.deduplicationService.windowMs) {
          this.deduplicationService.stats.duplicates++;
          return true;
        }

        // Store in cache
        this.deduplicationService.messageCache.set(cacheKey, {
          fingerprint,
          timestamp: Date.now(),
          data
        });

        // Clean old entries
        this._cleanMessageCache();
      }

      this.deduplicationService.stats.unique++;
      return false;
    } catch (error) {
      console.warn('[SubscriptionWebSocketService] Duplicate check failed:', error);
      return false;
    }
  }

  /**
   * Clean old entries from message cache
   *
   * @private
   */
  _cleanMessageCache() {
    const now = Date.now();
    const windowMs = this.deduplicationService.windowMs;

    for (const [key, value] of this.deduplicationService.messageCache.entries()) {
      if (now - value.timestamp > windowMs) {
        this.deduplicationService.messageCache.delete(key);
      }
    }
  }

  /**
   * Track user engagement for analytics
   *
   * @private
   * @param {Object} data - Message data
   */
  _trackUserEngagement(data) {
    try {
      if (!this.analyticsCollector.enabled) return;

      this.analyticsCollector.engagementMetrics.eventsProcessed++;

      // Track subscription journey events
      if (['subscription_changed', 'plan_upgraded', 'addon_purchased'].includes(data.type)) {
        this.analyticsCollector.subscriptionJourney.push({
          event: data.type,
          timestamp: Date.now(),
          payload: data.payload
        });
      }

      // Update user behavior patterns
      const behaviorKey = data.type || 'unknown';
      const currentCount = this.analyticsCollector.userBehavior.get(behaviorKey) || 0;
      this.analyticsCollector.userBehavior.set(behaviorKey, currentCount + 1);

    } catch (error) {
      console.warn('[SubscriptionWebSocketService] Analytics tracking failed:', error);
    }
  }

  /**
   * Update throughput metrics
   *
   * @private
   */
  _updateThroughputMetrics() {
    const now = Date.now();
    const timeDiff = now - this.performanceMetrics.throughput.lastMeasurement;

    this.performanceMetrics.throughput.messageCount++;

    // Calculate messages per second every 10 seconds
    if (timeDiff >= 10000) {
      this.performanceMetrics.throughput.messagesPerSecond =
        this.performanceMetrics.throughput.messageCount / (timeDiff / 1000);

      this.performanceMetrics.throughput.lastMeasurement = now;
      this.performanceMetrics.throughput.messageCount = 0;
    }
  }

  /**
   * Initialize analytics session
   *
   * @private
   */
  _initializeAnalyticsSession() {
    this.analyticsCollector.engagementMetrics.sessionDuration = Date.now();

    // Record session start
    this._recordMetric('session_started', 'analytics', {
      session_id: this.analyticsCollector.sessionId,
      user_id: this.userId
    });
  }

  /**
   * Generate unique connection ID
   *
   * @private
   * @returns {string} Connection ID
   */
  _generateConnectionId() {
    return `sub_conn_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Generate unique session ID
   *
   * @private
   * @returns {string} Session ID
   */
  _generateSessionId() {
    return `sub_session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Handle platform configuration updates from real-time service
   *
   * @private
   * @param {Object} updateData - Configuration update data
   */
  _handlePlatformConfigurationUpdate(updateData) {
    try {
      console.log('[SubscriptionWebSocketService] Platform configuration update received:', updateData);

      // Update local configuration
      if (updateData.version) {
        this.configurationManager.currentVersion = updateData.version;
        this.configurationManager.lastUpdate = Date.now();
      }

      // Apply dynamic settings
      if (updateData.settings) {
        Object.entries(updateData.settings).forEach(([key, value]) => {
          this.configurationManager.dynamicSettings.set(key, value);
        });
      }

      // Record configuration update
      this._recordMetric('configuration_updated', 'platform_service', {
        version: updateData.version,
        settings_count: Object.keys(updateData.settings || {}).length
      });

    } catch (error) {
      console.warn('[SubscriptionWebSocketService] Platform configuration update failed:', error);
    }
  }

  /**
   * Determine subscription change type for analytics
   *
   * @private
   * @param {string} oldPlan - Previous subscription plan
   * @param {string} newPlan - New subscription plan
   * @returns {string} Change type (upgrade, downgrade, lateral)
   */
  _getSubscriptionChangeType(oldPlan, newPlan) {
    const planHierarchy = {
      'creator': 1,
      'accelerator': 2,
      'dominator': 3
    };

    const oldLevel = planHierarchy[oldPlan?.toLowerCase()] || 0;
    const newLevel = planHierarchy[newPlan?.toLowerCase()] || 0;

    if (newLevel > oldLevel) return 'upgrade';
    if (newLevel < oldLevel) return 'downgrade';
    return 'lateral';
  }

  /**
   * Determine upgrade type for analytics
   *
   * @private
   * @param {string} oldPlan - Previous plan
   * @param {string} newPlan - New plan
   * @returns {string} Upgrade type
   */
  _getUpgradeType(oldPlan, newPlan) {
    const changeType = this._getSubscriptionChangeType(oldPlan, newPlan);

    if (changeType === 'upgrade') {
      if (oldPlan?.toLowerCase() === 'creator' && newPlan?.toLowerCase() === 'dominator') {
        return 'skip_tier_upgrade';
      }
      return 'standard_upgrade';
    }

    return changeType;
  }

  /**
   * Fix window.showNotification calls to use proper notification system
   *
   * @private
   * @param {Object} notificationData - Notification configuration
   */
  _showNotification(notificationData) {
    try {
      // Try to use the global notification system
      if (typeof window !== 'undefined' && typeof window['showNotification'] === 'function') {
        window['showNotification'](notificationData);
        return;
      }

      // Fallback to browser notification API
      if (typeof window !== 'undefined' && 'Notification' in window && Notification.permission === 'granted') {
        new Notification(notificationData.title, {
          body: notificationData.message,
          icon: '/favicon.ico'
        });
        return;
      }

      // Fallback to console log for development
      console.log(`[Notification] ${notificationData.title}: ${notificationData.message}`);
    } catch (error) {
      console.warn('[SubscriptionWebSocketService] Notification display failed:', error);
    }
  }
}

// Create singleton instance
const subscriptionWebSocketService = new SubscriptionWebSocketService();

export default subscriptionWebSocketService;
