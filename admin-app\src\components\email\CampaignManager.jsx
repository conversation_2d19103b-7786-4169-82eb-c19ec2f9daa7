import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Alert,
  Skeleton
} from '@mui/material';
import {
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Stop as StopIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Analytics as AnalyticsIcon,
  Schedule as ScheduleIcon,
  Email as EmailIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';

const CampaignManager = ({ 
  campaigns = [], 
  templates = [], 
  loading = false 
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedCampaign, setSelectedCampaign] = useState(null);

  const handleMenuOpen = (event, campaign) => {
    setAnchorEl(event.currentTarget);
    setSelectedCampaign(campaign);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedCampaign(null);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'running':
        return 'success';
      case 'scheduled':
        return 'info';
      case 'paused':
        return 'warning';
      case 'completed':
        return 'default';
      case 'cancelled':
        return 'error';
      case 'draft':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'running':
        return <PlayIcon fontSize="small" />;
      case 'scheduled':
        return <ScheduleIcon fontSize="small" />;
      case 'paused':
        return <PauseIcon fontSize="small" />;
      case 'completed':
        return <TrendingUpIcon fontSize="small" />;
      case 'cancelled':
        return <StopIcon fontSize="small" />;
      default:
        return <EmailIcon fontSize="small" />;
    }
  };

  const calculateOpenRate = (campaign) => {
    if (campaign.sent_count === 0) return 0;
    return ((campaign.opened_count / campaign.sent_count) * 100).toFixed(1);
  };

  const calculateClickRate = (campaign) => {
    if (campaign.sent_count === 0) return 0;
    return ((campaign.clicked_count / campaign.sent_count) * 100).toFixed(1);
  };

  const renderSkeleton = () => (
    <Box>
      {[...Array(5)].map((_, index) => (
        <Card key={index} sx={{ mb: 2 }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box sx={{ flex: 1 }}>
                <Skeleton variant="text" width="40%" height={32} />
                <Skeleton variant="text" width="60%" height={20} sx={{ mt: 1 }} />
              </Box>
              <Skeleton variant="circular" width={32} height={32} />
            </Box>
            <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
              <Skeleton variant="rounded" width={80} height={24} />
              <Skeleton variant="rounded" width={100} height={24} />
            </Box>
          </CardContent>
        </Card>
      ))}
    </Box>
  );

  const renderEmptyState = () => (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        py: 8,
        textAlign: 'center'
      }}
    >
      {/* Friendly illustration */}
      <Box
        sx={{
          fontSize: '4rem',
          mb: 2,
          opacity: 0.6,
          filter: 'grayscale(20%)'
        }}
      >
        📢
      </Box>

      <Typography variant="h6" color="text.secondary" gutterBottom>
        No email campaigns yet
      </Typography>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 3, maxWidth: 400 }}>
        Start engaging with your audience by creating your first email campaign.
        You can schedule newsletters, announcements, and automated email sequences.
      </Typography>

      <Button
        variant="contained"
        startIcon={<AddIcon />}
        sx={{
          background: 'linear-gradient(135deg, #6C4BFA 0%, #9B85FF 100%)',
          boxShadow: '0 8px 32px 0 rgba(108, 75, 250, 0.3)',
          '&:hover': {
            background: 'linear-gradient(135deg, #5A3FE8 0%, #8973ED 100%)',
            transform: 'translateY(-2px)',
            boxShadow: '0 12px 40px 0 rgba(108, 75, 250, 0.4)',
          }
        }}
      >
        Create Your First Campaign
      </Button>
    </Box>
  );

  const renderCampaignStats = () => (
    <Grid container spacing={3} sx={{ mb: 3 }}>
      <Grid item xs={12} sm={6} md={3}>
        <Card variant="glass">
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <EmailIcon color="primary" sx={{ mr: 1 }} />
              <Typography variant="h6">Total Campaigns</Typography>
            </Box>
            <Typography variant="h4" color="primary">
              {campaigns.length}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <Card variant="glass">
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <PlayIcon color="success" sx={{ mr: 1 }} />
              <Typography variant="h6">Active</Typography>
            </Box>
            <Typography variant="h4" color="success.main">
              {campaigns.filter(c => c.status === 'running').length}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <Card variant="glass">
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <ScheduleIcon color="info" sx={{ mr: 1 }} />
              <Typography variant="h6">Scheduled</Typography>
            </Box>
            <Typography variant="h4" color="info.main">
              {campaigns.filter(c => c.status === 'scheduled').length}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <Card variant="glass">
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <TrendingUpIcon color="secondary" sx={{ mr: 1 }} />
              <Typography variant="h6">Completed</Typography>
            </Box>
            <Typography variant="h4" color="secondary.main">
              {campaigns.filter(c => c.status === 'completed').length}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  if (loading) {
    return renderSkeleton();
  }

  if (campaigns.length === 0) {
    return renderEmptyState();
  }

  return (
    <Box>
      {/* Campaign Stats */}
      {renderCampaignStats()}

      {/* Create Campaign Button */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          Email Campaigns
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          sx={{
            background: 'linear-gradient(135deg, #6C4BFA 0%, #9B85FF 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #5A3FE8 0%, #8973ED 100%)',
            }
          }}
        >
          Create Campaign
        </Button>
      </Box>

      {/* Campaigns Table */}
      <TableContainer component={Paper} variant="glass" sx={{ borderRadius: 3 }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Campaign</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Template</TableCell>
              <TableCell align="right">Recipients</TableCell>
              <TableCell align="right">Sent</TableCell>
              <TableCell align="right">Open Rate</TableCell>
              <TableCell align="right">Click Rate</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {campaigns.map((campaign) => (
              <TableRow key={campaign.id} hover>
                <TableCell>
                  <Box>
                    <Typography variant="subtitle2" fontWeight={600}>
                      {campaign.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {campaign.description}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    icon={getStatusIcon(campaign.status)}
                    label={campaign.status}
                    color={getStatusColor(campaign.status)}
                    size="small"
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {templates.find(t => t.id === campaign.template_id)?.name || 'Unknown'}
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <Typography variant="body2">
                    {campaign.recipient_count.toLocaleString()}
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <Box>
                    <Typography variant="body2">
                      {campaign.sent_count.toLocaleString()}
                    </Typography>
                    {campaign.recipient_count > 0 && (
                      <LinearProgress
                        variant="determinate"
                        value={(campaign.sent_count / campaign.recipient_count) * 100}
                        sx={{ mt: 0.5, height: 4, borderRadius: 2 }}
                      />
                    )}
                  </Box>
                </TableCell>
                <TableCell align="right">
                  <Typography variant="body2" color="success.main">
                    {calculateOpenRate(campaign)}%
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <Typography variant="body2" color="info.main">
                    {calculateClickRate(campaign)}%
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <IconButton
                    size="small"
                    onClick={(e) => handleMenuOpen(e, campaign)}
                  >
                    <MoreVertIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: {
            borderRadius: 2,
            minWidth: 160,
            boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.2)',
          }
        }}
      >
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit Campaign</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <AnalyticsIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Analytics</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <PlayIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Start Campaign</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <PauseIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Pause Campaign</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleMenuClose} sx={{ color: 'error.main' }}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete Campaign</ListItemText>
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default CampaignManager;
