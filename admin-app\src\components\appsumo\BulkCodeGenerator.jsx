import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Alert,
  LinearProgress,
  Chip,
  IconButton,
  Tooltip,
  <PERSON>per,
  Step,
  Step<PERSON>abel,
  StepContent,
} from '@mui/material';
import {
  Close as CloseIcon,
  Download as DownloadIcon,
  ContentCopy as CopyIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { validateFormData, generateAppSumoCode, exportToCSV, copyToClipboard } from '../../utils/appsumoHelpers';
import api from '../../api';

/**
 * Bulk Code Generator Component
 * Allows generation of multiple AppSumo codes with validation and export
 */
const BulkCodeGenerator = ({ 
  open, 
  onClose, 
  deals = [], 
  tiers = [],
  onCodesGenerated,
  ...props 
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    deal_id: '',
    tier_type: '',
    quantity: 10,
    prefix: 'AS',
    code_length: 8,
    expires_at: '',
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [generatedCodes, setGeneratedCodes] = useState([]);
  const [progress, setProgress] = useState(0);

  const steps = [
    'Configure Generation',
    'Generate Codes',
    'Review & Export',
  ];

  // Form validation rules
  const validationRules = [
    { key: 'deal_id', label: 'Deal', type: 'string' },
    { key: 'tier_type', label: 'Tier Type', type: 'string' },
    { key: 'quantity', label: 'Quantity', type: 'number', min: 1, max: 1000 },
    { key: 'prefix', label: 'Prefix', type: 'string', minLength: 2, maxLength: 5 },
    { key: 'code_length', label: 'Code Length', type: 'number', min: 6, max: 20 },
  ];

  // Handle form field changes
  const handleFieldChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  // Validate current step
  const validateStep = (step) => {
    if (step === 0) {
      const validation = validateFormData(formData, validationRules);
      setErrors(validation.errors);
      return validation.isValid;
    }
    return true;
  };

  // Handle next step
  const handleNext = () => {
    if (validateStep(activeStep)) {
      if (activeStep === 0) {
        generateCodes();
      } else {
        setActiveStep(prev => prev + 1);
      }
    }
  };

  // Handle previous step
  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  // Generate codes
  const generateCodes = async () => {
    setLoading(true);
    setProgress(0);
    setActiveStep(1);
    
    try {
      const codes = [];
      const batchSize = 50; // Generate in batches for better UX
      const totalBatches = Math.ceil(formData.quantity / batchSize);
      
      for (let batch = 0; batch < totalBatches; batch++) {
        const batchStart = batch * batchSize;
        const batchEnd = Math.min(batchStart + batchSize, formData.quantity);
        const batchCodes = [];
        
        // Generate codes for this batch
        for (let i = batchStart; i < batchEnd; i++) {
          const code = generateAppSumoCode(formData.prefix, formData.code_length);
          batchCodes.push({
            code,
            deal_id: formData.deal_id,
            tier_type: formData.tier_type,
            expires_at: formData.expires_at || null,
            status: 'active',
            is_redeemed: false,
            created_at: new Date().toISOString(),
          });
        }
        
        // Send batch to API
        const response = await api.post('/api/appsumo/bulk-generate-codes', {
          codes: batchCodes,
          deal_id: formData.deal_id,
          tier_type: formData.tier_type,
        });
        
        codes.push(...response.data.codes);
        
        // Update progress
        const progressPercent = ((batch + 1) / totalBatches) * 100;
        setProgress(progressPercent);
        
        // Small delay for UX
        if (batch < totalBatches - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }
      
      setGeneratedCodes(codes);
      setActiveStep(2);
      
      // Notify parent component
      if (onCodesGenerated) {
        onCodesGenerated(codes);
      }
      
    } catch (error) {
      console.error('Error generating codes:', error);
      setErrors({ 
        generation: error.response?.data?.detail || 'Failed to generate codes' 
      });
      setActiveStep(0);
    } finally {
      setLoading(false);
    }
  };

  // Handle export
  const handleExport = () => {
    const exportColumns = [
      { key: 'code', label: 'Code' },
      { key: 'deal_id', label: 'Deal ID' },
      { key: 'tier_type', label: 'Tier Type' },
      { key: 'status', label: 'Status' },
      { key: 'created_at', label: 'Created At', type: 'date' },
      { key: 'expires_at', label: 'Expires At', type: 'date' },
    ];
    
    exportToCSV(
      generatedCodes, 
      `appsumo-codes-${formData.deal_id}-${Date.now()}`, 
      exportColumns
    );
  };

  // Handle copy all codes
  const handleCopyAll = async () => {
    const codesList = generatedCodes.map(code => code.code).join('\n');
    const success = await copyToClipboard(codesList);
    
    if (success) {
      // Could show a snackbar here
      console.log('Codes copied to clipboard');
    }
  };

  // Handle close
  const handleClose = () => {
    setActiveStep(0);
    setFormData({
      deal_id: '',
      tier_type: '',
      quantity: 10,
      prefix: 'AS',
      code_length: 8,
      expires_at: '',
    });
    setErrors({});
    setGeneratedCodes([]);
    setProgress(0);
    onClose();
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          minHeight: 500,
        }
      }}
      {...props}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">Bulk Code Generator</Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Stepper activeStep={activeStep} orientation="vertical">
          {/* Step 1: Configure Generation */}
          <Step>
            <StepLabel>Configure Generation</StepLabel>
            <StepContent>
              <Box display="flex" flexDirection="column" gap={3} mt={2}>
                {errors.generation && (
                  <Alert severity="error">{errors.generation}</Alert>
                )}
                
                <Box display="flex" gap={2}>
                  <FormControl fullWidth error={!!errors.deal_id}>
                    <InputLabel>Deal</InputLabel>
                    <Select
                      value={formData.deal_id}
                      onChange={(e) => handleFieldChange('deal_id', e.target.value)}
                      label="Deal"
                    >
                      {deals.map((deal) => (
                        <MenuItem key={deal.id} value={deal.deal_id}>
                          {deal.name} ({deal.deal_id})
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <FormControl fullWidth error={!!errors.tier_type}>
                    <InputLabel>Tier Type</InputLabel>
                    <Select
                      value={formData.tier_type}
                      onChange={(e) => handleFieldChange('tier_type', e.target.value)}
                      label="Tier Type"
                    >
                      {tiers.map((tier) => (
                        <MenuItem key={tier.id} value={tier.tier_type}>
                          {tier.name} ({tier.tier_type})
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>

                <Box display="flex" gap={2}>
                  <TextField
                    label="Quantity"
                    type="number"
                    value={formData.quantity}
                    onChange={(e) => handleFieldChange('quantity', parseInt(e.target.value) || 0)}
                    error={!!errors.quantity}
                    helperText={errors.quantity || 'Number of codes to generate (1-1000)'}
                    inputProps={{ min: 1, max: 1000 }}
                    fullWidth
                  />

                  <TextField
                    label="Prefix"
                    value={formData.prefix}
                    onChange={(e) => handleFieldChange('prefix', e.target.value.toUpperCase())}
                    error={!!errors.prefix}
                    helperText={errors.prefix || 'Code prefix (2-5 characters)'}
                    inputProps={{ maxLength: 5 }}
                    fullWidth
                  />

                  <TextField
                    label="Code Length"
                    type="number"
                    value={formData.code_length}
                    onChange={(e) => handleFieldChange('code_length', parseInt(e.target.value) || 8)}
                    error={!!errors.code_length}
                    helperText={errors.code_length || 'Length after prefix (6-20)'}
                    inputProps={{ min: 6, max: 20 }}
                    fullWidth
                  />
                </Box>

                <TextField
                  label="Expiration Date (Optional)"
                  type="datetime-local"
                  value={formData.expires_at}
                  onChange={(e) => handleFieldChange('expires_at', e.target.value)}
                  InputLabelProps={{ shrink: true }}
                  helperText="Leave empty for no expiration"
                  fullWidth
                />

                <Box mt={2}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Preview: {formData.prefix}-{Array(formData.code_length).fill('X').join('')}
                  </Typography>
                </Box>
              </Box>
            </StepContent>
          </Step>

          {/* Step 2: Generate Codes */}
          <Step>
            <StepLabel>Generate Codes</StepLabel>
            <StepContent>
              <Box py={3}>
                <Typography variant="body1" gutterBottom>
                  Generating {formData.quantity} codes...
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={progress} 
                  sx={{ mt: 2, height: 8, borderRadius: 4 }}
                />
                <Typography variant="body2" color="text.secondary" mt={1}>
                  {Math.round(progress)}% complete
                </Typography>
              </Box>
            </StepContent>
          </Step>

          {/* Step 3: Review & Export */}
          <Step>
            <StepLabel>Review & Export</StepLabel>
            <StepContent>
              <Box py={2}>
                <Alert severity="success" sx={{ mb: 2 }}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <CheckCircleIcon />
                    Successfully generated {generatedCodes.length} codes
                  </Box>
                </Alert>

                <Box display="flex" gap={2} mb={2}>
                  <Button
                    variant="outlined"
                    startIcon={<DownloadIcon />}
                    onClick={handleExport}
                  >
                    Export CSV
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<CopyIcon />}
                    onClick={handleCopyAll}
                  >
                    Copy All
                  </Button>
                </Box>

                <Typography variant="body2" color="text.secondary">
                  Codes have been saved to the database and are ready for distribution.
                </Typography>
              </Box>
            </StepContent>
          </Step>
        </Stepper>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>
          {activeStep === 2 ? 'Close' : 'Cancel'}
        </Button>
        {activeStep > 0 && activeStep < 2 && (
          <Button onClick={handleBack} disabled={loading}>
            Back
          </Button>
        )}
        {activeStep === 0 && (
          <Button 
            onClick={handleNext} 
            variant="contained"
            disabled={loading}
          >
            Generate Codes
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default BulkCodeGenerator;
