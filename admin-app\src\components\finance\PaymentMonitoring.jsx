import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  IconButton,
  Tooltip,
  Alert,
  CircularProgress,
  LinearProgress,
} from '@mui/material';
import {
  Warning as WarningIcon,
  Error as ErrorIcon,
  CheckCircle as SuccessIcon,
  Refresh as RefreshIcon,
  PlayArrow as RetryIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';

const PaymentMonitoring = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Mock data for demonstration
  const paymentHealth = {
    success_rate: 0.94,
    failed_payments_today: 12,
    pending_retries: 8,
    total_failures_this_month: 156,
    average_retry_success_rate: 0.67,
  };

  const failedPayments = [
    {
      id: '1',
      user_email: '<EMAIL>',
      amount: 99.00,
      plan: 'Accelerator',
      failure_reason: 'insufficient_funds',
      retry_count: 2,
      next_retry: new Date(Date.now() + 24 * 60 * 60 * 1000),
      status: 'retrying',
      failed_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    },
    {
      id: '2',
      user_email: '<EMAIL>',
      amount: 19.00,
      plan: 'Creator',
      failure_reason: 'card_declined',
      retry_count: 1,
      next_retry: new Date(Date.now() + 12 * 60 * 60 * 1000),
      status: 'retrying',
      failed_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    },
    {
      id: '3',
      user_email: '<EMAIL>',
      amount: 249.00,
      plan: 'Dominator',
      failure_reason: 'expired_card',
      retry_count: 3,
      next_retry: null,
      status: 'abandoned',
      failed_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    },
  ];

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'retrying':
        return 'warning';
      case 'abandoned':
        return 'error';
      case 'resolved':
        return 'success';
      default:
        return 'default';
    }
  };

  const getFailureReasonDisplay = (reason) => {
    const reasons = {
      insufficient_funds: 'Insufficient Funds',
      card_declined: 'Card Declined',
      expired_card: 'Expired Card',
      invalid_card: 'Invalid Card',
      processing_error: 'Processing Error',
    };
    return reasons[reason] || reason;
  };

  const handleRetryPayment = (paymentId) => {
    // TODO: Implement retry payment functionality
    console.log('Retrying payment:', paymentId);
  };

  const handleViewDetails = (paymentId) => {
    // TODO: Implement view payment details
    console.log('Viewing payment details:', paymentId);
  };

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {loading && <LinearProgress sx={{ mb: 2 }} />}

      <Grid container spacing={3}>
        {/* Payment Health Overview */}
        <Grid item xs={12}>
          <Card>
            <CardHeader 
              title="Payment Health Overview"
              action={
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={() => {/* TODO: Implement refresh */}}
                  disabled={loading}
                >
                  Refresh
                </Button>
              }
            />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={2.4}>
                  <Box textAlign="center">
                    <Box display="flex" justifyContent="center" alignItems="center" mb={1}>
                      <SuccessIcon 
                        color={paymentHealth.success_rate >= 0.95 ? "success" : "warning"} 
                        sx={{ fontSize: 40 }} 
                      />
                    </Box>
                    <Typography variant="h4" color="primary">
                      {formatPercentage(paymentHealth.success_rate)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Success Rate
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={paymentHealth.success_rate * 100}
                      color={paymentHealth.success_rate >= 0.95 ? "success" : "warning"}
                      sx={{ mt: 1, height: 6, borderRadius: 3 }}
                    />
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={2.4}>
                  <Box textAlign="center">
                    <Box display="flex" justifyContent="center" alignItems="center" mb={1}>
                      <ErrorIcon color="error" sx={{ fontSize: 40 }} />
                    </Box>
                    <Typography variant="h4" color="primary">
                      {paymentHealth.failed_payments_today}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Failed Today
                    </Typography>
                    <Typography variant="caption" color="error.main">
                      {paymentHealth.total_failures_this_month} this month
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={2.4}>
                  <Box textAlign="center">
                    <Box display="flex" justifyContent="center" alignItems="center" mb={1}>
                      <WarningIcon color="warning" sx={{ fontSize: 40 }} />
                    </Box>
                    <Typography variant="h4" color="primary">
                      {paymentHealth.pending_retries}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Pending Retries
                    </Typography>
                    <Typography variant="caption" color="warning.main">
                      Automatic retry system
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={2.4}>
                  <Box textAlign="center">
                    <Box display="flex" justifyContent="center" alignItems="center" mb={1}>
                      <RefreshIcon color="info" sx={{ fontSize: 40 }} />
                    </Box>
                    <Typography variant="h4" color="primary">
                      {formatPercentage(paymentHealth.average_retry_success_rate)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Retry Success Rate
                    </Typography>
                    <Typography variant="caption" color="info.main">
                      Historical average
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={2.4}>
                  <Box textAlign="center">
                    <Box display="flex" justifyContent="center" alignItems="center" mb={1}>
                      <SuccessIcon color="success" sx={{ fontSize: 40 }} />
                    </Box>
                    <Typography variant="h4" color="primary">
                      Healthy
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      System Status
                    </Typography>
                    <Typography variant="caption" color="success.main">
                      All systems operational
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Failed Payments Table */}
        <Grid item xs={12}>
          <Card>
            <CardHeader 
              title="Failed Payments & Retries"
              subheader={`${failedPayments.length} payments requiring attention`}
            />
            <CardContent sx={{ p: 0 }}>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Customer</TableCell>
                      <TableCell>Plan</TableCell>
                      <TableCell align="right">Amount</TableCell>
                      <TableCell>Failure Reason</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Retry Count</TableCell>
                      <TableCell>Next Retry</TableCell>
                      <TableCell>Failed Date</TableCell>
                      <TableCell align="center">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {failedPayments.map((payment) => (
                      <TableRow key={payment.id} hover>
                        <TableCell>
                          <Typography variant="body2">
                            {payment.user_email}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={payment.plan}
                            color="primary"
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" fontWeight="bold">
                            {formatCurrency(payment.amount)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {getFailureReasonDisplay(payment.failure_reason)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={payment.status}
                            color={getStatusColor(payment.status)}
                            size="small"
                            sx={{ textTransform: 'capitalize' }}
                          />
                        </TableCell>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            <Typography variant="body2">
                              {payment.retry_count}/3
                            </Typography>
                            <LinearProgress
                              variant="determinate"
                              value={(payment.retry_count / 3) * 100}
                              sx={{ width: 40, height: 4 }}
                              color={payment.retry_count >= 3 ? "error" : "warning"}
                            />
                          </Box>
                        </TableCell>
                        <TableCell>
                          {payment.next_retry ? (
                            <Box>
                              <Typography variant="body2">
                                {format(payment.next_retry, 'MMM dd, yyyy')}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {format(payment.next_retry, 'HH:mm')}
                              </Typography>
                            </Box>
                          ) : (
                            <Typography variant="body2" color="text.secondary">
                              No retry scheduled
                            </Typography>
                          )}
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {format(payment.failed_at, 'MMM dd, yyyy')}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {format(payment.failed_at, 'HH:mm')}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <Box display="flex" gap={0.5}>
                            <Tooltip title="View Details">
                              <IconButton 
                                size="small" 
                                onClick={() => handleViewDetails(payment.id)}
                              >
                                <ViewIcon />
                              </IconButton>
                            </Tooltip>
                            {payment.status === 'retrying' && (
                              <Tooltip title="Retry Now">
                                <IconButton 
                                  size="small" 
                                  onClick={() => handleRetryPayment(payment.id)}
                                  color="warning"
                                >
                                  <RetryIcon />
                                </IconButton>
                              </Tooltip>
                            )}
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Payment Failure Insights */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Payment Failure Insights" />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Common Failure Reasons
                  </Typography>
                  <Box>
                    {[
                      { reason: 'Insufficient Funds', count: 45, percentage: 0.35 },
                      { reason: 'Card Declined', count: 32, percentage: 0.25 },
                      { reason: 'Expired Card', count: 28, percentage: 0.22 },
                      { reason: 'Invalid Card', count: 15, percentage: 0.12 },
                      { reason: 'Processing Error', count: 8, percentage: 0.06 },
                    ].map((item) => (
                      <Box key={item.reason} mb={2}>
                        <Box display="flex" justifyContent="space-between" alignItems="center" mb={0.5}>
                          <Typography variant="body2">{item.reason}</Typography>
                          <Typography variant="body2" fontWeight="bold">
                            {item.count} ({formatPercentage(item.percentage)})
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={item.percentage * 100}
                          sx={{ height: 6, borderRadius: 3 }}
                        />
                      </Box>
                    ))}
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Recommendations
                  </Typography>
                  <Box>
                    <Alert severity="info" sx={{ mb: 2 }}>
                      <Typography variant="body2" fontWeight="bold">
                        Dunning Management
                      </Typography>
                      <Typography variant="body2">
                        Consider implementing smart retry logic with increasing intervals for better success rates.
                      </Typography>
                    </Alert>
                    <Alert severity="warning" sx={{ mb: 2 }}>
                      <Typography variant="body2" fontWeight="bold">
                        Customer Communication
                      </Typography>
                      <Typography variant="body2">
                        Send proactive notifications to customers about upcoming payment failures.
                      </Typography>
                    </Alert>
                    <Alert severity="success">
                      <Typography variant="body2" fontWeight="bold">
                        Payment Methods
                      </Typography>
                      <Typography variant="body2">
                        Encourage customers to add backup payment methods to reduce failures.
                      </Typography>
                    </Alert>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default PaymentMonitoring;
