"""
Competitor Social Media Analytics Service

This service fetches and analyzes competitor data from social media platforms
using the existing social media API integrations.
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

import httpx
from bson import ObjectId

from app.core.config import settings
from app.models.competitor import Competitor, CompetitorSocialMedia
from app.services.social_media.factory import SocialMediaIntegrationFactory
from app.services.social_media.base import BaseSocialMediaIntegration
from app.core.database import get_database

logger = logging.getLogger(__name__)

@dataclass
class CompetitorMetrics:
    """Data class for competitor social media metrics."""
    platform: str
    account_name: str
    followers_count: int
    following_count: int
    posts_count: int
    engagement_rate: float
    avg_likes_per_post: float
    avg_comments_per_post: float
    avg_shares_per_post: float
    posting_frequency: str
    last_post_date: Optional[datetime]
    top_content_types: List[str]
    peak_posting_times: List[str]
    hashtag_usage: List[str]
    last_updated: datetime

@dataclass
class CompetitorComparison:
    """Data class for competitor comparison results."""
    competitor_id: str
    competitor_name: str
    metrics: List[CompetitorMetrics]
    overall_score: float
    strengths: List[str]
    weaknesses: List[str]
    recommendations: List[str]

class CompetitorAnalyticsService:
    """
    Service for fetching and analyzing competitor social media data.
    """
    
    def __init__(self):
        self.db = get_database()
        self.competitors_collection = self.db.competitors
        self.analytics_collection = self.db.competitor_analytics
        
    async def fetch_competitor_metrics(
        self, 
        competitor_id: str, 
        platforms: Optional[List[str]] = None
    ) -> List[CompetitorMetrics]:
        """
        Fetch social media metrics for a specific competitor.
        
        Args:
            competitor_id: The competitor's ID
            platforms: List of platforms to analyze (if None, analyze all)
            
        Returns:
            List of CompetitorMetrics for each platform
        """
        try:
            # Get competitor data
            competitor = await self.competitors_collection.find_one(
                {"_id": ObjectId(competitor_id)}
            )
            
            if not competitor:
                raise ValueError(f"Competitor {competitor_id} not found")
            
            metrics = []
            social_media_accounts = competitor.get("social_media", [])
            
            # Filter platforms if specified
            if platforms:
                social_media_accounts = [
                    acc for acc in social_media_accounts 
                    if acc.get("platform") in platforms
                ]
            
            # Fetch metrics for each platform
            for account in social_media_accounts:
                try:
                    platform_metrics = await self._fetch_platform_metrics(
                        account["platform"],
                        account["account_name"],
                        account["account_url"]
                    )
                    metrics.append(platform_metrics)
                except Exception as e:
                    logger.error(f"Failed to fetch metrics for {account['platform']}: {e}")
                    continue
            
            # Store metrics in database
            await self._store_competitor_metrics(competitor_id, metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error fetching competitor metrics: {e}")
            raise
    
    async def _fetch_platform_metrics(
        self, 
        platform: str, 
        account_name: str, 
        account_url: str
    ) -> CompetitorMetrics:
        """
        Fetch metrics for a specific platform account.
        
        This method uses public APIs and web scraping techniques to gather
        competitor data without requiring authentication.
        """
        try:
            if platform.lower() == "linkedin":
                return await self._fetch_linkedin_metrics(account_name, account_url)
            elif platform.lower() == "twitter":
                return await self._fetch_twitter_metrics(account_name, account_url)
            elif platform.lower() == "facebook":
                return await self._fetch_facebook_metrics(account_name, account_url)
            elif platform.lower() == "instagram":
                return await self._fetch_instagram_metrics(account_name, account_url)
            else:
                raise ValueError(f"Platform {platform} not supported")
                
        except Exception as e:
            logger.error(f"Error fetching {platform} metrics: {e}")
            # Return default metrics if API fails
            return CompetitorMetrics(
                platform=platform,
                account_name=account_name,
                followers_count=0,
                following_count=0,
                posts_count=0,
                engagement_rate=0.0,
                avg_likes_per_post=0.0,
                avg_comments_per_post=0.0,
                avg_shares_per_post=0.0,
                posting_frequency="unknown",
                last_post_date=None,
                top_content_types=[],
                peak_posting_times=[],
                hashtag_usage=[],
                last_updated=datetime.utcnow()
            )
    
    async def _fetch_linkedin_metrics(self, account_name: str, account_url: str) -> CompetitorMetrics:
        """Fetch LinkedIn company page metrics using LinkedIn API or web scraping."""
        async with httpx.AsyncClient() as client:
            try:
                # Use LinkedIn public API endpoints or web scraping
                # Note: This would require proper implementation based on LinkedIn's terms
                
                # For demo purposes, returning mock data
                # In production, implement actual LinkedIn data fetching
                return CompetitorMetrics(
                    platform="linkedin",
                    account_name=account_name,
                    followers_count=5000,  # Would be fetched from API
                    following_count=500,
                    posts_count=150,
                    engagement_rate=3.5,
                    avg_likes_per_post=25.0,
                    avg_comments_per_post=5.0,
                    avg_shares_per_post=3.0,
                    posting_frequency="weekly",
                    last_post_date=datetime.utcnow() - timedelta(days=2),
                    top_content_types=["article", "image", "video"],
                    peak_posting_times=["09:00", "13:00", "17:00"],
                    hashtag_usage=["#business", "#technology", "#innovation"],
                    last_updated=datetime.utcnow()
                )
            except Exception as e:
                logger.error(f"LinkedIn API error: {e}")
                raise
    
    async def _fetch_twitter_metrics(self, account_name: str, account_url: str) -> CompetitorMetrics:
        """Fetch Twitter metrics using Twitter API v2."""
        async with httpx.AsyncClient() as client:
            try:
                # Use Twitter API v2 with bearer token
                headers = {
                    "Authorization": f"Bearer {settings.TWITTER_BEARER_TOKEN}",
                    "Content-Type": "application/json"
                }
                
                # Get user by username
                username = account_name.replace("@", "")
                user_response = await client.get(
                    f"https://api.twitter.com/2/users/by/username/{username}",
                    headers=headers,
                    params={
                        "user.fields": "public_metrics,created_at,description,verified"
                    }
                )
                
                if user_response.status_code != 200:
                    raise Exception(f"Twitter API error: {user_response.text}")
                
                user_data = user_response.json()["data"]
                metrics = user_data["public_metrics"]
                
                # Get recent tweets for engagement analysis
                tweets_response = await client.get(
                    f"https://api.twitter.com/2/users/{user_data['id']}/tweets",
                    headers=headers,
                    params={
                        "tweet.fields": "public_metrics,created_at,context_annotations",
                        "max_results": 100
                    }
                )
                
                tweets_data = tweets_response.json().get("data", [])
                
                # Calculate engagement metrics
                total_engagement = sum(
                    tweet["public_metrics"]["like_count"] + 
                    tweet["public_metrics"]["retweet_count"] + 
                    tweet["public_metrics"]["reply_count"]
                    for tweet in tweets_data
                )
                
                avg_engagement = total_engagement / len(tweets_data) if tweets_data else 0
                engagement_rate = (avg_engagement / metrics["followers_count"]) * 100 if metrics["followers_count"] > 0 else 0
                
                return CompetitorMetrics(
                    platform="twitter",
                    account_name=account_name,
                    followers_count=metrics["followers_count"],
                    following_count=metrics["following_count"],
                    posts_count=metrics["tweet_count"],
                    engagement_rate=engagement_rate,
                    avg_likes_per_post=sum(t["public_metrics"]["like_count"] for t in tweets_data) / len(tweets_data) if tweets_data else 0,
                    avg_comments_per_post=sum(t["public_metrics"]["reply_count"] for t in tweets_data) / len(tweets_data) if tweets_data else 0,
                    avg_shares_per_post=sum(t["public_metrics"]["retweet_count"] for t in tweets_data) / len(tweets_data) if tweets_data else 0,
                    posting_frequency=self._calculate_posting_frequency(tweets_data),
                    last_post_date=datetime.fromisoformat(tweets_data[0]["created_at"].replace("Z", "+00:00")) if tweets_data else None,
                    top_content_types=self._analyze_content_types(tweets_data),
                    peak_posting_times=self._analyze_posting_times(tweets_data),
                    hashtag_usage=self._extract_hashtags(tweets_data),
                    last_updated=datetime.utcnow()
                )
                
            except Exception as e:
                logger.error(f"Twitter API error: {e}")
                raise
    
    async def _fetch_facebook_metrics(self, account_name: str, account_url: str) -> CompetitorMetrics:
        """Fetch Facebook page metrics using Facebook Graph API."""
        # Implementation would use Facebook Graph API
        # For now, returning mock data
        return CompetitorMetrics(
            platform="facebook",
            account_name=account_name,
            followers_count=10000,
            following_count=0,  # Facebook pages don't have following
            posts_count=200,
            engagement_rate=4.2,
            avg_likes_per_post=50.0,
            avg_comments_per_post=8.0,
            avg_shares_per_post=5.0,
            posting_frequency="daily",
            last_post_date=datetime.utcnow() - timedelta(days=1),
            top_content_types=["image", "video", "link"],
            peak_posting_times=["10:00", "14:00", "19:00"],
            hashtag_usage=["#marketing", "#business", "#socialmedia"],
            last_updated=datetime.utcnow()
        )
    
    async def _fetch_instagram_metrics(self, account_name: str, account_url: str) -> CompetitorMetrics:
        """Fetch Instagram metrics using Instagram Basic Display API."""
        # Implementation would use Instagram Basic Display API
        # For now, returning mock data
        return CompetitorMetrics(
            platform="instagram",
            account_name=account_name,
            followers_count=15000,
            following_count=1000,
            posts_count=300,
            engagement_rate=5.8,
            avg_likes_per_post=150.0,
            avg_comments_per_post=20.0,
            avg_shares_per_post=10.0,
            posting_frequency="daily",
            last_post_date=datetime.utcnow() - timedelta(hours=12),
            top_content_types=["image", "video", "story"],
            peak_posting_times=["08:00", "12:00", "20:00"],
            hashtag_usage=["#lifestyle", "#brand", "#photography"],
            last_updated=datetime.utcnow()
        )
    
    def _calculate_posting_frequency(self, posts: List[Dict]) -> str:
        """Calculate posting frequency based on recent posts."""
        if not posts:
            return "unknown"
        
        # Analyze posting patterns
        post_dates = [datetime.fromisoformat(post["created_at"].replace("Z", "+00:00")) for post in posts]
        post_dates.sort(reverse=True)
        
        if len(post_dates) < 2:
            return "unknown"
        
        # Calculate average time between posts
        time_diffs = [(post_dates[i] - post_dates[i+1]).total_seconds() for i in range(len(post_dates)-1)]
        avg_diff = sum(time_diffs) / len(time_diffs)
        
        # Convert to frequency
        if avg_diff < 86400:  # Less than 1 day
            return "daily"
        elif avg_diff < 604800:  # Less than 1 week
            return "weekly"
        elif avg_diff < 2592000:  # Less than 1 month
            return "monthly"
        else:
            return "rarely"
    
    def _analyze_content_types(self, posts: List[Dict]) -> List[str]:
        """Analyze the types of content posted."""
        # This would analyze post content to determine types
        # For now, returning common types
        return ["text", "image", "video"]
    
    def _analyze_posting_times(self, posts: List[Dict]) -> List[str]:
        """Analyze peak posting times."""
        if not posts:
            return []
        
        hours = [datetime.fromisoformat(post["created_at"].replace("Z", "+00:00")).hour for post in posts]
        hour_counts = {}
        
        for hour in hours:
            hour_counts[hour] = hour_counts.get(hour, 0) + 1
        
        # Get top 3 posting hours
        top_hours = sorted(hour_counts.items(), key=lambda x: x[1], reverse=True)[:3]
        return [f"{hour:02d}:00" for hour, _ in top_hours]
    
    def _extract_hashtags(self, posts: List[Dict]) -> List[str]:
        """Extract commonly used hashtags."""
        # This would analyze post text for hashtags
        # For now, returning mock hashtags
        return ["#business", "#marketing", "#socialmedia"]
    
    async def _store_competitor_metrics(self, competitor_id: str, metrics: List[CompetitorMetrics]):
        """Store competitor metrics in the database."""
        try:
            # Convert metrics to dict for storage
            metrics_data = [
                {
                    "platform": m.platform,
                    "account_name": m.account_name,
                    "followers_count": m.followers_count,
                    "following_count": m.following_count,
                    "posts_count": m.posts_count,
                    "engagement_rate": m.engagement_rate,
                    "avg_likes_per_post": m.avg_likes_per_post,
                    "avg_comments_per_post": m.avg_comments_per_post,
                    "avg_shares_per_post": m.avg_shares_per_post,
                    "posting_frequency": m.posting_frequency,
                    "last_post_date": m.last_post_date,
                    "top_content_types": m.top_content_types,
                    "peak_posting_times": m.peak_posting_times,
                    "hashtag_usage": m.hashtag_usage,
                    "last_updated": m.last_updated
                }
                for m in metrics
            ]
            
            # Upsert analytics data
            await self.analytics_collection.update_one(
                {"competitor_id": competitor_id},
                {
                    "$set": {
                        "competitor_id": competitor_id,
                        "metrics": metrics_data,
                        "last_updated": datetime.utcnow()
                    }
                },
                upsert=True
            )
            
        except Exception as e:
            logger.error(f"Error storing competitor metrics: {e}")
            raise
    
    async def compare_competitors(
        self, 
        competitor_ids: List[str], 
        platforms: Optional[List[str]] = None
    ) -> List[CompetitorComparison]:
        """
        Compare multiple competitors across platforms.
        
        Args:
            competitor_ids: List of competitor IDs to compare
            platforms: List of platforms to compare (if None, compare all)
            
        Returns:
            List of CompetitorComparison objects
        """
        try:
            comparisons = []
            
            for competitor_id in competitor_ids:
                # Fetch metrics for this competitor
                metrics = await self.fetch_competitor_metrics(competitor_id, platforms)
                
                # Get competitor name
                competitor = await self.competitors_collection.find_one(
                    {"_id": ObjectId(competitor_id)}
                )
                competitor_name = competitor.get("name", "Unknown") if competitor else "Unknown"
                
                # Calculate overall score and insights
                overall_score = self._calculate_overall_score(metrics)
                strengths, weaknesses = self._analyze_strengths_weaknesses(metrics)
                recommendations = self._generate_recommendations(metrics)
                
                comparison = CompetitorComparison(
                    competitor_id=competitor_id,
                    competitor_name=competitor_name,
                    metrics=metrics,
                    overall_score=overall_score,
                    strengths=strengths,
                    weaknesses=weaknesses,
                    recommendations=recommendations
                )
                
                comparisons.append(comparison)
            
            return comparisons
            
        except Exception as e:
            logger.error(f"Error comparing competitors: {e}")
            raise
    
    def _calculate_overall_score(self, metrics: List[CompetitorMetrics]) -> float:
        """Calculate an overall performance score for a competitor."""
        if not metrics:
            return 0.0
        
        total_score = 0.0
        for metric in metrics:
            # Weight different factors
            platform_score = (
                (metric.engagement_rate * 0.4) +
                (min(metric.followers_count / 10000, 10) * 0.3) +  # Normalize followers
                (metric.avg_likes_per_post / 100 * 0.2) +  # Normalize likes
                (len(metric.top_content_types) * 0.1)  # Content diversity
            )
            total_score += platform_score
        
        return min(total_score / len(metrics), 10.0)  # Cap at 10
    
    def _analyze_strengths_weaknesses(self, metrics: List[CompetitorMetrics]) -> Tuple[List[str], List[str]]:
        """Analyze competitor strengths and weaknesses."""
        strengths = []
        weaknesses = []
        
        for metric in metrics:
            if metric.engagement_rate > 5.0:
                strengths.append(f"High engagement rate on {metric.platform} ({metric.engagement_rate:.1f}%)")
            elif metric.engagement_rate < 2.0:
                weaknesses.append(f"Low engagement rate on {metric.platform} ({metric.engagement_rate:.1f}%)")
            
            if metric.followers_count > 10000:
                strengths.append(f"Large following on {metric.platform} ({metric.followers_count:,} followers)")
            elif metric.followers_count < 1000:
                weaknesses.append(f"Small following on {metric.platform} ({metric.followers_count:,} followers)")
            
            if metric.posting_frequency in ["daily", "weekly"]:
                strengths.append(f"Consistent posting on {metric.platform} ({metric.posting_frequency})")
            elif metric.posting_frequency in ["rarely", "unknown"]:
                weaknesses.append(f"Inconsistent posting on {metric.platform}")
        
        return strengths, weaknesses
    
    def _generate_recommendations(self, metrics: List[CompetitorMetrics]) -> List[str]:
        """Generate recommendations based on competitor analysis."""
        recommendations = []
        
        for metric in metrics:
            if metric.engagement_rate < 3.0:
                recommendations.append(f"Improve content quality on {metric.platform} to increase engagement")
            
            if metric.posting_frequency in ["rarely", "unknown"]:
                recommendations.append(f"Establish consistent posting schedule on {metric.platform}")
            
            if len(metric.top_content_types) < 2:
                recommendations.append(f"Diversify content types on {metric.platform}")
            
            if not metric.hashtag_usage:
                recommendations.append(f"Use relevant hashtags on {metric.platform} to increase discoverability")
        
        return recommendations
