/**
 * Enhanced WebSocket Service v2.0.0
 * Enterprise-grade WebSocket management with Enhanced Platform Service integration
 *
 * This service provides comprehensive WebSocket functionality with:
 * - Enhanced security with device fingerprinting and token blacklist validation
 * - Prometheus metrics integration for comprehensive monitoring
 * - Circuit breaker pattern for resilience and fault tolerance
 * - Message deduplication service for reliability
 * - Real-time configuration updates from Enhanced Platform Service v2.0.0
 * - Subscription-based feature gating using ACE Social tier structure
 * - Advanced error handling and recovery mechanisms
 * - Intelligent connection management with load balancing
 * - Comprehensive audit logging with security context
 * - Cross-tab synchronization with security validation
 *
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 * @since 2024-01-01
 *
 * @example
 * ```javascript
 * import webSocketService from './services/WebSocketService';
 *
 * // Initialize enhanced WebSocket service
 * await webSocketService.initialize();
 *
 * // Connect with enhanced security
 * await webSocketService.connect('/api/v1/ws', true);
 *
 * // Send message with deduplication
 * await webSocketService.sendMessage('command', { action: 'refresh' });
 * ```
 */

import { v4 as uuidv4 } from 'uuid';
import { EventEmitter } from 'events';

// Enhanced Platform Service v2.0.0 Integration
import platformService from './platformService';
import { getDeviceFingerprint } from './fingerprint';
import { PrometheusMetricsCollector } from '../utils/PrometheusMetricsCollector';
import tokenManager from './tokenManager';

// WebSocket message types
export const MessageType = {
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  ERROR: 'error',
  MESSAGE: 'message',
  NOTIFICATION: 'notification',
  PING: 'ping',
  PONG: 'pong',
  SUBSCRIBE: 'subscribe',
  UNSUBSCRIBE: 'unsubscribe',
  UPDATE: 'update',
  SYNC: 'sync',
  COMMAND: 'command',
  RESPONSE: 'response',
  BROADCAST: 'broadcast',
  SYSTEM: 'system'
};

// WebSocket error codes
export const ErrorCode = {
  AUTHENTICATION_FAILED: 'authentication_failed',
  AUTHORIZATION_FAILED: 'authorization_failed',
  INVALID_MESSAGE: 'invalid_message',
  INVALID_SUBSCRIPTION: 'invalid_subscription',
  RATE_LIMIT_EXCEEDED: 'rate_limit_exceeded',
  SERVER_ERROR: 'server_error',
  CONNECTION_ERROR: 'connection_error',
  TIMEOUT: 'timeout',
  UNKNOWN: 'unknown'
};

/**
 * Enhanced WebSocket Service Class
 * Enterprise-grade WebSocket management with Enhanced Platform Service v2.0.0 integration
 */
class EnhancedWebSocketService extends EventEmitter {
  /**
   * Create an enhanced WebSocket service with enterprise features
   */
  constructor() {
    super();

    // Enhanced Platform Service v2.0.0 Integration
    this.platformService = platformService;
    this.metricsCollector = null;
    this.deviceFingerprint = null;

    // Enhanced configuration
    this.config = {
      enableEncryption: process.env.NODE_ENV === 'production' || process.env.REACT_APP_WS_ENCRYPTION_ENABLED === 'true',
      enableMetrics: process.env.NODE_ENV === 'production' || process.env.REACT_APP_METRICS_ENABLED === 'true',
      enableSecurityFingerprinting: true,
      enableRealTimeUpdates: true,
      enableMessageDeduplication: true,
      enableCircuitBreaker: true,
      enableSubscriptionGating: true
    };

    // WebSocket connection
    this.socket = null;

    // Connection status
    this.connected = false;
    this.connecting = false;
    this.authenticated = false;

    // Connection ID and metadata
    this.connectionId = null;
    this.sessionId = this._generateSessionId();

    // User context with enhanced security
    this.userId = null;
    this.userEmail = null;
    this.subscriptionTier = 'creator'; // Default tier

    // Client ID (persistent across reconnects)
    this.clientId = localStorage.getItem('websocket_client_id') || uuidv4();
    localStorage.setItem('websocket_client_id', this.clientId);

    // Enhanced Security Configuration
    this.securityConfig = {
      deviceFingerprint: null,
      platformFingerprint: null,
      fingerprintingEnabled: this.config.enableSecurityFingerprinting,
      lastSecurityCheck: null,
      securityValidationEnabled: true,
      tokenBlacklistEnabled: true
    };

    // Circuit Breaker Configuration
    this.circuitBreaker = {
      state: 'CLOSED', // CLOSED, OPEN, HALF_OPEN
      failureCount: 0,
      failureThreshold: 5,
      resetTimeout: 60000, // 1 minute
      lastFailureTime: null,
      nextAttemptTime: null
    };

    // Performance Metrics
    this.performanceMetrics = {
      connectionAttempts: 0,
      successfulConnections: 0,
      messagesSent: 0,
      messagesReceived: 0,
      duplicatesDetected: 0,
      securityViolations: 0,
      circuitBreakerTrips: 0,
      averageLatency: 0,
      totalLatency: 0,
      latencyMeasurements: 0
    };

    // Message Deduplication Service
    this.deduplicationService = {
      enabled: this.config.enableMessageDeduplication,
      messageCache: new Map(),
      cacheTTL: 300000, // 5 minutes
      maxCacheSize: 1000
    };

    // Real-time Configuration Management
    this.configurationManager = {
      enabled: this.config.enableRealTimeUpdates,
      currentVersion: '2.0.0',
      lastUpdate: null,
      dynamicSettings: new Map(),
      configurationCache: new Map()
    };

    // Event listeners
    this.eventListeners = new Map();

    // Reconnection settings with enhanced logic
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;
    this.reconnectInterval = 1000; // Start with 1 second
    this.maxReconnectInterval = 30000; // Max 30 seconds
    this.reconnectTimer = null;

    // Message handlers
    this.messageHandlers = new Map();

    // Pending requests with enhanced metadata
    this.pendingRequests = new Map();

    // Request timeout (in milliseconds)
    this.requestTimeout = 10000; // 10 seconds

    // Subscribed channels with tier validation
    this.subscribedChannels = new Set();

    // Ping/pong with enhanced monitoring
    this.pingInterval = 30000; // 30 seconds
    this.pingTimer = null;
    this.lastPongTime = 0;
    this.lastPingTime = 0;

    // Base URL with enhanced protocol detection
    this.baseUrl = import.meta.env.VITE_API_URL || '';
    if (this.baseUrl.startsWith('http://')) {
      this.baseUrl = this.baseUrl.replace('http://', 'ws://');
    } else if (this.baseUrl.startsWith('https://')) {
      this.baseUrl = this.baseUrl.replace('https://', 'wss://');
    }

    // Connection health monitoring
    this.connectionHealth = {
      connected: false,
      connectedAt: null,
      lastActivity: null,
      quality: 'unknown', // excellent, good, fair, poor
      latency: 0,
      messagesSent: 0,
      messagesReceived: 0,
      errors: 0
    };

    // Initialization flag
    this.isInitialized = false;

    // Bind methods to preserve context
    this.handleConnect = this.handleConnect.bind(this);
    this.handleDisconnect = this.handleDisconnect.bind(this);
    this.handleError = this.handleError.bind(this);
    this.handlePong = this.handlePong.bind(this);
    this.handleResponse = this.handleResponse.bind(this);
    this.onOpen = this.onOpen.bind(this);
    this.onClose = this.onClose.bind(this);
    this.onError = this.onError.bind(this);
    this.onMessage = this.onMessage.bind(this);

    // Register default message handlers
    this.registerMessageHandler(MessageType.CONNECT, this.handleConnect);
    this.registerMessageHandler(MessageType.DISCONNECT, this.handleDisconnect);
    this.registerMessageHandler(MessageType.ERROR, this.handleError);
    this.registerMessageHandler(MessageType.PONG, this.handlePong);
    this.registerMessageHandler(MessageType.RESPONSE, this.handleResponse);
  }

  // ===================================================================
  // ENHANCED PLATFORM SERVICE v2.0.0 INTEGRATION METHODS
  // ===================================================================

  /**
   * Initialize the enhanced WebSocket service with Platform Service v2.0.0 integration
   *
   * @returns {Promise<Object>} Initialization result with service status
   */
  async initialize() {
    if (this.isInitialized) {
      return this.getServiceStatus();
    }

    try {
      console.log('[EnhancedWebSocketService] Initializing enhanced WebSocket service...');

      // Phase 1: Initialize Enhanced Platform Service integration
      await this._initializeEnhancedServices();

      // Phase 2: Initialize security features
      await this._initializeSecurityFeatures();

      // Phase 3: Initialize monitoring and metrics
      this._initializeMonitoring();

      // Phase 4: Initialize message deduplication
      this._initializeDeduplicationService();

      // Phase 5: Initialize circuit breaker
      this._initializeCircuitBreaker();

      // Phase 6: Set up event listeners
      this._setupEventListeners();

      this.isInitialized = true;

      console.log('[EnhancedWebSocketService] Enhanced WebSocket service initialized successfully');

      // Record initialization metrics
      this._recordMetric('websocket_service_initialized', 'success');

      // Emit initialization event
      this.emit('initialized', this.getServiceStatus());

      return this.getServiceStatus();

    } catch (error) {
      console.error('[EnhancedWebSocketService] Initialization failed:', error);
      this._recordMetric('websocket_service_initialized', 'error');

      // Fallback to basic functionality
      this._initializeBasicFunctionality();

      throw error;
    }
  }

  /**
   * Initialize Enhanced Platform Service integration
   *
   * @private
   */
  async _initializeEnhancedServices() {
    try {
      // 1. Initialize Prometheus Metrics Collector
      if (this.config.enableMetrics) {
        this.metricsCollector = new PrometheusMetricsCollector({
          enabled: true,
          endpoint: '/api/metrics/websocket',
          batchSize: 50,
          flushInterval: 30000 // 30 seconds for WebSocket operations
        });

        console.log('[EnhancedWebSocketService] Prometheus metrics initialized');
      }

      // 2. Initialize real-time configuration updates
      if (this.config.enableRealTimeUpdates && this.platformService) {
        this.platformService.on('configuration_updated', this._handleConfigurationUpdate.bind(this));
        console.log('[EnhancedWebSocketService] Real-time configuration updates enabled');
      }

      // 3. Initialize subscription tier detection
      if (this.config.enableSubscriptionGating) {
        await this._initializeSubscriptionTierDetection();
      }

    } catch (error) {
      console.warn('[EnhancedWebSocketService] Enhanced services initialization failed:', error);
      // Continue with basic functionality
    }
  }

  /**
   * Initialize security features
   *
   * @private
   */
  async _initializeSecurityFeatures() {
    try {
      if (this.config.enableSecurityFingerprinting) {
        // Generate device fingerprint
        this.securityConfig.deviceFingerprint = await getDeviceFingerprint();

        // Generate platform-specific fingerprint for WebSocket management
        if (this.platformService && typeof this.platformService.generatePlatformFingerprint === 'function') {
          this.securityConfig.platformFingerprint = await this.platformService.generatePlatformFingerprint('websocket', {
            enhanced: true,
            sessionId: this.sessionId,
            clientId: this.clientId
          });
        }

        console.log('[EnhancedWebSocketService] Security fingerprinting initialized');
      }

    } catch (error) {
      console.warn('[EnhancedWebSocketService] Security features initialization failed:', error);
      this.securityConfig.fingerprintingEnabled = false;
    }
  }

  /**
   * Initialize monitoring and metrics collection
   *
   * @private
   */
  _initializeMonitoring() {
    // Initialize connection health monitoring
    this.connectionHealth = {
      connected: false,
      connectedAt: null,
      lastActivity: Date.now(),
      quality: 'unknown',
      latency: 0,
      messagesSent: 0,
      messagesReceived: 0,
      errors: 0
    };

    // Start periodic health checks
    setInterval(() => {
      this._updateConnectionHealth();
    }, 30000); // Every 30 seconds

    console.log('[EnhancedWebSocketService] Monitoring initialized');
  }

  /**
   * Initialize message deduplication service
   *
   * @private
   */
  _initializeDeduplicationService() {
    if (this.config.enableMessageDeduplication) {
      // Start cache cleanup timer
      setInterval(() => {
        this._cleanupMessageCache();
      }, 60000); // Every minute

      console.log('[EnhancedWebSocketService] Message deduplication service initialized');
    }
  }

  /**
   * Initialize circuit breaker
   *
   * @private
   */
  _initializeCircuitBreaker() {
    if (this.config.enableCircuitBreaker) {
      // Reset circuit breaker state
      this.circuitBreaker = {
        state: 'CLOSED',
        failureCount: 0,
        failureThreshold: 5,
        resetTimeout: 60000,
        lastFailureTime: null,
        nextAttemptTime: null
      };

      console.log('[EnhancedWebSocketService] Circuit breaker initialized');
    }
  }

  /**
   * Set up event listeners
   *
   * @private
   */
  _setupEventListeners() {
    // Network status monitoring
    window.addEventListener('online', () => {
      this._handleNetworkStatusChange(true);
    });

    window.addEventListener('offline', () => {
      this._handleNetworkStatusChange(false);
    });

    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });
  }

  /**
   * Set user context for enhanced logging and error handling
   * @param {string} userId - User ID
   * @param {string} userEmail - User email (optional)
   */
  setUserContext(userId, userEmail = null) {
    this.userId = userId;
    this.userEmail = userEmail;

    console.log('WebSocket user context set:', {
      userId,
      userEmail: userEmail || 'unknown',
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Add event listener
   * @param {string} event - Event name
   * @param {Function} listener - Event listener function
   */
  addEventListener(event, listener) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(listener);
  }

  /**
   * Remove event listener
   * @param {string} event - Event name
   * @param {Function} listener - Event listener function
   */
  removeEventListener(event, listener) {
    if (!this.eventListeners.has(event)) {
      return;
    }

    const listeners = this.eventListeners.get(event);
    const index = listeners.indexOf(listener);

    if (index > -1) {
      listeners.splice(index, 1);
    }

    if (listeners.length === 0) {
      this.eventListeners.delete(event);
    }
  }

  /**
   * Emit event to all listeners
   * @param {string} event - Event name
   * @param {*} data - Event data
   */
  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, {
            error: error.message,
            userId: this.userId,
            userEmail: this.userEmail || 'unknown',
            timestamp: new Date().toISOString()
          });
        }
      });
    }
  }

  /**
   * Send message (alias for sendMessage for backward compatibility)
   * @param {Object} message - Message to send
   */
  send(message) {
    if (typeof message === 'object' && message.type) {
      return this.sendMessage(message.type, message.data, message.id);
    } else {
      // For raw message sending
      if (this.socket && this.socket.readyState === WebSocket.OPEN) {
        this.socket.send(typeof message === 'string' ? message : JSON.stringify(message));
      } else {
        console.warn('WebSocket not connected, cannot send message:', {
          message,
          userId: this.userId,
          userEmail: this.userEmail || 'unknown',
          readyState: this.socket ? this.socket.readyState : 'null'
        });
      }
    }
  }

  // ===================================================================
  // ENHANCED CONNECTION MANAGEMENT WITH SECURITY & METRICS
  // ===================================================================

  /**
   * Connect to the WebSocket server with enhanced security and monitoring
   *
   * @param {string} endpoint - The WebSocket endpoint
   * @param {boolean} authenticate - Whether to authenticate the connection
   * @param {Object} options - Connection options
   * @param {boolean} options.forceReconnect - Force reconnection even if already connected
   * @returns {Promise<void>} - Resolves when connected
   */
  async connect(endpoint = '/api/v1/ws', authenticate = true, options = {}) {
    // Check circuit breaker
    if (!this._canAttemptConnection()) {
      throw new Error('Circuit breaker open - connection temporarily disabled');
    }

    // Don't connect if already connected or connecting (unless forced)
    if ((this.connected || this.connecting) && !options.forceReconnect) {
      console.log('[EnhancedWebSocketService] WebSocket already connected or connecting:', {
        connected: this.connected,
        connecting: this.connecting,
        userId: this.userId,
        userEmail: this.userEmail || 'unknown',
        sessionId: this.sessionId
      });
      return;
    }

    const startTime = Date.now();
    this.connecting = true;
    this.performanceMetrics.connectionAttempts++;

    console.log('[EnhancedWebSocketService] Attempting enhanced WebSocket connection:', {
      endpoint,
      authenticate,
      userId: this.userId,
      userEmail: this.userEmail || 'unknown',
      clientId: this.clientId,
      sessionId: this.sessionId,
      securityEnabled: this.securityConfig.fingerprintingEnabled,
      timestamp: new Date().toISOString()
    });

    try {
      // Initialize security features if not already done
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Build secure WebSocket URL with enhanced security
      const url = await this._buildSecureConnectionURL(endpoint, authenticate);

      // Create WebSocket connection
      this.socket = new WebSocket(url);

      // Set up enhanced event handlers
      this._setupEnhancedEventHandlers();

      // Return a promise that resolves when connected
      return new Promise((resolve, reject) => {
        // Set timeout for connection
        const timeout = setTimeout(() => {
          if (!this.connected) {
            this.connecting = false;
            this._handleCircuitBreakerFailure();
            reject(new Error('Connection timeout'));
          }
        }, 10000);

        // Register one-time connect handler
        const connectHandler = (message) => {
          clearTimeout(timeout);
          this.connecting = false;
          this.connected = true;
          this.connectionId = message.data?.connection_id;
          this.authenticated = message.data?.authenticated || false;

          // Update performance metrics
          this.performanceMetrics.successfulConnections++;
          this.connectionHealth.connected = true;
          this.connectionHealth.connectedAt = Date.now();

          // Reset circuit breaker on successful connection
          this._resetCircuitBreaker();

          // Record successful connection
          this._recordMetric('websocket_connection', 'success', {
            endpoint,
            duration: Date.now() - startTime,
            authenticated: this.authenticated,
            security_enabled: this.securityConfig.fingerprintingEnabled
          });

          // Resubscribe to channels
          this.resubscribeToChannels();

          // Start ping timer
          this.startPingTimer();

          // Reset reconnect attempts
          this.reconnectAttempts = 0;

          console.log('[EnhancedWebSocketService] WebSocket connected successfully with enhanced security');

          // Emit enhanced connection event
          this.emit('connected', {
            connectionId: this.connectionId,
            authenticated: this.authenticated,
            endpoint,
            sessionId: this.sessionId,
            securityEnabled: this.securityConfig.fingerprintingEnabled,
            timestamp: Date.now()
          });

          // Resolve the promise
          resolve();

          // Remove this one-time handler
          return true;
        };

        // Register enhanced error handler
        const errorHandler = (message) => {
          clearTimeout(timeout);
          this.connecting = false;

          console.error('[EnhancedWebSocketService] WebSocket connection error:', {
            error: message.message || 'Connection error',
            code: message.code,
            userId: this.userId,
            userEmail: this.userEmail || 'unknown',
            endpoint,
            sessionId: this.sessionId,
            timestamp: new Date().toISOString()
          });

          // Handle circuit breaker failure
          this._handleCircuitBreakerFailure();

          // Record connection failure
          this._recordMetric('websocket_connection', 'error', {
            endpoint,
            duration: Date.now() - startTime,
            error_code: message.code,
            error_message: message.message
          });

          // Emit authentication error if it's an auth issue
          if (message.code === ErrorCode.AUTHENTICATION_FAILED) {
            this.emit('authentication_failed', {
              userId: this.userId,
              userEmail: this.userEmail,
              error: message.message,
              endpoint,
              sessionId: this.sessionId
            });
          }

          // Emit enhanced connection failed event
          this.emit('connection_failed', {
            error: message.message || 'Connection error',
            code: message.code,
            userId: this.userId,
            userEmail: this.userEmail,
            endpoint,
            sessionId: this.sessionId
          });

          // Reject the promise
          reject(new Error(message.message || 'Connection error'));

          // Remove this one-time handler
          return true;
        };

        // Register handlers
        this.registerMessageHandler(MessageType.CONNECT, connectHandler, true);
        this.registerMessageHandler(MessageType.ERROR, errorHandler, true);
      });
    } catch (error) {
      this.connecting = false;
      this._handleCircuitBreakerFailure();

      console.error('[EnhancedWebSocketService] WebSocket connection failed:', {
        error: error.message,
        userId: this.userId,
        userEmail: this.userEmail || 'unknown',
        endpoint,
        sessionId: this.sessionId,
        timestamp: new Date().toISOString()
      });

      // Record connection failure
      this._recordMetric('websocket_connection', 'error', {
        endpoint,
        duration: Date.now() - startTime,
        error_message: error.message
      });

      this.emit('connection_failed', {
        error: error.message,
        userId: this.userId,
        userEmail: this.userEmail,
        endpoint,
        sessionId: this.sessionId
      });

      throw error;
    }
  }

  /**
   * Build secure connection URL with enhanced security features
   *
   * @private
   * @param {string} endpoint - WebSocket endpoint
   * @param {boolean} authenticate - Whether to authenticate
   * @returns {Promise<string>} Secure WebSocket URL
   */
  async _buildSecureConnectionURL(endpoint, authenticate) {
    let url = `${this.baseUrl}${endpoint}?client_id=${this.clientId}`;

    // Add session ID for tracking
    url += `&session_id=${this.sessionId}`;

    // Add user context to URL if available
    if (this.userId) {
      url += `&user_id=${this.userId}`;
    }

    // Add subscription tier for feature gating
    if (this.subscriptionTier) {
      url += `&tier=${this.subscriptionTier}`;
    }

    // Add authentication token if needed
    if (authenticate) {
      const token = await tokenManager.getToken();
      if (token) {
        // Validate token security before using
        const isTokenValid = await this._validateTokenSecurity(token);
        if (!isTokenValid) {
          throw new Error('Token security validation failed');
        }

        url += `&token=${token}`;

        // Add security fingerprints if available
        if (this.securityConfig.deviceFingerprint) {
          url += `&device_fp=${this.securityConfig.deviceFingerprint}`;
        }

        if (this.securityConfig.platformFingerprint) {
          url += `&platform_fp=${this.securityConfig.platformFingerprint}`;
        }
      } else {
        console.warn('[EnhancedWebSocketService] Authentication requested but no token available:', {
          userId: this.userId,
          userEmail: this.userEmail || 'unknown',
          sessionId: this.sessionId
        });
        this.emit('authentication_warning', {
          reason: 'No token available',
          userId: this.userId,
          userEmail: this.userEmail,
          sessionId: this.sessionId
        });
      }
    }

    return url;
  }

  /**
   * Set up enhanced event handlers for WebSocket
   *
   * @private
   */
  _setupEnhancedEventHandlers() {
    this.socket.onopen = this.onOpen.bind(this);
    this.socket.onclose = this.onClose.bind(this);
    this.socket.onerror = this.onError.bind(this);
    this.socket.onmessage = this.onMessage.bind(this);
  }

  /**
   * Disconnect from the WebSocket server with enhanced logging and cleanup
   */
  disconnect() {
    console.log('Disconnecting WebSocket:', {
      userId: this.userId,
      userEmail: this.userEmail || 'unknown',
      connected: this.connected,
      connectionId: this.connectionId,
      timestamp: new Date().toISOString()
    });

    // Stop timers
    this.stopPingTimer();
    this.stopReconnectTimer();

    // Clear pending requests with detailed logging and cleanup
    const pendingRequestIds = [];
    for (const [id, { reject, timestamp, type, timeout }] of this.pendingRequests) {
      // Log pending request details for debugging
      const requestAge = Date.now() - (timestamp || 0);
      console.warn(`Cleaning up pending request: ${id}`, {
        type: type || 'unknown',
        age: requestAge,
        timeout: timeout || 'none',
        userId: this.userId,
        userEmail: this.userEmail || 'unknown'
      });

      // Clear any associated timeouts
      if (timeout) {
        clearTimeout(timeout);
      }

      // Track request ID for analytics
      pendingRequestIds.push({
        id,
        type: type || 'unknown',
        age: requestAge
      });

      // Reject with detailed error information
      reject(new Error(`WebSocket disconnected while processing request ${id} (type: ${type || 'unknown'}, age: ${requestAge}ms)`));
    }

    // Log cleanup summary for monitoring
    if (pendingRequestIds.length > 0) {
      console.warn(`Cleaned up ${pendingRequestIds.length} pending requests on disconnect:`, pendingRequestIds);

      // Emit cleanup event for analytics
      this.emit('pending_requests_cleanup', {
        count: pendingRequestIds.length,
        requests: pendingRequestIds,
        timestamp: Date.now()
      });
    }

    this.pendingRequests.clear();

    // Close the connection
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }

    // Reset state
    this.connected = false;
    this.connecting = false;
    this.connectionId = null;

    console.log('WebSocket disconnected successfully:', {
      userId: this.userId,
      userEmail: this.userEmail || 'unknown',
      timestamp: new Date().toISOString()
    });

    this.emit('disconnected', {
      userId: this.userId,
      userEmail: this.userEmail,
      timestamp: Date.now()
    });
  }

  /**
   * Send a message to the WebSocket server with enhanced security and deduplication
   *
   * @param {string} type - The message type
   * @param {object} data - The message data
   * @param {string} id - Optional message ID
   * @returns {Promise<object>} - Resolves with the response
   */
  async sendMessage(type, data = null, id = null) {
    // Check circuit breaker
    if (!this._canSendMessage()) {
      throw new Error('Circuit breaker open - message sending disabled');
    }

    // Check if connected
    if (!this.connected) {
      const error = new Error('WebSocket not connected');
      console.error('[EnhancedWebSocketService] Cannot send message - WebSocket not connected:', {
        type,
        userId: this.userId,
        userEmail: this.userEmail || 'unknown',
        sessionId: this.sessionId,
        readyState: this.socket ? this.socket.readyState : 'null',
        timestamp: new Date().toISOString()
      });

      this._recordMetric('message_send', 'error', {
        type,
        error: 'not_connected'
      });

      throw error;
    }

    const startTime = Date.now();
    const messageId = id || uuidv4();

    try {
      // Check for duplicates
      if (this._isDuplicateMessage(messageId, type, data)) {
        this.performanceMetrics.duplicatesDetected++;
        this._recordMetric('message_duplicate', 'detected', { messageId, type });
        console.warn('[EnhancedWebSocketService] Duplicate message detected:', { messageId, type });
        return null;
      }

      // Create enhanced message with security context
      const message = {
        type,
        id: messageId,
        data,
        timestamp: new Date().toISOString(),
        userId: this.userId,
        userEmail: this.userEmail,
        sessionId: this.sessionId,
        clientId: this.clientId,
        deviceFingerprint: this.securityConfig.deviceFingerprint,
        platformFingerprint: this.securityConfig.platformFingerprint
      };

      // Log message sending (excluding ping to reduce noise)
      if (type !== MessageType.PING) {
        console.debug('[EnhancedWebSocketService] Sending WebSocket message:', {
          type,
          messageId,
          userId: this.userId,
          userEmail: this.userEmail || 'unknown',
          sessionId: this.sessionId,
          hasData: !!data,
          timestamp: message.timestamp
        });
      }

      // Send the message
      this.socket.send(JSON.stringify(message));

      // Update metrics
      this.performanceMetrics.messagesSent++;
      this.connectionHealth.messagesSent++;
      this.connectionHealth.lastActivity = Date.now();

      // Record successful send
      this._recordMetric('message_send', 'success', {
        type,
        messageId,
        duration: Date.now() - startTime,
        has_data: !!data
      });

      // Cache for deduplication
      this._cacheMessage(messageId, type, data);

      // For message types that expect a response, return a promise
      if ([MessageType.COMMAND, MessageType.SUBSCRIBE, MessageType.UNSUBSCRIBE].includes(type)) {
        return new Promise((resolve, reject) => {
          // Set timeout
          const timeout = setTimeout(() => {
            // Remove from pending requests
            this.pendingRequests.delete(messageId);

            // Record timeout
            this._recordMetric('message_timeout', 'error', { messageId, type });

            // Reject with timeout error
            reject(new Error(`Request timeout for message ${messageId}`));
          }, this.requestTimeout);

          // Store in pending requests with enhanced metadata
          this.pendingRequests.set(messageId, {
            resolve,
            reject,
            timeout,
            timestamp: Date.now(),
            type,
            userId: this.userId,
            userEmail: this.userEmail,
            sessionId: this.sessionId
          });
        });
      }

      return messageId;

    } catch (error) {
      console.error('[EnhancedWebSocketService] Failed to send WebSocket message:', {
        type,
        messageId,
        error: error.message,
        userId: this.userId,
        userEmail: this.userEmail || 'unknown',
        sessionId: this.sessionId,
        timestamp: new Date().toISOString()
      });

      // Record failure
      this._recordMetric('message_send', 'error', {
        type,
        messageId,
        duration: Date.now() - startTime,
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Check if message sending is allowed (circuit breaker)
   *
   * @private
   * @returns {boolean} Whether message can be sent
   */
  _canSendMessage() {
    return this._canAttemptConnection(); // Reuse circuit breaker logic
  }

  /**
   * Check if message is duplicate
   *
   * @private
   * @param {string} messageId - Message ID
   * @param {string} type - Message type
   * @param {Object} data - Message data
   * @returns {boolean} Whether message is duplicate
   */
  _isDuplicateMessage(messageId, type, data) {
    if (!this.config.enableMessageDeduplication) {
      return false;
    }

    const cacheKey = `${messageId}_${type}`;
    const cached = this.deduplicationService.messageCache.get(cacheKey);

    if (cached) {
      // Check if it's within the TTL and has same data
      const isWithinTTL = Date.now() - cached.timestamp < this.deduplicationService.cacheTTL;
      const hasSameData = JSON.stringify(cached.data) === JSON.stringify(data);

      return isWithinTTL && hasSameData;
    }

    return false;
  }

  /**
   * Cache message for deduplication
   *
   * @private
   * @param {string} messageId - Message ID
   * @param {string} type - Message type
   * @param {Object} data - Message data
   */
  _cacheMessage(messageId, type, data) {
    if (!this.config.enableMessageDeduplication) {
      return;
    }

    const cacheKey = `${messageId}_${type}`;
    this.deduplicationService.messageCache.set(cacheKey, {
      messageId,
      type,
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Subscribe to a channel.
   *
   * @param {string} channel - The channel to subscribe to
   * @returns {Promise<object>} - Resolves with the response
   */
  async subscribe(channel) {
    // Send subscribe message
    const response = await this.sendMessage(MessageType.SUBSCRIBE, { channel });

    // Add to subscribed channels
    if (response?.success) {
      this.subscribedChannels.add(channel);
    }

    return response;
  }

  /**
   * Unsubscribe from a channel.
   *
   * @param {string} channel - The channel to unsubscribe from
   * @returns {Promise<object>} - Resolves with the response
   */
  async unsubscribe(channel) {
    // Send unsubscribe message
    const response = await this.sendMessage(MessageType.UNSUBSCRIBE, { channel });

    // Remove from subscribed channels
    if (response?.success) {
      this.subscribedChannels.delete(channel);
    }

    return response;
  }

  /**
   * Send a command to the WebSocket server.
   *
   * @param {string} command - The command to send
   * @param {object} params - The command parameters
   * @returns {Promise<object>} - Resolves with the response
   */
  async sendCommand(command, params = {}) {
    return this.sendMessage(MessageType.COMMAND, { command, params });
  }

  /**
   * Register a message handler.
   *
   * @param {string} type - The message type
   * @param {function} handler - The handler function
   * @param {boolean} once - Whether to remove the handler after first use
   */
  registerMessageHandler(type, handler, once = false) {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, []);
    }

    this.messageHandlers.get(type).push({ handler, once });
  }

  /**
   * Unregister a message handler.
   *
   * @param {string} type - The message type
   * @param {function} handler - The handler function
   */
  unregisterMessageHandler(type, handler) {
    if (!this.messageHandlers.has(type)) {
      return;
    }

    const handlers = this.messageHandlers.get(type);
    const index = handlers.findIndex(h => h.handler === handler);

    if (index !== -1) {
      handlers.splice(index, 1);
    }

    if (handlers.length === 0) {
      this.messageHandlers.delete(type);
    }
  }

  /**
   * Handle WebSocket open event.
   *
   * @param {Event} event - The event
   */
  onOpen(event) {
    // Extract connection information from event
    const connectionTime = Date.now();
    const protocol = event.target?.protocol || 'unknown';
    const url = event.target?.url || this.url;
    const readyState = event.target?.readyState || WebSocket.OPEN;

    // Update connection state
    this.isConnected = true;
    this.connectionStartTime = connectionTime;
    this.reconnectAttempts = 0;
    this.lastConnectionError = null;

    // Log detailed connection information
    console.log('WebSocket connected successfully:', {
      url,
      protocol,
      readyState,
      connectionTime: new Date(connectionTime).toISOString(),
      reconnectAttempts: this.reconnectAttempts,
      timeToConnect: this.connectionStartTime ? connectionTime - this.connectionStartTime : 0
    });

    // Initialize connection health monitoring
    this.connectionHealth = {
      connected: true,
      connectedAt: connectionTime,
      protocol,
      url,
      readyState,
      lastActivity: connectionTime,
      messagesSent: 0,
      messagesReceived: 0,
      errors: 0
    };

    // Start heartbeat if configured
    if (this.heartbeatInterval && !this.heartbeatTimer) {
      this.startHeartbeat();
    }

    // Emit connection established event with detailed information
    this.emit('connected', {
      url,
      protocol,
      connectionTime,
      reconnectAttempts: this.reconnectAttempts,
      connectionHealth: this.connectionHealth
    });

    // Send initial connection message if configured
    if (this.initialMessage) {
      this.send(this.initialMessage);
    }

    // Process any queued messages
    if (this.messageQueue && this.messageQueue.length > 0) {
      console.log(`Processing ${this.messageQueue.length} queued messages`);
      const queuedMessages = [...this.messageQueue];
      this.messageQueue = [];

      queuedMessages.forEach(message => {
        this.send(message);
      });
    }
  }

  /**
   * Handle WebSocket close event.
   *
   * @param {CloseEvent} event - The event
   */
  onClose(event) {
    console.log(`WebSocket disconnected: ${event.code} ${event.reason}`);

    // Reset state
    this.connected = false;
    this.connecting = false;

    // Stop ping timer
    this.stopPingTimer();

    // Start reconnect timer if not a clean close
    if (event.code !== 1000) {
      this.startReconnectTimer();
    }
  }

  /**
   * Handle WebSocket error event with enhanced logging.
   *
   * @param {Event} event - The event
   */
  onError(event) {
    console.error('WebSocket error:', {
      error: event.error || 'Unknown WebSocket error',
      userId: this.userId,
      userEmail: this.userEmail || 'unknown',
      readyState: this.socket ? this.socket.readyState : 'null',
      timestamp: new Date().toISOString()
    });

    this.emit('websocket_error', {
      error: event.error || 'Unknown WebSocket error',
      userId: this.userId,
      userEmail: this.userEmail,
      readyState: this.socket ? this.socket.readyState : 'null',
      timestamp: Date.now()
    });
  }

  /**
   * Handle WebSocket message event.
   *
   * @param {MessageEvent} event - The event
   */
  onMessage(event) {
    try {
      // Parse message
      const message = JSON.parse(event.data);

      // Check if message has a type
      if (!message.type) {
        console.warn('Received message without type:', message);
        return;
      }

      // Get handlers for this message type
      const handlers = this.messageHandlers.get(message.type) || [];

      // Call handlers
      for (let i = handlers.length - 1; i >= 0; i--) {
        const { handler, once } = handlers[i];

        try {
          const result = handler(message);

          // Remove handler if once is true and result is true
          if (once && result === true) {
            handlers.splice(i, 1);
          }
        } catch (error) {
          console.error(`Error in ${message.type} handler:`, error);
        }
      }

      // Remove empty handler arrays
      if (handlers.length === 0) {
        this.messageHandlers.delete(message.type);
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }

  /**
   * Handle connect message with enhanced logging.
   *
   * @param {object} message - The message
   */
  handleConnect(message) {
    console.log('Connected to WebSocket server:', {
      data: message.data,
      userId: this.userId,
      userEmail: this.userEmail || 'unknown',
      connectionId: this.connectionId,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle disconnect message with enhanced logging.
   *
   * @param {object} message - The message
   */
  handleDisconnect(message) {
    console.log('Disconnected from WebSocket server:', {
      data: message.data,
      userId: this.userId,
      userEmail: this.userEmail || 'unknown',
      connectionId: this.connectionId,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle error message with enhanced logging and user context.
   *
   * @param {object} message - The message
   */
  handleError(message) {
    console.error('WebSocket error:', {
      message: message.message || 'Unknown error',
      code: message.code,
      id: message.id,
      userId: this.userId,
      userEmail: this.userEmail || 'unknown',
      timestamp: new Date().toISOString()
    });

    // Emit error event
    this.emit('message_error', {
      message: message.message || 'Unknown error',
      code: message.code,
      id: message.id,
      userId: this.userId,
      userEmail: this.userEmail
    });

    // Check if this is a response to a pending request
    if (message.id && this.pendingRequests.has(message.id)) {
      const { reject, timeout } = this.pendingRequests.get(message.id);

      // Clear timeout
      clearTimeout(timeout);

      // Reject the promise
      reject(new Error(message.message || 'Unknown error'));

      // Remove from pending requests
      this.pendingRequests.delete(message.id);
    }
  }

  /**
   * Handle pong message.
   *
   * @param {object} message - The message
   */
  handlePong(message) {
    const currentTime = Date.now();

    // Extract pong information from message
    const serverTimestamp = message.timestamp || currentTime;
    const pingId = message.ping_id || message.id;
    const serverLoad = message.server_load || 'unknown';
    const connectionId = message.connection_id || 'unknown';

    // Calculate round-trip time if ping timestamp is available
    const pingTimestamp = message.ping_timestamp || this.lastPingTime;
    const roundTripTime = pingTimestamp ? currentTime - pingTimestamp : 0;

    // Update connection health metrics
    this.lastPongTime = currentTime;
    this.connectionHealth = {
      ...this.connectionHealth,
      lastPong: currentTime,
      roundTripTime,
      serverTimestamp,
      serverLoad,
      connectionId,
      latency: roundTripTime,
      quality: roundTripTime < 100 ? 'excellent' :
               roundTripTime < 300 ? 'good' :
               roundTripTime < 1000 ? 'fair' : 'poor'
    };

    // Log pong details for monitoring
    console.debug('WebSocket pong received:', {
      pingId,
      roundTripTime,
      serverLoad,
      connectionId,
      quality: this.connectionHealth.quality,
      serverTimestamp: new Date(serverTimestamp).toISOString()
    });

    // Update connection statistics
    if (!this.connectionStats) {
      this.connectionStats = {
        pongsReceived: 0,
        totalRoundTripTime: 0,
        minRoundTripTime: Infinity,
        maxRoundTripTime: 0,
        averageRoundTripTime: 0
      };
    }

    this.connectionStats.pongsReceived++;
    this.connectionStats.totalRoundTripTime += roundTripTime;
    this.connectionStats.minRoundTripTime = Math.min(this.connectionStats.minRoundTripTime, roundTripTime);
    this.connectionStats.maxRoundTripTime = Math.max(this.connectionStats.maxRoundTripTime, roundTripTime);
    this.connectionStats.averageRoundTripTime = this.connectionStats.totalRoundTripTime / this.connectionStats.pongsReceived;

    // Emit pong event with detailed metrics
    this.emit('pong', {
      pingId,
      roundTripTime,
      serverLoad,
      connectionId,
      quality: this.connectionHealth.quality,
      stats: this.connectionStats,
      timestamp: currentTime
    });

    // Check for connection quality issues
    if (roundTripTime > 2000) {
      console.warn('High latency detected:', {
        roundTripTime,
        quality: this.connectionHealth.quality,
        connectionId
      });

      this.emit('connection_quality_warning', {
        type: 'high_latency',
        roundTripTime,
        threshold: 2000,
        connectionId
      });
    }
  }

  /**
   * Handle response message.
   *
   * @param {object} message - The message
   */
  handleResponse(message) {
    // Check if this is a response to a pending request
    if (message.data?.request_id && this.pendingRequests.has(message.data.request_id)) {
      const { resolve, reject, timeout } = this.pendingRequests.get(message.data.request_id);

      // Clear timeout
      clearTimeout(timeout);

      // Resolve or reject the promise
      if (message.data.success) {
        resolve(message.data);
      } else {
        reject(new Error(message.data.message || 'Request failed'));
      }

      // Remove from pending requests
      this.pendingRequests.delete(message.data.request_id);
    }
  }

  /**
   * Start the ping timer.
   */
  startPingTimer() {
    // Stop existing timer
    this.stopPingTimer();

    // Start new timer
    this.pingTimer = setInterval(() => {
      // Send ping
      this.sendMessage(MessageType.PING, { timestamp: new Date().toISOString() });

      // Check if we've received a pong recently
      const elapsed = Date.now() - this.lastPongTime;
      if (this.lastPongTime > 0 && elapsed > this.pingInterval * 2) {
        console.warn(`No pong received for ${elapsed}ms, reconnecting...`);
        this.reconnect();
      }
    }, this.pingInterval);
  }

  /**
   * Stop the ping timer.
   */
  stopPingTimer() {
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
  }

  /**
   * Start the reconnect timer.
   */
  startReconnectTimer() {
    // Stop existing timer
    this.stopReconnectTimer();

    // Check if max reconnect attempts reached
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.warn('Max reconnect attempts reached');
      return;
    }

    // Calculate reconnect interval with exponential backoff
    const interval = Math.min(
      this.reconnectInterval * Math.pow(1.5, this.reconnectAttempts),
      this.maxReconnectInterval
    );

    console.log(`Reconnecting in ${interval}ms (attempt ${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})...`);

    // Start new timer
    this.reconnectTimer = setTimeout(() => {
      this.reconnect();
    }, interval);
  }

  /**
   * Stop the reconnect timer.
   */
  stopReconnectTimer() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * Reconnect to the WebSocket server.
   */
  async reconnect() {
    // Increment reconnect attempts
    this.reconnectAttempts++;

    // Disconnect if connected
    if (this.connected || this.connecting) {
      this.disconnect();
    }

    try {
      // Connect
      await this.connect();
      console.log('Reconnected to WebSocket server');
    } catch (error) {
      console.error('Reconnect failed:', error);

      // Start reconnect timer
      this.startReconnectTimer();
    }
  }

  /**
   * Resubscribe to channels.
   */
  async resubscribeToChannels() {
    // Get channels to resubscribe to
    const channels = [...this.subscribedChannels];

    // Clear subscribed channels
    this.subscribedChannels.clear();

    // Resubscribe to each channel
    for (const channel of channels) {
      try {
        await this.subscribe(channel);
      } catch (error) {
        console.error(`Error resubscribing to channel ${channel}:`, error);
      }
    }
  }

  // ===================================================================
  // UTILITY METHODS - Enhanced Platform Service Integration
  // ===================================================================

  /**
   * Check if connection can be attempted (circuit breaker)
   *
   * @private
   * @returns {boolean} Whether connection can be attempted
   */
  _canAttemptConnection() {
    if (!this.config.enableCircuitBreaker) {
      return true;
    }

    if (this.circuitBreaker.state === 'CLOSED') {
      return true;
    }

    if (this.circuitBreaker.state === 'OPEN') {
      // Check if reset timeout has passed
      if (Date.now() >= this.circuitBreaker.nextAttemptTime) {
        this.circuitBreaker.state = 'HALF_OPEN';
        return true;
      }
      return false;
    }

    // HALF_OPEN state - allow one attempt
    return this.circuitBreaker.state === 'HALF_OPEN';
  }

  /**
   * Handle circuit breaker failure
   *
   * @private
   */
  _handleCircuitBreakerFailure() {
    if (!this.config.enableCircuitBreaker) {
      return;
    }

    this.circuitBreaker.failureCount++;
    this.circuitBreaker.lastFailureTime = Date.now();

    if (this.circuitBreaker.failureCount >= this.circuitBreaker.failureThreshold) {
      this.circuitBreaker.state = 'OPEN';
      this.circuitBreaker.nextAttemptTime = Date.now() + this.circuitBreaker.resetTimeout;
      this.performanceMetrics.circuitBreakerTrips++;

      console.warn('[EnhancedWebSocketService] Circuit breaker opened due to failures:', {
        failureCount: this.circuitBreaker.failureCount,
        nextAttemptTime: new Date(this.circuitBreaker.nextAttemptTime).toISOString(),
        sessionId: this.sessionId
      });

      // Emit circuit breaker event
      this.emit('circuit_breaker_opened', {
        failureCount: this.circuitBreaker.failureCount,
        nextAttemptTime: this.circuitBreaker.nextAttemptTime,
        sessionId: this.sessionId
      });
    }
  }

  /**
   * Reset circuit breaker on successful operation
   *
   * @private
   */
  _resetCircuitBreaker() {
    if (!this.config.enableCircuitBreaker) {
      return;
    }

    const wasOpen = this.circuitBreaker.state !== 'CLOSED';

    this.circuitBreaker.state = 'CLOSED';
    this.circuitBreaker.failureCount = 0;
    this.circuitBreaker.lastFailureTime = null;
    this.circuitBreaker.nextAttemptTime = null;

    if (wasOpen) {
      console.log('[EnhancedWebSocketService] Circuit breaker reset');
      this.emit('circuit_breaker_reset', {
        timestamp: Date.now(),
        sessionId: this.sessionId
      });
    }
  }

  /**
   * Record metrics using Prometheus collector
   *
   * @private
   * @param {string} operation - Operation name
   * @param {string} status - Operation status
   * @param {Object} metadata - Additional metadata
   */
  _recordMetric(operation, status, metadata = {}) {
    try {
      if (!this.metricsCollector) return;

      this.metricsCollector.recordMessageSend(
        'websocket',
        this.subscriptionTier || 'creator',
        status,
        metadata.duration || 0,
        {
          operation,
          user_id: this.userId || 'unknown',
          session_id: this.sessionId,
          connection_id: this.connectionId || 'unknown',
          client_id: this.clientId,
          ...metadata
        }
      );

    } catch (error) {
      console.warn('[EnhancedWebSocketService] Metrics recording failed:', error);
    }
  }

  /**
   * Validate token security
   *
   * @private
   * @param {string} token - Token to validate (currently used for future security enhancements)
   * @returns {Promise<boolean>} Whether token security is valid
   */
  async _validateTokenSecurity(token) {
    try {
      if (!this.securityConfig.fingerprintingEnabled) {
        return true;
      }

      // Use tokenManager's enhanced validation
      const isValid = await tokenManager.isTokenValid();

      // Future enhancement: Validate token-specific security attributes
      // This could include checking token binding to device characteristics
      // For now, we validate the overall security context
      if (token && typeof token === 'string' && token.length > 0 && isValid) {
        // Token format validation passed, additional security checks can be added here
        return true;
      }

      return isValid;

    } catch (error) {
      console.error('[EnhancedWebSocketService] Token security validation failed:', error);
      return false;
    }
  }

  /**
   * Generate unique session ID
   *
   * @private
   * @returns {string} Session ID
   */
  _generateSessionId() {
    return `ws_session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Initialize subscription tier detection
   *
   * @private
   */
  async _initializeSubscriptionTierDetection() {
    try {
      // Get user info from token to determine subscription tier
      const userInfo = await tokenManager.getTokenPayload();
      if (userInfo && userInfo.subscription) {
        this.subscriptionTier = userInfo.subscription.plan || 'creator';
      }
    } catch (error) {
      console.warn('[EnhancedWebSocketService] Subscription tier detection failed:', error);
      this.subscriptionTier = 'creator'; // Default fallback
    }
  }

  /**
   * Handle network status changes
   *
   * @private
   * @param {boolean} isOnline - Whether network is online
   */
  _handleNetworkStatusChange(isOnline) {
    console.log(`[EnhancedWebSocketService] Network status changed: ${isOnline ? 'online' : 'offline'}`);

    if (isOnline && !this.connected) {
      // Attempt to reconnect when back online
      setTimeout(() => {
        if (!this.connected && !this.connecting) {
          this.reconnect();
        }
      }, 1000);
    }

    this.emit('network_status_changed', {
      isOnline,
      timestamp: Date.now(),
      sessionId: this.sessionId
    });
  }

  /**
   * Handle configuration updates from platform service
   *
   * @private
   * @param {Object} updateData - Configuration update data
   */
  _handleConfigurationUpdate(updateData) {
    try {
      console.log('[EnhancedWebSocketService] Configuration update received:', updateData);

      // Update WebSocket settings if provided
      if (updateData.websocket) {
        Object.assign(this.config, updateData.websocket);
      }

      // Update security settings if provided
      if (updateData.security) {
        Object.assign(this.securityConfig, updateData.security);
      }

      this.emit('configuration_updated', updateData);

    } catch (error) {
      console.warn('[EnhancedWebSocketService] Configuration update failed:', error);
    }
  }

  /**
   * Update connection health metrics
   *
   * @private
   */
  _updateConnectionHealth() {
    if (!this.connected) {
      this.connectionHealth.quality = 'disconnected';
      return;
    }

    const now = Date.now();
    const timeSinceLastPong = now - this.lastPongTime;

    // Update quality based on latency and connection stability
    if (this.connectionHealth.latency < 100 && timeSinceLastPong < 60000) {
      this.connectionHealth.quality = 'excellent';
    } else if (this.connectionHealth.latency < 300 && timeSinceLastPong < 120000) {
      this.connectionHealth.quality = 'good';
    } else if (this.connectionHealth.latency < 1000 && timeSinceLastPong < 180000) {
      this.connectionHealth.quality = 'fair';
    } else {
      this.connectionHealth.quality = 'poor';
    }

    this.connectionHealth.lastActivity = now;
  }

  /**
   * Clean up message cache for deduplication
   *
   * @private
   */
  _cleanupMessageCache() {
    if (!this.config.enableMessageDeduplication) {
      return;
    }

    const now = Date.now();
    const expiredKeys = [];

    for (const [key, data] of this.deduplicationService.messageCache) {
      if (now - data.timestamp > this.deduplicationService.cacheTTL) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => {
      this.deduplicationService.messageCache.delete(key);
    });

    // Limit cache size
    if (this.deduplicationService.messageCache.size > this.deduplicationService.maxCacheSize) {
      const entries = Array.from(this.deduplicationService.messageCache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

      const toRemove = entries.slice(0, entries.length - this.deduplicationService.maxCacheSize);
      toRemove.forEach(([key]) => {
        this.deduplicationService.messageCache.delete(key);
      });
    }
  }

  /**
   * Initialize basic functionality as fallback
   *
   * @private
   */
  _initializeBasicFunctionality() {
    console.log('[EnhancedWebSocketService] Initializing basic functionality fallback');

    // Disable enhanced features
    this.config.enableMetrics = false;
    this.config.enableSecurityFingerprinting = false;
    this.config.enableMessageDeduplication = false;
    this.config.enableCircuitBreaker = false;

    this.isInitialized = true;
  }

  /**
   * Get service status
   *
   * @returns {Object} Service status information
   */
  getServiceStatus() {
    return {
      initialized: this.isInitialized,
      connected: this.connected,
      authenticated: this.authenticated,
      config: this.config,
      circuitBreaker: {
        state: this.circuitBreaker.state,
        failureCount: this.circuitBreaker.failureCount
      },
      performance: {
        connectionAttempts: this.performanceMetrics.connectionAttempts,
        successfulConnections: this.performanceMetrics.successfulConnections,
        messagesSent: this.performanceMetrics.messagesSent,
        messagesReceived: this.performanceMetrics.messagesReceived,
        duplicatesDetected: this.performanceMetrics.duplicatesDetected,
        securityViolations: this.performanceMetrics.securityViolations,
        circuitBreakerTrips: this.performanceMetrics.circuitBreakerTrips,
        averageLatency: this.performanceMetrics.averageLatency
      },
      security: {
        fingerprintingEnabled: this.securityConfig.fingerprintingEnabled,
        hasDeviceFingerprint: !!this.securityConfig.deviceFingerprint,
        hasPlatformFingerprint: !!this.securityConfig.platformFingerprint
      },
      connectionHealth: this.connectionHealth,
      subscriptionTier: this.subscriptionTier,
      sessionId: this.sessionId,
      clientId: this.clientId
    };
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    // Disconnect if connected
    if (this.connected) {
      this.disconnect();
    }

    // Clear timers
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    // Clear caches
    this.deduplicationService.messageCache.clear();
    this.pendingRequests.clear();

    // Remove event listeners
    window.removeEventListener('online', this._handleNetworkStatusChange);
    window.removeEventListener('offline', this._handleNetworkStatusChange);
    window.removeEventListener('beforeunload', this.cleanup);

    console.log('[EnhancedWebSocketService] Cleanup completed');
  }
}

// Create singleton instance
const webSocketService = new EnhancedWebSocketService();

export default webSocketService;
