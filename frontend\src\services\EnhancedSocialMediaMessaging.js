/**
 * Enhanced Social Media Messaging Service
 *
 * Provides comprehensive messaging capabilities across multiple social media platforms
 * with advanced error handling, rate limiting, real-time synchronization, Prometheus metrics,
 * message encryption, deduplication, and WebSocket status updates.
 *
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 * @since 2024-01-01
 *
 * Features:
 * - Platform-specific error handling with actionable messages
 * - Multiple fallback modes for different failure scenarios
 * - Automatic retry with exponential backoff and jitter
 * - Comprehensive Prometheus metrics collection
 * - End-to-end message encryption for sensitive content
 * - Intelligent message deduplication
 * - Real-time WebSocket platform status updates
 * - Rate limit tracking and circuit breaker protection
 *
 * @example
 * ```javascript
 * import enhancedSocialMediaMessaging from './EnhancedSocialMediaMessaging';
 *
 * // Send encrypted message with deduplication
 * const result = await enhancedSocialMediaMessaging.sendMessage({
 *   platform: 'linkedin',
 *   conversation_id: 'conv_123',
 *   content: 'Hello from ACE Social!',
 *   encrypt: true
 * });
 * ```
 */

import axios from 'axios';
import { toast } from 'react-toastify';
import networkMonitor from './NetworkMonitor';
import { socialMediaMetrics } from '../utils/PrometheusMetricsCollector';
import { messageEncryption } from '../utils/MessageEncryptionService';
import { messageDeduplication } from '../utils/MessageDeduplicationService';
import WebSocketService from './WebSocketService';

// Configuration constants
const CONFIG = {
  // Platform status checking
  PLATFORM_STATUS_CHECK_INTERVAL: 300000, // 5 minutes (fallback for WebSocket)

  // Circuit breaker configuration
  CIRCUIT_BREAKER_RESET_TIMEOUT: 300000, // 5 minutes
  ERROR_THRESHOLDS: {
    send: 3,
    sync: 5,
    platform: 3
  },

  // Message queue configuration
  MAX_RETRY_ATTEMPTS: 5,
  INITIAL_RETRY_DELAY: 5000,
  MAX_RETRY_DELAY: 300000, // 5 minutes
  RETRY_BACKOFF_FACTOR: 2,

  // Notification configuration
  NOTIFICATION_AUTO_CLOSE: 5000,

  // Environment
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production',

  // Enhancement configurations
  METRICS_ENABLED: true,
  ENCRYPTION_ENABLED: true,
  DEDUPLICATION_ENABLED: true,
  WEBSOCKET_STATUS_ENABLED: true,

  // Deduplication settings
  DEDUPLICATION_WINDOW_MS: 30000, // 30 seconds
  DEDUPLICATION_CACHE_SIZE: 1000,

  // WebSocket settings
  WS_RECONNECT_ATTEMPTS: 10,
  WS_RECONNECT_INTERVAL: 5000,
  WS_HEARTBEAT_INTERVAL: 30000,

  // Metrics settings
  METRICS_BATCH_SIZE: 50,
  METRICS_FLUSH_INTERVAL: 30000, // 30 seconds

  // Encryption settings
  ENCRYPTION_KEY_ROTATION_INTERVAL: 86400000, // 24 hours
  ENCRYPTION_ALGORITHM: 'AES-256-GCM'
};

// Enhanced logging utility
const logger = {
  debug: (message, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.log(`[SocialMediaMessaging] ${message}`, ...args);
    }
  },
  info: (message, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.info(`[SocialMediaMessaging] ${message}`, ...args);
    }
  },
  warn: (message, ...args) => {
    console.warn(`[SocialMediaMessaging] ${message}`, ...args);
  },
  error: (message, error, ...args) => {
    console.error(`[SocialMediaMessaging] ${message}`, error, ...args);

    // In production, you might want to send errors to a logging service
    if (CONFIG.IS_PRODUCTION && window.analytics) {
      window.analytics.track('Social Media Messaging Error', {
        message,
        error: error?.message || error,
        stack: error?.stack,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Platform-specific error messages with actionable information
const PLATFORM_ERROR_MESSAGES = {
  facebook: {
    AUTH_ERROR: {
      title: 'Facebook Authentication Error',
      message: 'Your Facebook account connection needs to be refreshed.',
      action: 'Click to reconnect your Facebook account',
      type: 'error',
      actionLink: '/settings/integrations'
    },
    RATE_LIMIT: {
      title: 'Facebook Rate Limit Reached',
      message: 'Facebook has temporarily limited your messaging activity.',
      action: 'Messages will be sent automatically when the limit resets.',
      type: 'warning'
    },
    API_CHANGE: {
      title: 'Facebook API Change Detected',
      message: 'Facebook has updated their API which may affect messaging.',
      action: 'Our team has been notified and is working on a fix.',
      type: 'warning'
    }
  },
  instagram: {
    AUTH_ERROR: {
      title: 'Instagram Authentication Error',
      message: 'Your Instagram account connection needs to be refreshed.',
      action: 'Click to reconnect your Instagram account',
      type: 'error',
      actionLink: '/settings/integrations'
    },
    RATE_LIMIT: {
      title: 'Instagram Rate Limit Reached',
      message: 'Instagram has temporarily limited your messaging activity.',
      action: 'Messages will be sent automatically when the limit resets.',
      type: 'warning'
    },
    CONTENT_POLICY: {
      title: 'Instagram Content Policy Violation',
      message: 'Your message may violate Instagram\'s content policies.',
      action: 'Please review and modify your message content.',
      type: 'error'
    }
  },
  linkedin: {
    AUTH_ERROR: {
      title: 'LinkedIn Authentication Error',
      message: 'Your LinkedIn account connection needs to be refreshed.',
      action: 'Click to reconnect your LinkedIn account',
      type: 'error',
      actionLink: '/settings/integrations'
    },
    RATE_LIMIT: {
      title: 'LinkedIn Rate Limit Reached',
      message: 'LinkedIn has temporarily limited your messaging activity.',
      action: 'Messages will be sent automatically when the limit resets.',
      type: 'warning'
    },
    COMMERCIAL_LIMIT: {
      title: 'LinkedIn Commercial Use Limit',
      message: 'You\'ve reached LinkedIn\'s limit for commercial messages.',
      action: 'Consider upgrading your LinkedIn account for higher limits.',
      type: 'warning'
    }
  },
  twitter: {
    AUTH_ERROR: {
      title: 'Twitter Authentication Error',
      message: 'Your Twitter account connection needs to be refreshed.',
      action: 'Click to reconnect your Twitter account',
      type: 'error',
      actionLink: '/settings/integrations'
    },
    RATE_LIMIT: {
      title: 'Twitter Rate Limit Reached',
      message: 'Twitter has temporarily limited your messaging activity.',
      action: 'Messages will be sent automatically when the limit resets.',
      type: 'warning'
    },
    DM_RESTRICTION: {
      title: 'Twitter DM Restriction',
      message: 'This user doesn\'t accept direct messages or you need to follow each other.',
      action: 'Check your Twitter relationship with this user.',
      type: 'error'
    }
  },
  // Generic errors for all platforms
  generic: {
    CONNECTION_ERROR: {
      title: 'Connection Error',
      message: 'Unable to connect to the social media platform.',
      action: 'Messages will be queued and sent when connection is restored.',
      type: 'warning'
    },
    SYNC_ERROR: {
      title: 'Sync Error',
      message: 'Unable to sync messages with the platform.',
      action: 'We\'ll automatically retry. Click to try manually.',
      type: 'warning'
    },
    ATTACHMENT_ERROR: {
      title: 'Attachment Error',
      message: 'There was a problem with your attachment.',
      action: 'Please try a different file or format.',
      type: 'error'
    },
    PLATFORM_UNAVAILABLE: {
      title: 'Platform Temporarily Unavailable',
      message: 'The social media platform is currently experiencing issues.',
      action: 'Messages will be queued and sent automatically when available.',
      type: 'error'
    }
  }
};

// Rate limit tracking by platform
const RATE_LIMIT_DEFAULTS = {
  facebook: { limit: 200, window: 3600000, resetTime: null }, // 200 per hour
  instagram: { limit: 100, window: 3600000, resetTime: null }, // 100 per hour
  linkedin: { limit: 100, window: 86400000, resetTime: null }, // 100 per day
  twitter: { limit: 1000, window: 86400000, resetTime: null }, // 1000 per day
};

class EnhancedSocialMediaMessaging {
  constructor() {
    // Initialize state
    this.messageQueue = [];
    this.syncStatus = {};
    this.rateLimits = JSON.parse(JSON.stringify(RATE_LIMIT_DEFAULTS));
    this.platformStatus = {
      facebook: { available: true, lastCheck: null },
      instagram: { available: true, lastCheck: null },
      linkedin: { available: true, lastCheck: null },
      twitter: { available: true, lastCheck: null }
    };
    this.errorCounts = {};
    this.circuitBreakers = {};
    this.circuitBreakerResetTimeout = CONFIG.CIRCUIT_BREAKER_RESET_TIMEOUT;
    this.listeners = new Map();

    // Enhancement services
    this.metricsCollector = socialMediaMetrics;
    this.encryptionService = messageEncryption;
    this.deduplicationService = messageDeduplication;
    this.webSocketService = null; // Initialized later

    // WebSocket status monitoring
    this.wsConnected = false;
    this.wsReconnectAttempts = 0;
    this.wsHeartbeatTimer = null;
    this.lastPlatformStatusUpdate = null;

    // Performance tracking
    this.operationStartTimes = new Map();
    this.subscriptionTier = 'creator'; // Default, will be updated from user context

    // Bind methods
    this.sendMessage = this.sendMessage.bind(this);
    this.syncMessages = this.syncMessages.bind(this);
    this.processMessageQueue = this.processMessageQueue.bind(this);
    this.checkPlatformStatus = this.checkPlatformStatus.bind(this);
    this.handleNetworkChange = this.handleNetworkChange.bind(this);
    this.handleWebSocketMessage = this.handleWebSocketMessage.bind(this);
    this.initWebSocketStatusMonitoring = this.initWebSocketStatusMonitoring.bind(this);

    // Initialize network monitoring
    this.initNetworkMonitoring();

    // Initialize platform status checking (fallback)
    this.initPlatformStatusChecking();

    // Initialize WebSocket status monitoring
    if (CONFIG.WEBSOCKET_STATUS_ENABLED) {
      this.initWebSocketStatusMonitoring();
    }

    // Initialize metrics collection
    if (CONFIG.METRICS_ENABLED && this.metricsCollector) {
      this.metricsCollector.enable();
    }

    // Initialize encryption service
    if (CONFIG.ENCRYPTION_ENABLED && this.encryptionService) {
      this.encryptionService.enable();
    }

    // Initialize deduplication service
    if (CONFIG.DEDUPLICATION_ENABLED && this.deduplicationService) {
      this.deduplicationService.enable();
    }
  }

  /**
   * Initialize network monitoring
   */
  initNetworkMonitoring() {
    networkMonitor.addEventListener('statusChange', this.handleNetworkChange);
    networkMonitor.addEventListener('reconnect', () => {
      this.processMessageQueue();
    });
  }

  /**
   * Initialize platform status checking (fallback for WebSocket)
   */
  initPlatformStatusChecking() {
    // Check platform status periodically as fallback
    setInterval(this.checkPlatformStatus, CONFIG.PLATFORM_STATUS_CHECK_INTERVAL);

    // Initial check
    this.checkPlatformStatus();
  }

  /**
   * Initialize WebSocket status monitoring for real-time platform updates
   */
  initWebSocketStatusMonitoring() {
    try {
      // Initialize WebSocket service if not already done
      if (!this.webSocketService) {
        this.webSocketService = new WebSocketService();
      }

      // Connect to platform status channel
      this.webSocketService.connect({
        channels: ['platform_status', 'social_media_status'],
        onMessage: this.handleWebSocketMessage,
        onConnect: () => {
          this.wsConnected = true;
          this.wsReconnectAttempts = 0;
          logger.info('WebSocket connected for platform status monitoring');

          // Record connection metric
          if (CONFIG.METRICS_ENABLED && this.metricsCollector) {
            this.metricsCollector.recordPlatformStatus('websocket', true, 0, {
              event: 'websocket_connected'
            });
          }
        },
        onDisconnect: () => {
          this.wsConnected = false;
          logger.warn('WebSocket disconnected for platform status monitoring');

          // Record disconnection metric
          if (CONFIG.METRICS_ENABLED && this.metricsCollector) {
            this.metricsCollector.recordPlatformStatus('websocket', false, 0, {
              event: 'websocket_disconnected'
            });
          }

          // Attempt reconnection
          this.scheduleWebSocketReconnect();
        },
        onError: (error) => {
          logger.error('WebSocket error in platform status monitoring:', error);

          // Record error metric
          if (CONFIG.METRICS_ENABLED && this.metricsCollector) {
            this.metricsCollector.recordPlatformStatus('websocket', false, 0, {
              event: 'websocket_error',
              error: error.message
            });
          }
        }
      });

      // Start heartbeat
      this.startWebSocketHeartbeat();

    } catch (error) {
      logger.error('Error initializing WebSocket status monitoring:', error);

      // Fall back to polling
      logger.info('Falling back to polling for platform status');
    }
  }

  /**
   * Handle WebSocket messages for platform status updates
   *
   * @param {Object} message - WebSocket message
   */
  handleWebSocketMessage(message) {
    try {
      if (message.type === 'platform_status_update') {
        const { platform, status, timestamp } = message.data;

        // Update platform status
        this.platformStatus[platform] = {
          available: status.available,
          lastCheck: timestamp,
          rateLimitRemaining: status.rate_limit_remaining,
          rateLimitReset: status.rate_limit_reset,
          responseTime: status.response_time
        };

        // Update rate limit information
        if (status.rate_limit_reset) {
          this.rateLimits[platform].resetTime = new Date(status.rate_limit_reset).getTime();
        }

        // Reset circuit breaker if platform is available
        if (status.available && this.isCircuitBreakerOpen(platform)) {
          this.resetCircuitBreaker(platform);
        }

        // Record platform status metric
        if (CONFIG.METRICS_ENABLED && this.metricsCollector) {
          this.metricsCollector.recordPlatformStatus(
            platform,
            status.available,
            status.response_time || 0,
            {
              rate_limit_remaining: status.rate_limit_remaining,
              source: 'websocket'
            }
          );
        }

        // Emit platform status update event
        this.emit('platformStatusUpdated', this.platformStatus);

        // Process message queue if platform became available
        if (status.available) {
          this.processMessageQueue();
        }

        this.lastPlatformStatusUpdate = Date.now();
      }
    } catch (error) {
      logger.error('Error handling WebSocket platform status message:', error);
    }
  }

  /**
   * Schedule WebSocket reconnection with exponential backoff
   */
  scheduleWebSocketReconnect() {
    if (this.wsReconnectAttempts >= CONFIG.WS_RECONNECT_ATTEMPTS) {
      logger.warn('Max WebSocket reconnect attempts reached, falling back to polling');
      return;
    }

    const backoffTime = Math.min(
      CONFIG.WS_RECONNECT_INTERVAL * Math.pow(2, this.wsReconnectAttempts),
      60000 // Max 1 minute
    );

    setTimeout(() => {
      this.wsReconnectAttempts++;
      logger.info(`Attempting WebSocket reconnection (attempt ${this.wsReconnectAttempts})`);
      this.initWebSocketStatusMonitoring();
    }, backoffTime);
  }

  /**
   * Start WebSocket heartbeat to maintain connection
   */
  startWebSocketHeartbeat() {
    if (this.wsHeartbeatTimer) {
      clearInterval(this.wsHeartbeatTimer);
    }

    this.wsHeartbeatTimer = setInterval(() => {
      if (this.webSocketService && this.wsConnected) {
        this.webSocketService.send({
          type: 'heartbeat',
          timestamp: new Date().toISOString()
        });
      }
    }, CONFIG.WS_HEARTBEAT_INTERVAL);
  }

  /**
   * Handle network status changes
   * 
   * @param {Object} data - Network status data
   */
  handleNetworkChange(data) {
    if (data.isOnline) {
      // Network is back online, process message queue
      this.processMessageQueue();
    }
  }

  /**
   * Check status of all connected social media platforms
   */
  async checkPlatformStatus() {
    if (!networkMonitor.isOnline) {
      return;
    }
    
    try {
      const response = await axios.get('/api/social-media/status');
      
      if (response.data && response.data.platforms) {
        // Update platform status
        Object.keys(response.data.platforms).forEach(platform => {
          const status = response.data.platforms[platform];
          
          this.platformStatus[platform] = {
            available: status.available,
            lastCheck: new Date().toISOString(),
            rateLimitRemaining: status.rate_limit_remaining,
            rateLimitReset: status.rate_limit_reset
          };
          
          // Update rate limit information
          if (status.rate_limit_reset) {
            this.rateLimits[platform].resetTime = new Date(status.rate_limit_reset).getTime();
          }
          
          // Reset circuit breaker if platform is available
          if (status.available && this.isCircuitBreakerOpen(platform)) {
            this.resetCircuitBreaker(platform);
          }
        });
        
        // Emit platform status update event
        this.emit('platformStatusUpdated', this.platformStatus);
      }
    } catch (error) {
      logger.error('Error checking platform status:', error);
    }
  }

  /**
   * Send a message to a social media platform with encryption, deduplication, and metrics
   *
   * @param {Object} messageData - Message data
   * @param {string} messageData.platform - Target platform
   * @param {string} messageData.conversation_id - Conversation ID
   * @param {string} messageData.content - Message content
   * @param {Array} messageData.attachments - Message attachments
   * @param {boolean} messageData.encrypt - Whether to encrypt the message
   * @param {string} messageData.user_id - User ID for metrics
   * @returns {Promise<Object>} - Sent message
   */
  async sendMessage(messageData) {
    const startTime = Date.now();
    const { platform, conversation_id, content, attachments = [], encrypt = false, user_id } = messageData;

    // Generate temporary ID for the message
    const tempId = `temp-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    // Update subscription tier from user context if available
    if (user_id && window.userContext?.subscription?.plan) {
      this.subscriptionTier = window.userContext.subscription.plan;
    }

    // Create message object with temporary ID
    let message = {
      id: tempId,
      platform,
      conversation_id,
      content,
      attachments,
      status: 'sending',
      created_at: new Date().toISOString(),
      user_id
    };

    try {
      // Check for duplicate message
      if (CONFIG.DEDUPLICATION_ENABLED && this.deduplicationService) {
        const isDuplicate = await this.deduplicationService.isDuplicate(message);
        if (isDuplicate) {
          logger.debug(`Duplicate message detected for ${platform}: ${tempId}`);

          // Record deduplication metric
          if (CONFIG.METRICS_ENABLED && this.metricsCollector) {
            this.metricsCollector.recordMessageSend(
              platform,
              this.subscriptionTier,
              'duplicate_blocked',
              Date.now() - startTime,
              { temp_id: tempId, reason: 'deduplication' }
            );
          }

          // Return duplicate status
          return {
            ...message,
            status: 'duplicate',
            duplicate_detected: true
          };
        }
      }

      // Encrypt message if requested or required
      if (CONFIG.ENCRYPTION_ENABLED && this.encryptionService &&
          (encrypt || this.encryptionService.shouldEncrypt(message))) {
        message = await this.encryptionService.encryptMessage(message);
        logger.debug(`Message encrypted for ${platform}: ${tempId}`);
      }

      // Emit message sending event
      this.emit('messageSending', message);

      // Check if we're online
      if (!networkMonitor.isOnline) {
        // Queue message for later
        this.queueMessage(message);

        // Record offline metric
        if (CONFIG.METRICS_ENABLED && this.metricsCollector) {
          this.metricsCollector.recordMessageSend(
            platform,
            this.subscriptionTier,
            'queued_offline',
            Date.now() - startTime,
            { temp_id: tempId, reason: 'network_offline' }
          );
        }

        // Show notification
        this.showErrorNotification(PLATFORM_ERROR_MESSAGES.generic.CONNECTION_ERROR);

        return message;
      }

      // Check if platform is available
      if (!this.isPlatformAvailable(platform)) {
        // Queue message for later
        this.queueMessage(message);

        // Record platform unavailable metric
        if (CONFIG.METRICS_ENABLED && this.metricsCollector) {
          this.metricsCollector.recordMessageSend(
            platform,
            this.subscriptionTier,
            'queued_platform_unavailable',
            Date.now() - startTime,
            { temp_id: tempId, reason: 'platform_unavailable' }
          );
        }

        // Show notification
        this.showErrorNotification(PLATFORM_ERROR_MESSAGES.generic.PLATFORM_UNAVAILABLE);

        return message;
      }

      // Check if we're rate limited
      if (this.isRateLimited(platform)) {
        // Queue message for later
        this.queueMessage(message);

        // Record rate limit metric
        if (CONFIG.METRICS_ENABLED && this.metricsCollector) {
          this.metricsCollector.recordRateLimitViolation(
            platform,
            this.subscriptionTier,
            this.rateLimits[platform]
          );

          this.metricsCollector.recordMessageSend(
            platform,
            this.subscriptionTier,
            'queued_rate_limited',
            Date.now() - startTime,
            { temp_id: tempId, reason: 'rate_limited' }
          );
        }

        // Show notification
        const platformMessages = PLATFORM_ERROR_MESSAGES[platform] || PLATFORM_ERROR_MESSAGES.generic;
        this.showErrorNotification(platformMessages.RATE_LIMIT);

        return message;
      }

      // Check if circuit breaker is open
      if (this.isCircuitBreakerOpen(platform)) {
        // Queue message for later
        this.queueMessage(message);

        // Record circuit breaker metric
        if (CONFIG.METRICS_ENABLED && this.metricsCollector) {
          this.metricsCollector.recordCircuitBreakerState(
            platform,
            'open',
            {
              errorCount: this.errorCounts[platform],
              resetTime: this.circuitBreakers[platform]?.resetTime
            }
          );

          this.metricsCollector.recordMessageSend(
            platform,
            this.subscriptionTier,
            'queued_circuit_breaker',
            Date.now() - startTime,
            { temp_id: tempId, reason: 'circuit_breaker_open' }
          );
        }

        // Show notification
        this.showErrorNotification(PLATFORM_ERROR_MESSAGES.generic.PLATFORM_UNAVAILABLE);

        return message;
      }

      // Send message to platform
      const response = await axios.post('/api/social-media/messages', {
        platform,
        conversation_id,
        content: message.content, // Use potentially encrypted content
        attachments,
        encryption_metadata: message._encryption || null
      });

      // Update rate limit information if provided
      if (response.headers['x-rate-limit-remaining'] && response.headers['x-rate-limit-reset']) {
        this.rateLimits[platform].remaining = parseInt(response.headers['x-rate-limit-remaining'], 10);
        this.rateLimits[platform].resetTime = new Date(response.headers['x-rate-limit-reset']).getTime();
      }

      // Reset error count
      this.resetErrorCount(platform);

      // Mark as sent for deduplication
      if (CONFIG.DEDUPLICATION_ENABLED && this.deduplicationService) {
        await this.deduplicationService.markAsSent(message);
      }

      // Record successful send metric
      if (CONFIG.METRICS_ENABLED && this.metricsCollector) {
        this.metricsCollector.recordMessageSend(
          platform,
          this.subscriptionTier,
          'success',
          Date.now() - startTime,
          {
            temp_id: tempId,
            message_id: response.data.id,
            encrypted: !!message._encryption,
            content_length: content.length
          }
        );
      }

      // Emit message sent event
      this.emit('messageSent', {
        tempId,
        message: response.data
      });

      return response.data;
    } catch (error) {
      logger.error(`Error sending message to ${platform}:`, error);

      // Increment error count
      this.incrementErrorCount(platform);

      // Update message status
      message.status = 'failed';

      // Record failure metric
      if (CONFIG.METRICS_ENABLED && this.metricsCollector) {
        this.metricsCollector.recordMessageSend(
          platform,
          this.subscriptionTier,
          'failure',
          Date.now() - startTime,
          {
            temp_id: tempId,
            error_type: error.response?.status || 'network_error',
            error_message: error.message,
            encrypted: !!message._encryption
          }
        );
      }

      // Emit message failed event
      this.emit('messageFailed', {
        tempId,
        error
      });

      // Handle specific error types
      if (error.response) {
        const { status, data } = error.response;

        // Handle rate limiting
        if (status === 429) {
          // Update rate limit information
          if (error.response.headers['x-rate-limit-reset']) {
            this.rateLimits[platform].resetTime = new Date(error.response.headers['x-rate-limit-reset']).getTime();
          } else {
            // Default to 1 hour if no reset time provided
            this.rateLimits[platform].resetTime = Date.now() + 3600000;
          }

          // Record rate limit violation
          if (CONFIG.METRICS_ENABLED && this.metricsCollector) {
            this.metricsCollector.recordRateLimitViolation(
              platform,
              this.subscriptionTier,
              this.rateLimits[platform]
            );
          }

          // Queue message for retry after rate limit reset
          this.queueMessage(message, this.rateLimits[platform].resetTime);

          // Show rate limit notification
          const platformMessages = PLATFORM_ERROR_MESSAGES[platform] || PLATFORM_ERROR_MESSAGES.generic;
          this.showErrorNotification(platformMessages.RATE_LIMIT);

          return message;
        }

        // Handle authentication errors
        if (status === 401 || status === 403) {
          const platformMessages = PLATFORM_ERROR_MESSAGES[platform] || PLATFORM_ERROR_MESSAGES.generic;
          this.showErrorNotification(platformMessages.AUTH_ERROR);

          return message;
        }

        // Handle platform-specific errors
        if (data && data.error_code) {
          const errorCode = data.error_code;

          // Handle content policy violations
          if (errorCode.includes('content_policy') || errorCode.includes('community_guidelines')) {
            const platformMessages = PLATFORM_ERROR_MESSAGES[platform] || PLATFORM_ERROR_MESSAGES.generic;
            this.showErrorNotification(platformMessages.CONTENT_POLICY || PLATFORM_ERROR_MESSAGES.generic.PLATFORM_UNAVAILABLE);

            return message;
          }

          // Handle attachment errors
          if (errorCode.includes('attachment') || errorCode.includes('media')) {
            this.showErrorNotification(PLATFORM_ERROR_MESSAGES.generic.ATTACHMENT_ERROR);

            return message;
          }
        }
      }

      // Queue message for retry
      this.queueMessage(message);

      // Show generic error notification
      this.showErrorNotification(PLATFORM_ERROR_MESSAGES.generic.CONNECTION_ERROR);

      return message;
    }
  }

  /**
   * Sync messages from a social media platform
   * 
   * @param {string} platform - Platform name
   * @param {string} accountId - Account ID
   * @returns {Promise<Object>} - Sync result
   */
  async syncMessages(platform, accountId) {
    // Update sync status
    this.syncStatus[platform] = {
      inProgress: true,
      lastAttempt: new Date().toISOString()
    };
    
    // Emit sync started event
    this.emit('syncStarted', { platform, accountId });
    
    try {
      // Check if platform is available
      if (!this.isPlatformAvailable(platform)) {
        throw new Error(`Platform ${platform} is currently unavailable`);
      }
      
      // Check if circuit breaker is open
      if (this.isCircuitBreakerOpen(platform)) {
        throw new Error(`Circuit breaker is open for ${platform}`);
      }
      
      // Sync messages
      const response = await axios.post('/api/social-media/sync', {
        platform,
        account_id: accountId
      });
      
      // Update sync status
      this.syncStatus[platform] = {
        inProgress: false,
        lastSuccess: new Date().toISOString(),
        lastAttempt: new Date().toISOString(),
        error: null
      };
      
      // Reset error count
      this.resetErrorCount(platform);
      
      // Emit sync completed event
      this.emit('syncCompleted', {
        platform,
        accountId,
        result: response.data
      });
      
      return response.data;
    } catch (error) {
      logger.error(`Error syncing messages from ${platform}:`, error);

      // Increment error count
      this.incrementErrorCount(platform);
      
      // Update sync status
      this.syncStatus[platform] = {
        inProgress: false,
        lastAttempt: new Date().toISOString(),
        error: error.message
      };
      
      // Emit sync failed event
      this.emit('syncFailed', {
        platform,
        accountId,
        error
      });
      
      // Show error notification
      this.showErrorNotification(PLATFORM_ERROR_MESSAGES.generic.SYNC_ERROR);
      
      throw error;
    }
  }

  /**
   * Queue a message for later sending
   * 
   * @param {Object} message - Message to queue
   * @param {number} nextRetryTime - Next retry time (optional)
   */
  queueMessage(message, nextRetryTime = null) {
    // Add to queue if not already there
    if (!this.messageQueue.some(m => m.id === message.id)) {
      this.messageQueue.push({
        ...message,
        retryCount: 0,
        nextRetryTime: nextRetryTime || Date.now() + CONFIG.INITIAL_RETRY_DELAY
      });

      logger.debug(`Message queued for later sending to ${message.platform}: ${message.id}`);
      
      // Emit message queued event
      this.emit('messageQueued', message);
    }
  }

  /**
   * Process the message queue with enhanced metrics and error handling
   */
  async processMessageQueue() {
    if (!networkMonitor.isOnline || this.messageQueue.length === 0) {
      return;
    }

    const startTime = Date.now();
    const initialQueueSize = this.messageQueue.length;
    let successCount = 0;
    let failureCount = 0;

    logger.debug(`Processing social media message queue (${this.messageQueue.length} messages)`);

    // Get messages that are ready to be sent
    const currentTime = Date.now();
    const readyMessages = this.messageQueue.filter(m => m.nextRetryTime <= currentTime);

    // Process each message
    for (const queuedMessage of readyMessages) {
      const { platform } = queuedMessage;

      // Skip if platform is unavailable or rate limited
      if (!this.isPlatformAvailable(platform) || this.isRateLimited(platform) || this.isCircuitBreakerOpen(platform)) {
        continue;
      }

      try {
        // Remove from queue
        this.messageQueue = this.messageQueue.filter(m => m.id !== queuedMessage.id);

        // Update status
        queuedMessage.status = 'sending';
        this.emit('messageStatusChanged', queuedMessage);

        // Send message
        const response = await axios.post('/api/social-media/messages', {
          platform: queuedMessage.platform,
          conversation_id: queuedMessage.conversation_id,
          content: queuedMessage.content,
          attachments: queuedMessage.attachments || [],
          encryption_metadata: queuedMessage._encryption || null
        });

        // Mark as sent for deduplication
        if (CONFIG.DEDUPLICATION_ENABLED && this.deduplicationService) {
          await this.deduplicationService.markAsSent(queuedMessage);
        }

        // Emit message sent event
        this.emit('messageSent', {
          tempId: queuedMessage.id,
          message: response.data
        });

        successCount++;
      } catch (error) {
        logger.error(`Error sending queued message to ${platform} (${queuedMessage.id}):`, error);

        // Increment retry count
        queuedMessage.retryCount = (queuedMessage.retryCount || 0) + 1;

        // Calculate next retry time with exponential backoff
        const backoffTime = Math.min(
          CONFIG.INITIAL_RETRY_DELAY * Math.pow(CONFIG.RETRY_BACKOFF_FACTOR, queuedMessage.retryCount),
          CONFIG.MAX_RETRY_DELAY
        );
        const jitter = Math.random() * 2000; // Add jitter
        queuedMessage.nextRetryTime = Date.now() + backoffTime + jitter;

        // If we haven't reached max retries, add back to queue
        if (queuedMessage.retryCount < CONFIG.MAX_RETRY_ATTEMPTS) {
          this.messageQueue.push(queuedMessage);
        } else {
          // Mark as permanently failed
          queuedMessage.status = 'failed';
          this.emit('messageStatusChanged', queuedMessage);

          // Show error notification
          this.showErrorNotification({
            title: 'Message Could Not Be Sent',
            message: `We couldn't send your message to ${platform} after multiple attempts.`,
            action: 'Please try again later or contact support if the issue persists.',
            type: 'error'
          });
        }

        failureCount++;
      }
    }

    // Record queue processing metrics
    if (CONFIG.METRICS_ENABLED && this.metricsCollector) {
      const processingTime = Date.now() - startTime;
      this.metricsCollector.recordQueueProcessing(
        initialQueueSize,
        processingTime,
        successCount,
        failureCount
      );
    }

    // Log queue processing summary
    if (successCount > 0 || failureCount > 0) {
      logger.info(`Queue processing completed: ${successCount} success, ${failureCount} failures, ${Date.now() - startTime}ms`);
    }
  }

  /**
   * Check if a platform is currently available
   * 
   * @param {string} platform - Platform name
   * @returns {boolean} - Whether platform is available
   */
  isPlatformAvailable(platform) {
    return this.platformStatus[platform]?.available || false;
  }

  /**
   * Check if a platform is currently rate limited
   * 
   * @param {string} platform - Platform name
   * @returns {boolean} - Whether platform is rate limited
   */
  isRateLimited(platform) {
    const rateLimit = this.rateLimits[platform];
    
    if (!rateLimit || !rateLimit.resetTime) {
      return false;
    }
    
    return Date.now() < rateLimit.resetTime;
  }

  /**
   * Get time remaining until rate limit reset
   * 
   * @param {string} platform - Platform name
   * @returns {number} - Milliseconds until reset, or 0 if not rate limited
   */
  getRateLimitResetTime(platform) {
    const rateLimit = this.rateLimits[platform];
    
    if (!rateLimit || !rateLimit.resetTime) {
      return 0;
    }
    
    const timeRemaining = rateLimit.resetTime - Date.now();
    return Math.max(0, timeRemaining);
  }

  /**
   * Increment error count for a specific platform
   * 
   * @param {string} platform - Platform name
   */
  incrementErrorCount(platform) {
    this.errorCounts[platform] = (this.errorCounts[platform] || 0) + 1;
    
    // Check if we should open circuit breaker
    if (this.errorCounts[platform] >= 3) {
      this.openCircuitBreaker(platform);
    }
  }

  /**
   * Reset error count for a specific platform
   * 
   * @param {string} platform - Platform name
   */
  resetErrorCount(platform) {
    this.errorCounts[platform] = 0;
  }

  /**
   * Open circuit breaker for a specific platform
   * 
   * @param {string} platform - Platform name
   */
  openCircuitBreaker(platform) {
    logger.info(`Opening circuit breaker for ${platform}`);
    
    this.circuitBreakers[platform] = {
      open: true,
      resetTime: Date.now() + this.circuitBreakerResetTimeout
    };
    
    // Update platform status
    this.platformStatus[platform] = {
      ...this.platformStatus[platform],
      available: false,
      lastCheck: new Date().toISOString()
    };
    
    // Emit platform status update event
    this.emit('platformStatusUpdated', this.platformStatus);
    
    // Schedule circuit breaker reset
    setTimeout(() => {
      this.resetCircuitBreaker(platform);
    }, this.circuitBreakerResetTimeout);
  }

  /**
   * Reset circuit breaker for a specific platform
   * 
   * @param {string} platform - Platform name
   */
  resetCircuitBreaker(platform) {
    logger.info(`Resetting circuit breaker for ${platform}`);
    
    this.circuitBreakers[platform] = {
      open: false,
      resetTime: null
    };
    
    this.resetErrorCount(platform);
    
    // Update platform status
    this.platformStatus[platform] = {
      ...this.platformStatus[platform],
      available: true,
      lastCheck: new Date().toISOString()
    };
    
    // Emit platform status update event
    this.emit('platformStatusUpdated', this.platformStatus);
    
    // Process message queue
    this.processMessageQueue();
  }

  /**
   * Check if circuit breaker is open for a specific platform
   * 
   * @param {string} platform - Platform name
   * @returns {boolean} - Whether circuit breaker is open
   */
  isCircuitBreakerOpen(platform) {
    return this.circuitBreakers[platform]?.open || false;
  }

  /**
   * Show error notification
   * 
   * @param {Object} errorInfo - Error information
   */
  showErrorNotification(errorInfo) {
    // Use toast for notifications
    toast.error(
      <div>
        <strong>{errorInfo.title}</strong>
        <p>{errorInfo.message}</p>
        <p className="action-text">
          {errorInfo.actionLink ? (
            <a href={errorInfo.actionLink}>{errorInfo.action}</a>
          ) : (
            errorInfo.action
          )}
        </p>
      </div>,
      {
        autoClose: CONFIG.NOTIFICATION_AUTO_CLOSE,
        closeOnClick: true,
        pauseOnHover: true
      }
    );
  }

  /**
   * Add event listener
   * 
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  addListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    
    this.listeners.get(event).push(callback);
  }

  /**
   * Remove event listener
   * 
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  removeListener(event, callback) {
    if (!this.listeners.has(event)) {
      return;
    }
    
    const callbacks = this.listeners.get(event);
    this.listeners.set(event, callbacks.filter(cb => cb !== callback));
  }

  /**
   * Emit event to all listeners
   *
   * @param {string} event - Event name
   * @param {any} data - Event data
   */
  emit(event, data) {
    if (!this.listeners.has(event)) {
      return;
    }

    const callbacks = this.listeners.get(event);
    callbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        logger.error(`Error in ${event} listener:`, error);
      }
    });
  }

  /**
   * Update user subscription tier for metrics
   *
   * @param {string} tier - Subscription tier (creator, accelerator, dominator)
   */
  updateSubscriptionTier(tier) {
    this.subscriptionTier = tier;
    logger.debug(`Updated subscription tier to: ${tier}`);
  }

  /**
   * Get comprehensive service status
   *
   * @returns {Object} Service status
   */
  getServiceStatus() {
    return {
      // Core service status
      enabled: true,
      subscription_tier: this.subscriptionTier,
      queue_size: this.messageQueue.length,
      platform_status: this.platformStatus,
      circuit_breakers: this.circuitBreakers,
      rate_limits: this.rateLimits,

      // Enhancement status
      metrics: {
        enabled: CONFIG.METRICS_ENABLED,
        collector_status: this.metricsCollector?.getMetricsSummary() || null
      },
      encryption: {
        enabled: CONFIG.ENCRYPTION_ENABLED,
        service_status: this.encryptionService?.getStatus() || null
      },
      deduplication: {
        enabled: CONFIG.DEDUPLICATION_ENABLED,
        service_stats: this.deduplicationService?.getStats() || null
      },
      websocket: {
        enabled: CONFIG.WEBSOCKET_STATUS_ENABLED,
        connected: this.wsConnected,
        reconnect_attempts: this.wsReconnectAttempts,
        last_status_update: this.lastPlatformStatusUpdate
      },

      // Network and connectivity
      network_online: networkMonitor.isOnline,
      last_platform_check: Math.max(
        ...Object.values(this.platformStatus).map(s =>
          s.lastCheck ? new Date(s.lastCheck).getTime() : 0
        )
      ),

      // Performance metrics
      active_listeners: this.listeners.size,
      error_counts: this.errorCounts
    };
  }

  /**
   * Enable or disable specific enhancements
   *
   * @param {Object} options - Enhancement options
   */
  configureEnhancements(options = {}) {
    if (options.metrics !== undefined) {
      if (options.metrics && this.metricsCollector) {
        this.metricsCollector.enable();
      } else if (!options.metrics && this.metricsCollector) {
        this.metricsCollector.disable();
      }
    }

    if (options.encryption !== undefined) {
      if (options.encryption && this.encryptionService) {
        this.encryptionService.enable();
      } else if (!options.encryption && this.encryptionService) {
        this.encryptionService.disable();
      }
    }

    if (options.deduplication !== undefined) {
      if (options.deduplication && this.deduplicationService) {
        this.deduplicationService.enable();
      } else if (!options.deduplication && this.deduplicationService) {
        this.deduplicationService.disable();
      }
    }

    if (options.websocket !== undefined) {
      if (options.websocket && !this.wsConnected) {
        this.initWebSocketStatusMonitoring();
      } else if (!options.websocket && this.webSocketService) {
        this.webSocketService.disconnect();
        this.wsConnected = false;
      }
    }

    logger.info('Enhanced social media messaging configuration updated', options);
  }

  /**
   * Cleanup resources and connections
   */
  cleanup() {
    // Clear timers
    if (this.wsHeartbeatTimer) {
      clearInterval(this.wsHeartbeatTimer);
      this.wsHeartbeatTimer = null;
    }

    // Disconnect WebSocket
    if (this.webSocketService) {
      this.webSocketService.disconnect();
      this.wsConnected = false;
    }

    // Flush metrics
    if (this.metricsCollector) {
      this.metricsCollector.flushMetrics().catch(error => {
        logger.error('Error flushing metrics during cleanup:', error);
      });
    }

    // Clear listeners
    this.listeners.clear();

    logger.info('Enhanced social media messaging service cleaned up');
  }
}

// Create singleton instance
const enhancedSocialMediaMessaging = new EnhancedSocialMediaMessaging();

export default enhancedSocialMediaMessaging;
