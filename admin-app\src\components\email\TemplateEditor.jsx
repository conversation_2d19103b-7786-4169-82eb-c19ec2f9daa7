import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Tabs,
  Tab,
  Grid,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  Divider
} from '@mui/material';
import {
  Close as CloseIcon,
  Preview as PreviewIcon,
  Send as SendIcon,
  Code as CodeIcon,
  Palette as DesignIcon,
  Settings as SettingsIcon,
  Add as AddIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';

const TemplateEditor = ({ 
  open, 
  onClose, 
  onSave, 
  template = null, 
  mode = 'create' 
}) => {
  const [currentTab, setCurrentTab] = useState(0);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    template_type: 'transactional',
    subject: '',
    html_content: '',
    text_content: '',
    preview_text: '',
    variables: [],
    default_values: {},
    category: '',
    tags: []
  });
  const [newVariable, setNewVariable] = useState('');
  const [newTag, setNewTag] = useState('');
  const [errors, setErrors] = useState({});
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  useEffect(() => {
    if (template && mode === 'edit') {
      setFormData({
        name: template.name || '',
        description: template.description || '',
        template_type: template.template_type || 'transactional',
        subject: template.subject || '',
        html_content: template.html_content || '',
        text_content: template.text_content || '',
        preview_text: template.preview_text || '',
        variables: template.variables || [],
        default_values: template.default_values || {},
        category: template.category || '',
        tags: template.tags || []
      });
    } else {
      // Reset form for create mode
      setFormData({
        name: '',
        description: '',
        template_type: 'transactional',
        subject: '',
        html_content: '',
        text_content: '',
        preview_text: '',
        variables: [],
        default_values: {},
        category: '',
        tags: []
      });
    }
    setErrors({});
    setCurrentTab(0);
  }, [template, mode, open]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const handleAddVariable = () => {
    if (newVariable && !formData.variables.includes(newVariable)) {
      setFormData(prev => ({
        ...prev,
        variables: [...prev.variables, newVariable],
        default_values: {
          ...prev.default_values,
          [newVariable]: ''
        }
      }));
      setNewVariable('');
    }
  };

  const handleRemoveVariable = (variable) => {
    setFormData(prev => {
      const newDefaultValues = { ...prev.default_values };
      delete newDefaultValues[variable];
      
      return {
        ...prev,
        variables: prev.variables.filter(v => v !== variable),
        default_values: newDefaultValues
      };
    });
  };

  const handleAddTag = () => {
    if (newTag && !formData.tags.includes(newTag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag]
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tag) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Template name is required';
    }

    if (!formData.subject.trim()) {
      newErrors.subject = 'Subject is required';
    }

    if (!formData.html_content.trim()) {
      newErrors.html_content = 'HTML content is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (validateForm()) {
      try {
        await onSave(formData);
      } catch (error) {
        // Error handling is done in the parent component
        // This ensures the editor stays open if there's an error
        console.error('Error saving template:', error);
      }
    }
  };

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const renderBasicInfo = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Template Name"
          value={formData.name}
          onChange={(e) => handleInputChange('name', e.target.value)}
          error={!!errors.name}
          helperText={errors.name}
          required
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <FormControl fullWidth>
          <InputLabel>Template Type</InputLabel>
          <Select
            value={formData.template_type}
            label="Template Type"
            onChange={(e) => handleInputChange('template_type', e.target.value)}
          >
            <MenuItem value="transactional">Transactional</MenuItem>
            <MenuItem value="marketing">Marketing</MenuItem>
            <MenuItem value="system">System</MenuItem>
            <MenuItem value="notification">Notification</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          multiline
          rows={3}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Category"
          value={formData.category}
          onChange={(e) => handleInputChange('category', e.target.value)}
          placeholder="e.g., Welcome, Billing, Notifications"
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Tags
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, mb: 1, flexWrap: 'wrap' }}>
            {formData.tags.map((tag, index) => (
              <Chip
                key={index}
                label={tag}
                onDelete={() => handleRemoveTag(tag)}
                size="small"
                color="primary"
                variant="outlined"
              />
            ))}
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <TextField
              size="small"
              placeholder="Add tag"
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
            />
            <IconButton size="small" onClick={handleAddTag}>
              <AddIcon />
            </IconButton>
          </Box>
        </Box>
      </Grid>
    </Grid>
  );

  const renderContent = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Subject Line"
          value={formData.subject}
          onChange={(e) => handleInputChange('subject', e.target.value)}
          error={!!errors.subject}
          helperText={errors.subject || 'Use {{variable}} for dynamic content'}
          required
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Preview Text"
          value={formData.preview_text}
          onChange={(e) => handleInputChange('preview_text', e.target.value)}
          helperText="Text shown in email client preview"
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="HTML Content"
          value={formData.html_content}
          onChange={(e) => handleInputChange('html_content', e.target.value)}
          error={!!errors.html_content}
          helperText={errors.html_content}
          multiline
          rows={12}
          required
          sx={{
            '& .MuiInputBase-input': {
              fontFamily: 'monospace',
              fontSize: '0.875rem'
            }
          }}
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Plain Text Content (Optional)"
          value={formData.text_content}
          onChange={(e) => handleInputChange('text_content', e.target.value)}
          multiline
          rows={6}
          helperText="Fallback for email clients that don't support HTML"
        />
      </Grid>
    </Grid>
  );

  const renderVariables = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Template Variables
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Define variables that can be used in your template with {`{{variable_name}}`} syntax.
      </Typography>

      {/* Add Variable */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <TextField
          label="Variable Name"
          value={newVariable}
          onChange={(e) => setNewVariable(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleAddVariable()}
          placeholder="e.g., user_name, company_name"
        />
        <Button
          variant="outlined"
          onClick={handleAddVariable}
          startIcon={<AddIcon />}
        >
          Add Variable
        </Button>
      </Box>

      {/* Variables List */}
      {formData.variables.length > 0 ? (
        <Grid container spacing={2}>
          {formData.variables.map((variable, index) => (
            <Grid item xs={12} md={6} key={index}>
              <Box
                sx={{
                  p: 2,
                  border: 1,
                  borderColor: 'divider',
                  borderRadius: 2,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2
                }}
              >
                <Box sx={{ flex: 1 }}>
                  <Typography variant="subtitle2">
                    {`{{${variable}}}`}
                  </Typography>
                  <TextField
                    size="small"
                    fullWidth
                    placeholder="Default value"
                    value={formData.default_values[variable] || ''}
                    onChange={(e) => handleInputChange('default_values', {
                      ...formData.default_values,
                      [variable]: e.target.value
                    })}
                    sx={{ mt: 1 }}
                  />
                </Box>
                <IconButton
                  size="small"
                  onClick={() => handleRemoveVariable(variable)}
                  color="error"
                >
                  <DeleteIcon />
                </IconButton>
              </Box>
            </Grid>
          ))}
        </Grid>
      ) : (
        <Alert severity="info">
          No variables defined. Add variables to make your template dynamic.
        </Alert>
      )}
    </Box>
  );

  const renderTabContent = () => {
    switch (currentTab) {
      case 0:
        return renderBasicInfo();
      case 1:
        return renderContent();
      case 2:
        return renderVariables();
      default:
        return null;
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          minHeight: '80vh'
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        pb: 1
      }}>
        <Typography variant="h5" component="h2">
          {mode === 'create' ? 'Create Email Template' : 'Edit Email Template'}
        </Typography>
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <Divider />

      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={currentTab} onChange={handleTabChange}>
          <Tab icon={<SettingsIcon />} label="Basic Info" iconPosition="start" />
          <Tab icon={<CodeIcon />} label="Content" iconPosition="start" />
          <Tab icon={<DesignIcon />} label="Variables" iconPosition="start" />
        </Tabs>
      </Box>

      <DialogContent sx={{ p: 3 }}>
        {renderTabContent()}
      </DialogContent>

      <Divider />

      <DialogActions sx={{ p: 3, gap: 1 }}>
        <Button onClick={onClose} color="inherit">
          Cancel
        </Button>
        <Button
          variant="outlined"
          startIcon={<PreviewIcon />}
          onClick={() => setIsPreviewMode(true)}
        >
          Preview
        </Button>
        <Button
          variant="contained"
          onClick={handleSave}
          sx={{
            background: 'linear-gradient(135deg, #6C4BFA 0%, #9B85FF 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #5A3FE8 0%, #8973ED 100%)',
            }
          }}
        >
          {mode === 'create' ? 'Create Template' : 'Save Changes'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TemplateEditor;
