import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Rating,
} from '@mui/material';
import {
  Search as SearchIcon,
  Article as ArticleIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
  Star as StarIcon,
  TrendingUp as TrendingIcon,
  Help as HelpIcon,
} from '@mui/icons-material';

const KnowledgeBase = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedArticle, setSelectedArticle] = useState(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);

  // Mock data for demonstration
  const articles = [
    {
      id: '1',
      title: 'How to Connect Your Instagram Account',
      summary: 'Step-by-step guide to connect your Instagram business account to the platform',
      category: 'technical',
      tags: ['instagram', 'social-media', 'setup'],
      difficulty_level: 'beginner',
      is_featured: true,
      view_count: 1250,
      helpful_votes: 45,
      not_helpful_votes: 3,
      helpfulness_score: 0.94,
      author_name: 'Sarah Johnson',
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-01-20T14:15:00Z',
      content: `# How to Connect Your Instagram Account

## Prerequisites
Before you begin, make sure you have:
- An Instagram Business or Creator account
- Admin access to your Instagram account
- The latest version of our platform

## Step-by-Step Instructions

### 1. Navigate to Social Media Settings
1. Log into your account
2. Go to Settings > Social Media Connections
3. Click on "Connect Instagram"

### 2. Authorize the Connection
1. You'll be redirected to Instagram
2. Log in with your Instagram credentials
3. Click "Authorize" to grant permissions

### 3. Verify the Connection
1. Return to our platform
2. You should see your Instagram account listed
3. Test the connection by posting a sample message

## Troubleshooting
If you encounter issues:
- Make sure you're using a Business or Creator account
- Check that you have the necessary permissions
- Try disconnecting and reconnecting

## Need Help?
If you're still having trouble, contact our support <NAME_EMAIL>`
    },
    {
      id: '2',
      title: 'Understanding Your Analytics Dashboard',
      summary: 'Learn how to interpret and use the analytics data in your dashboard',
      category: 'general',
      tags: ['analytics', 'dashboard', 'metrics'],
      difficulty_level: 'intermediate',
      is_featured: false,
      view_count: 890,
      helpful_votes: 32,
      not_helpful_votes: 5,
      helpfulness_score: 0.86,
      author_name: 'Mike Chen',
      created_at: '2024-01-10T09:15:00Z',
      updated_at: '2024-01-18T11:30:00Z',
      content: 'Detailed analytics guide content...'
    },
    {
      id: '3',
      title: 'Billing and Subscription Management',
      summary: 'How to manage your subscription, update payment methods, and understand billing',
      category: 'billing',
      tags: ['billing', 'subscription', 'payment'],
      difficulty_level: 'beginner',
      is_featured: true,
      view_count: 2100,
      helpful_votes: 78,
      not_helpful_votes: 8,
      helpfulness_score: 0.91,
      author_name: 'Emily Rodriguez',
      created_at: '2024-01-05T16:45:00Z',
      updated_at: '2024-01-22T10:20:00Z',
      content: 'Billing management guide content...'
    },
  ];

  const categories = [
    { value: '', label: 'All Categories' },
    { value: 'technical', label: 'Technical' },
    { value: 'billing', label: 'Billing' },
    { value: 'account', label: 'Account' },
    { value: 'general', label: 'General' },
    { value: 'integration', label: 'Integration' },
    { value: 'training', label: 'Training' },
  ];

  const difficultyLevels = [
    { value: 'beginner', label: 'Beginner', color: 'success' },
    { value: 'intermediate', label: 'Intermediate', color: 'warning' },
    { value: 'advanced', label: 'Advanced', color: 'error' },
  ];

  const filteredArticles = articles.filter(article => {
    const matchesSearch = !searchQuery || 
      article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.summary.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = !selectedCategory || article.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const handleViewArticle = (article) => {
    setSelectedArticle(article);
    setViewDialogOpen(true);
  };

  const handleSearch = () => {
    // Search is handled by the filter effect
  };

  const getDifficultyColor = (level) => {
    const difficulty = difficultyLevels.find(d => d.value === level);
    return difficulty ? difficulty.color : 'default';
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'technical':
        return 'primary';
      case 'billing':
        return 'secondary';
      case 'account':
        return 'info';
      case 'general':
        return 'default';
      case 'integration':
        return 'warning';
      case 'training':
        return 'success';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      {/* Search and Filters */}
      <Card sx={{ mb: 3 }}>
        <CardHeader 
          title="Knowledge Base Search"
          action={
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => {/* TODO: Implement add article */}}
            >
              New Article
            </Button>
          }
        />
        <CardContent>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                size="small"
                label="Search articles"
                placeholder="Search by title, content, or tags..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  endAdornment: (
                    <IconButton size="small" onClick={handleSearch}>
                      <SearchIcon />
                    </IconButton>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Category</InputLabel>
                <Select
                  value={selectedCategory}
                  label="Category"
                  onChange={(e) => setSelectedCategory(e.target.value)}
                >
                  {categories.map((category) => (
                    <MenuItem key={category.value} value={category.value}>
                      {category.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <Box display="flex" gap={1}>
                <Button
                  size="small"
                  variant={searchQuery === '' && selectedCategory === '' ? "contained" : "outlined"}
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('');
                  }}
                >
                  All Articles
                </Button>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => setSearchQuery('featured')}
                >
                  Featured
                </Button>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Knowledge Base Statistics */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader title="Knowledge Base Stats" />
            <CardContent>
              <Box mb={2}>
                <Typography variant="h4" color="primary">
                  {articles.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Articles
                </Typography>
              </Box>
              <Box mb={2}>
                <Typography variant="h4" color="primary">
                  {articles.reduce((sum, a) => sum + a.view_count, 0).toLocaleString()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Views
                </Typography>
              </Box>
              <Box mb={2}>
                <Typography variant="h4" color="primary">
                  {(articles.reduce((sum, a) => sum + a.helpfulness_score, 0) / articles.length * 100).toFixed(0)}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Average Helpfulness
                </Typography>
              </Box>
              <Box>
                <Typography variant="h4" color="primary">
                  {articles.filter(a => a.is_featured).length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Featured Articles
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Articles List */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardHeader 
              title={`Articles (${filteredArticles.length})`}
              subheader={searchQuery ? `Showing results for "${searchQuery}"` : 'All articles'}
            />
            <CardContent sx={{ p: 0 }}>
              {loading ? (
                <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
                  <CircularProgress />
                </Box>
              ) : filteredArticles.length === 0 ? (
                <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" minHeight="300px">
                  <HelpIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    No articles found
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Try adjusting your search criteria or create a new article
                  </Typography>
                </Box>
              ) : (
                <List>
                  {filteredArticles.map((article, index) => (
                    <React.Fragment key={article.id}>
                      <ListItem alignItems="flex-start">
                        <ListItemIcon>
                          <ArticleIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box display="flex" alignItems="center" gap={1} mb={1}>
                              <Typography variant="body1" fontWeight="bold">
                                {article.title}
                              </Typography>
                              {article.is_featured && (
                                <StarIcon color="warning" fontSize="small" />
                              )}
                              <Chip
                                label={article.category}
                                color={getCategoryColor(article.category)}
                                size="small"
                                sx={{ textTransform: 'capitalize' }}
                              />
                              <Chip
                                label={article.difficulty_level}
                                color={getDifficultyColor(article.difficulty_level)}
                                size="small"
                                sx={{ textTransform: 'capitalize' }}
                              />
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="body2" color="text.primary" paragraph>
                                {article.summary}
                              </Typography>
                              <Box display="flex" alignItems="center" gap={2} mb={1}>
                                <Box display="flex" alignItems="center" gap={0.5}>
                                  <TrendingIcon fontSize="small" color="action" />
                                  <Typography variant="caption" color="text.secondary">
                                    {article.view_count.toLocaleString()} views
                                  </Typography>
                                </Box>
                                <Box display="flex" alignItems="center" gap={0.5}>
                                  <ThumbUpIcon fontSize="small" color="success" />
                                  <Typography variant="caption" color="text.secondary">
                                    {article.helpful_votes}
                                  </Typography>
                                  <ThumbDownIcon fontSize="small" color="error" />
                                  <Typography variant="caption" color="text.secondary">
                                    {article.not_helpful_votes}
                                  </Typography>
                                </Box>
                                <Typography variant="caption" color="text.secondary">
                                  By {article.author_name}
                                </Typography>
                              </Box>
                              <Box display="flex" flexWrap="wrap" gap={0.5}>
                                {article.tags.map((tag) => (
                                  <Chip
                                    key={tag}
                                    label={tag}
                                    size="small"
                                    variant="outlined"
                                    sx={{ fontSize: '0.7rem', height: 20 }}
                                  />
                                ))}
                              </Box>
                            </Box>
                          }
                        />
                        <ListItemSecondaryAction>
                          <Box display="flex" gap={0.5}>
                            <Tooltip title="View Article">
                              <IconButton 
                                size="small" 
                                onClick={() => handleViewArticle(article)}
                              >
                                <ViewIcon />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Edit Article">
                              <IconButton size="small">
                                <EditIcon />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </ListItemSecondaryAction>
                      </ListItem>
                      {index < filteredArticles.length - 1 && <Divider variant="inset" component="li" />}
                    </React.Fragment>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Article View Dialog */}
      <Dialog 
        open={viewDialogOpen} 
        onClose={() => setViewDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            <ArticleIcon />
            {selectedArticle?.title}
            {selectedArticle?.is_featured && (
              <StarIcon color="warning" />
            )}
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedArticle && (
            <Box>
              <Box display="flex" gap={1} mb={2}>
                <Chip
                  label={selectedArticle.category}
                  color={getCategoryColor(selectedArticle.category)}
                  size="small"
                  sx={{ textTransform: 'capitalize' }}
                />
                <Chip
                  label={selectedArticle.difficulty_level}
                  color={getDifficultyColor(selectedArticle.difficulty_level)}
                  size="small"
                  sx={{ textTransform: 'capitalize' }}
                />
              </Box>
              
              <Typography variant="body1" paragraph>
                {selectedArticle.summary}
              </Typography>
              
              <Box display="flex" alignItems="center" gap={2} mb={3}>
                <Typography variant="caption" color="text.secondary">
                  {selectedArticle.view_count.toLocaleString()} views
                </Typography>
                <Box display="flex" alignItems="center" gap={0.5}>
                  <Rating 
                    value={selectedArticle.helpfulness_score * 5} 
                    readOnly 
                    size="small" 
                  />
                  <Typography variant="caption" color="text.secondary">
                    ({selectedArticle.helpful_votes + selectedArticle.not_helpful_votes} votes)
                  </Typography>
                </Box>
              </Box>
              
              <Typography variant="body2" component="div" sx={{ whiteSpace: 'pre-wrap' }}>
                {selectedArticle.content}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
          <Button variant="contained">Edit Article</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default KnowledgeBase;
