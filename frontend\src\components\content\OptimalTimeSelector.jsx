/**
 * Enhanced OptimalTimeSelector Component - Enterprise-grade optimal posting time selection
 * Features: Intelligent time recommendations, comprehensive analytics integration,
 * subscription-based feature gating, accessibility compliance, error handling,
 * time zone management, and ACE Social platform integration
 */

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Alert,
  useTheme,
  alpha,
  LinearProgress,
  Fade,
  Tooltip,
  IconButton,
  Stack
} from '@mui/material';
import {
  AccessTime as AccessTimeIcon,
  Schedule as ScheduleIcon,
  Check as CheckIcon,
  TrendingUp as TrendingUpIcon,
  Refresh as RefreshIcon,
  Close as CloseIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as CalendarIcon
} from '@mui/icons-material';

// Enhanced context and hook imports
import { useNotification } from '../../hooks/useNotification';
import { useAuth } from '../../hooks/useAuth';
import { useSubscription } from '../../hooks/useSubscription';
import { useAccessibility } from '../../hooks/useAccessibility';
import api from '../../api';

// Enhanced component imports
import ErrorBoundary from '../common/ErrorBoundary';

// Analytics hook implementation
const useAnalytics = () => ({
  trackEvent: (eventName, data = {}) => {
    if (typeof window !== 'undefined' && window.analytics) {
      window.analytics.track(eventName, {
        ...data,
        component: 'OptimalTimeSelector',
        timestamp: new Date().toISOString()
      });
    }
  },
  trackError: (error, context = {}) => {
    if (typeof window !== 'undefined' && window.analytics) {
      window.analytics.track('Error', {
        error: error.message,
        stack: error.stack,
        context,
        component: 'OptimalTimeSelector',
        timestamp: new Date().toISOString()
      });
    }
    console.error('OptimalTimeSelector Error:', error, context);
  }
});

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Note: Days of the week mapping can be added in future iterations if needed

// Confidence level configurations
const CONFIDENCE_LEVELS = {
  HIGH: {
    label: 'High',
    color: '#4caf50',
    threshold: 0.8,
    description: 'Strong recommendation based on historical data'
  },
  MEDIUM: {
    label: 'Medium',
    color: '#ff9800',
    threshold: 0.6,
    description: 'Good recommendation with moderate confidence'
  },
  LOW: {
    label: 'Low',
    color: '#f44336',
    threshold: 0.4,
    description: 'Basic recommendation with limited data'
  }
};

// Component configuration
const COMPONENT_CONFIG = {
  ANIMATION_DURATION: 300,
  REFRESH_INTERVAL: 300000, // 5 minutes
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
  SKELETON_COUNT: 6,
  MAX_TIME_SLOTS_PER_DAY: 8,
  DEFAULT_TIMEZONE: 'UTC'
};

// Subscription tier features
const SUBSCRIPTION_FEATURES = {
  creator: {
    enableAdvancedAnalytics: false,
    enableCustomTimeZones: false,
    enableBulkScheduling: false,
    maxRecommendations: 3,
    enableAudienceInsights: false,
    enableCompetitorAnalysis: false
  },
  accelerator: {
    enableAdvancedAnalytics: true,
    enableCustomTimeZones: true,
    enableBulkScheduling: false,
    maxRecommendations: 6,
    enableAudienceInsights: true,
    enableCompetitorAnalysis: false
  },
  dominator: {
    enableAdvancedAnalytics: true,
    enableCustomTimeZones: true,
    enableBulkScheduling: true,
    maxRecommendations: 12,
    enableAudienceInsights: true,
    enableCompetitorAnalysis: true
  }
};

// Platform-specific configurations
const PLATFORM_CONFIGS = {
  linkedin: {
    name: 'LinkedIn',
    icon: '💼',
    color: '#0077b5',
    optimalDays: ['Tuesday', 'Wednesday', 'Thursday'],
    peakHours: [8, 9, 10, 17, 18]
  },
  twitter: {
    name: 'Twitter',
    icon: '🐦',
    color: '#1da1f2',
    optimalDays: ['Monday', 'Tuesday', 'Wednesday'],
    peakHours: [9, 10, 15, 16, 17]
  },
  facebook: {
    name: 'Facebook',
    icon: '📘',
    color: '#1877f2',
    optimalDays: ['Tuesday', 'Wednesday', 'Thursday'],
    peakHours: [9, 13, 15, 19, 20]
  },
  instagram: {
    name: 'Instagram',
    icon: '📷',
    color: '#e4405f',
    optimalDays: ['Monday', 'Tuesday', 'Friday'],
    peakHours: [11, 13, 17, 19, 20]
  }
};

/**
 * Enhanced enterprise-grade optimal time selector component
 * @param {Object} props - Component props
 * @param {boolean} props.open - Whether the dialog is open
 * @param {Function} props.onClose - Callback when dialog is closed
 * @param {Function} props.onSelectTime - Callback when time is selected
 * @param {Array} props.selectedPlatforms - Array of selected platforms
 * @param {string} props.userPlan - User subscription plan
 * @param {boolean} props.enableAnalytics - Enable analytics tracking
 * @param {boolean} props.enableAdvancedFeatures - Enable advanced features
 * @param {string} props.defaultTimezone - Default timezone
 * @param {string} props.testId - Test identifier
 * @param {string} props.ariaLabel - Accessibility label
 */
const OptimalTimeSelector = memo(forwardRef(({
  // Core props
  open = false,
  onClose,
  onSelectTime,
  selectedPlatforms = [],

  // Enhanced props
  userPlan = 'creator',
  enableAnalytics = true,
  enableAdvancedFeatures = false,
  defaultTimezone = COMPONENT_CONFIG.DEFAULT_TIMEZONE,

  // E-commerce props
  selectedProducts = [],
  enableProductPerformance = false,
  productMetrics = null,

  // Testing props
  testId = 'optimal-time-selector',

  // Accessibility props (reserved for future use)
  // ariaLabel
}, ref) => {
  // ===========================
  // HOOKS & CONTEXT
  // ===========================

  const theme = useTheme();
  // Note: isMobile can be used for responsive design in future iterations
  const { showSuccessNotification, showErrorNotification, showWarningNotification } = useNotification();
  const { user } = useAuth();
  const { trackEvent: trackAnalytics, trackError: trackAnalyticsError } = useAnalytics();
  const {
    subscription,
    loading: subscriptionLoading
  } = useSubscription();
  const {
    announceToScreenReader,
    state: accessibilityState
  } = useAccessibility();

  // ===========================
  // REFS
  // ===========================

  const containerRef = useRef(null);
  const refreshTimeoutRef = useRef(null);

  // ===========================
  // STATE MANAGEMENT
  // ===========================

  const [state, setState] = useState({
    loading: false,
    recommendations: null,
    selectedDay: null,
    selectedTimeSlot: null,
    error: null,
    retryCount: 0,
    lastFetchTime: null,
    userTimezone: defaultTimezone,
    expandedInsights: false,
    selectedPlatformFilter: 'all'
  });

  // ===========================
  // COMPUTED VALUES
  // ===========================

  // Get subscription features
  const subscriptionFeatures = useMemo(() => {
    if (subscriptionLoading || !subscription) {
      return SUBSCRIPTION_FEATURES.creator;
    }

    const plan = subscription.plan || userPlan || 'creator';
    return SUBSCRIPTION_FEATURES[plan] || SUBSCRIPTION_FEATURES.creator;
  }, [subscription, subscriptionLoading, userPlan]);

  // Get user timezone
  const userTimezone = useMemo(() => {
    if (subscriptionFeatures.enableCustomTimeZones) {
      return user?.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone || defaultTimezone;
    }
    return defaultTimezone;
  }, [subscriptionFeatures.enableCustomTimeZones, user?.timezone, defaultTimezone]);

  // Filter recommendations based on subscription
  const filteredRecommendations = useMemo(() => {
    if (!state.recommendations) return null;

    const maxRecommendations = subscriptionFeatures.maxRecommendations;
    const filtered = { ...state.recommendations };

    // Limit recommendations per day based on subscription
    Object.keys(filtered.combined_recommendations || {}).forEach(day => {
      if (filtered.combined_recommendations[day].length > maxRecommendations) {
        filtered.combined_recommendations[day] = filtered.combined_recommendations[day].slice(0, maxRecommendations);
      }
    });

    return filtered;
  }, [state.recommendations, subscriptionFeatures.maxRecommendations]);

  // Check if advanced features are available
  const canUseAdvancedFeatures = useMemo(() => {
    return enableAdvancedFeatures && subscriptionFeatures.enableAdvancedAnalytics;
  }, [enableAdvancedFeatures, subscriptionFeatures.enableAdvancedAnalytics]);

  // ===========================
  // EFFECTS
  // ===========================

  // Load recommendations when dialog opens
  useEffect(() => {
    if (open) {
      fetchRecommendations();
    }

    // Cleanup on close
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
        refreshTimeoutRef.current = null;
      }
    };
  }, [open, selectedPlatforms, fetchRecommendations]);

  // Update user timezone when it changes
  useEffect(() => {
    setState(prev => ({ ...prev, userTimezone }));
  }, [userTimezone]);

  // ===========================
  // EVENT HANDLERS
  // ===========================

  /**
   * Fetch recommendations from API with comprehensive error handling
   */
  const fetchRecommendations = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    const startTime = Date.now();

    try {
      // Build query parameters
      const params = new URLSearchParams();

      // Add platform filter if specified
      if (selectedPlatforms.length === 1) {
        params.append('platform', selectedPlatforms[0]);
      } else if (selectedPlatforms.length > 1) {
        params.append('platforms', selectedPlatforms.join(','));
      }

      // Add timezone parameter
      params.append('timezone', userTimezone);

      // Add subscription plan for server-side filtering
      params.append('plan', subscription?.plan || userPlan);

      // Add product performance data if available
      if (enableProductPerformance && selectedProducts.length > 0) {
        params.append('include_product_performance', 'true');
        params.append('product_ids', selectedProducts.map(p => p.id).join(','));
      }

      // Fetch recommendations
      const response = await api.get(`/api/posting-time/recommendations?${params.toString()}`);
      const recommendationsData = response.data;

      // Pre-select the best day and time slot
      const bestDay = recommendationsData.historical_analysis?.best_day;
      let selectedDay = null;
      let selectedTimeSlot = null;

      if (bestDay && recommendationsData.combined_recommendations?.[bestDay]?.length > 0) {
        selectedDay = bestDay;
        selectedTimeSlot = recommendationsData.combined_recommendations[bestDay][0];
      }

      setState(prev => ({
        ...prev,
        loading: false,
        recommendations: recommendationsData,
        selectedDay,
        selectedTimeSlot,
        lastFetchTime: Date.now(),
        retryCount: 0
      }));

      const fetchTime = Date.now() - startTime;

      if (enableAnalytics) {
        trackAnalytics('optimal_time_recommendations_fetched', {
          platforms: selectedPlatforms,
          timezone: userTimezone,
          fetchTime,
          dataPoints: recommendationsData.data_points || 0,
          bestDay,
          userPlan: subscription?.plan || userPlan,
          userId: user?.id
        });
      }

      if (accessibilityState.isScreenReaderActive) {
        announceToScreenReader(`Optimal posting time recommendations loaded for ${selectedPlatforms.length || 'all'} platforms`);
      }

    } catch (error) {
      const errorMessage = error.response?.data?.detail || error.message || 'Failed to load posting time recommendations';

      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
        retryCount: prev.retryCount + 1
      }));

      showErrorNotification(errorMessage);

      if (trackAnalyticsError) {
        trackAnalyticsError(error, {
          context: 'fetch_recommendations',
          platforms: selectedPlatforms,
          timezone: userTimezone,
          retryCount: state.retryCount + 1
        });
      }
    }
  }, [
    selectedPlatforms, userTimezone, subscription?.plan, userPlan, enableAnalytics,
    trackAnalytics, trackAnalyticsError, user?.id, accessibilityState.isScreenReaderActive,
    announceToScreenReader, showErrorNotification, state.retryCount
  ]);

  /**
   * Handle day selection with analytics and validation
   */
  const handleDaySelect = useCallback((day) => {
    const recommendations = filteredRecommendations || state.recommendations;

    if (!recommendations?.combined_recommendations?.[day]) {
      showWarningNotification(`No recommendations available for ${day}`);
      return;
    }

    // Auto-select the first time slot for this day
    const timeSlots = recommendations.combined_recommendations[day];
    const selectedTimeSlot = timeSlots.length > 0 ? timeSlots[0] : null;

    setState(prev => ({
      ...prev,
      selectedDay: day,
      selectedTimeSlot
    }));

    if (enableAnalytics) {
      trackAnalytics('optimal_time_day_selected', {
        selectedDay: day,
        availableTimeSlots: timeSlots.length,
        platforms: selectedPlatforms,
        userPlan: subscription?.plan || userPlan,
        userId: user?.id
      });
    }

    if (accessibilityState.isScreenReaderActive) {
      announceToScreenReader(`${day} selected. ${timeSlots.length} time slots available.`);
    }
  }, [
    filteredRecommendations, state.recommendations, enableAnalytics, trackAnalytics,
    selectedPlatforms, subscription?.plan, userPlan, user?.id,
    accessibilityState.isScreenReaderActive, announceToScreenReader, showWarningNotification
  ]);

  /**
   * Handle time slot selection with analytics
   */
  const handleTimeSlotSelect = useCallback((timeSlot) => {
    setState(prev => ({ ...prev, selectedTimeSlot: timeSlot }));

    if (enableAnalytics) {
      trackAnalytics('optimal_time_slot_selected', {
        timeSlot: timeSlot.time_slot,
        confidence: timeSlot.confidence,
        source: timeSlot.source,
        selectedDay: state.selectedDay,
        platforms: selectedPlatforms,
        userPlan: subscription?.plan || userPlan,
        userId: user?.id
      });
    }

    if (accessibilityState.isScreenReaderActive) {
      announceToScreenReader(`Time slot ${timeSlot.time_slot} selected with ${timeSlot.confidence} confidence`);
    }
  }, [
    enableAnalytics, trackAnalytics, state.selectedDay, selectedPlatforms,
    subscription?.plan, userPlan, user?.id, accessibilityState.isScreenReaderActive,
    announceToScreenReader
  ]);

  /**
   * Handle confirm button click with comprehensive validation and analytics
   */
  const handleConfirm = useCallback(() => {
    if (!state.selectedDay || !state.selectedTimeSlot) {
      showWarningNotification('Please select both a day and time slot');
      return;
    }

    try {
      // Extract hours from time slot (e.g., "08:00-09:59" -> 8)
      const timeSlotParts = state.selectedTimeSlot.time_slot.split(':');
      const startHour = parseInt(timeSlotParts[0], 10);
      const startMinute = timeSlotParts[1] ? parseInt(timeSlotParts[1].split('-')[0], 10) : 0;

      // Get current date
      const now = new Date();

      // Calculate target day (0 = Sunday, 6 = Saturday in JavaScript)
      const dayMap = {
        'Monday': 1,
        'Tuesday': 2,
        'Wednesday': 3,
        'Thursday': 4,
        'Friday': 5,
        'Saturday': 6,
        'Sunday': 0
      };

      const targetDay = dayMap[state.selectedDay];
      const currentDay = now.getDay();

      // Calculate days to add
      let daysToAdd = targetDay - currentDay;
      if (daysToAdd <= 0) {
        daysToAdd += 7; // Move to next week if target day is today or earlier
      }

      // Create target date with timezone consideration
      const targetDate = new Date(now);
      targetDate.setDate(now.getDate() + daysToAdd);
      targetDate.setHours(startHour, startMinute, 0, 0);

      // Validate the target date is in the future
      if (targetDate <= now) {
        showWarningNotification('Selected time must be in the future');
        return;
      }

      if (enableAnalytics) {
        trackAnalytics('optimal_time_confirmed', {
          selectedDay: state.selectedDay,
          selectedTimeSlot: state.selectedTimeSlot.time_slot,
          confidence: state.selectedTimeSlot.confidence,
          source: state.selectedTimeSlot.source,
          targetDate: targetDate.toISOString(),
          platforms: selectedPlatforms,
          timezone: userTimezone,
          userPlan: subscription?.plan || userPlan,
          userId: user?.id
        });
      }

      // Call the onSelectTime callback with the selected time
      if (onSelectTime) {
        onSelectTime(targetDate, {
          day: state.selectedDay,
          timeSlot: state.selectedTimeSlot,
          platforms: selectedPlatforms,
          timezone: userTimezone,
          confidence: state.selectedTimeSlot.confidence
        });
      }

      showSuccessNotification(`Scheduled for ${state.selectedDay} at ${state.selectedTimeSlot.time_slot}`);

      if (accessibilityState.isScreenReaderActive) {
        announceToScreenReader(`Content scheduled for ${state.selectedDay} at ${state.selectedTimeSlot.time_slot}`);
      }

      if (onClose) {
        onClose();
      }

    } catch (error) {
      const errorMessage = 'Failed to schedule content. Please try again.';
      showErrorNotification(errorMessage);

      if (trackAnalyticsError) {
        trackAnalyticsError(error, {
          context: 'confirm_scheduling',
          selectedDay: state.selectedDay,
          selectedTimeSlot: state.selectedTimeSlot?.time_slot
        });
      }
    }
  }, [
    state.selectedDay, state.selectedTimeSlot, selectedPlatforms, userTimezone,
    enableAnalytics, trackAnalytics, trackAnalyticsError, subscription?.plan,
    userPlan, user?.id, onSelectTime, onClose, showSuccessNotification,
    showWarningNotification, showErrorNotification, accessibilityState.isScreenReaderActive,
    announceToScreenReader
  ]);
  
  /**
   * Get confidence color based on level
   */
  const getConfidenceColor = useCallback((confidence) => {
    const level = CONFIDENCE_LEVELS[confidence?.toUpperCase()];
    return level ? level.color : theme.palette.grey[500];
  }, [theme.palette.grey]);

  /**
   * Handle retry action
   */
  const handleRetry = useCallback(() => {
    if (state.retryCount < COMPONENT_CONFIG.RETRY_ATTEMPTS) {
      fetchRecommendations();
    } else {
      showErrorNotification('Maximum retry attempts reached. Please refresh the page.');
    }
  }, [state.retryCount, fetchRecommendations, showErrorNotification]);

  // Expose ref methods
  useImperativeHandle(ref, () => ({
    focus: () => containerRef.current?.focus(),
    refresh: fetchRecommendations,
    getSelectedTime: () => ({
      day: state.selectedDay,
      timeSlot: state.selectedTimeSlot,
      platforms: selectedPlatforms,
      timezone: userTimezone
    }),
    getState: () => state,
    retry: handleRetry
  }), [state, selectedPlatforms, userTimezone, fetchRecommendations, handleRetry]);

  // ===========================
  // MAIN RENDER
  // ===========================

  return (
    <ErrorBoundary>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.1)}`
          }
        }}
        aria-labelledby="optimal-time-selector-title"
        aria-describedby="optimal-time-selector-description"
        data-testid={testId}
      >
        <DialogTitle
          id="optimal-time-selector-title"
          sx={{
            bgcolor: alpha(ACE_COLORS.PURPLE, 0.05),
            borderBottom: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.1)}`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <CalendarIcon sx={{ color: ACE_COLORS.PURPLE }} />
            <Typography variant="h6" sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
              Select Optimal Posting Time
            </Typography>

            {selectedPlatforms.length > 0 && (
              <Stack direction="row" spacing={1}>
                {selectedPlatforms.slice(0, 3).map(platform => (
                  <Chip
                    key={platform}
                    label={PLATFORM_CONFIGS[platform]?.name || platform}
                    size="small"
                    variant="outlined"
                    sx={{ borderColor: ACE_COLORS.PURPLE, color: ACE_COLORS.PURPLE }}
                  />
                ))}
                {selectedPlatforms.length > 3 && (
                  <Chip
                    label={`+${selectedPlatforms.length - 3} more`}
                    size="small"
                    variant="outlined"
                    sx={{ borderColor: ACE_COLORS.YELLOW, color: ACE_COLORS.DARK }}
                  />
                )}
              </Stack>
            )}
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Refresh recommendations">
              <IconButton
                onClick={fetchRecommendations}
                disabled={state.loading}
                sx={{ color: ACE_COLORS.PURPLE }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title="Close">
              <IconButton
                onClick={onClose}
                sx={{ color: ACE_COLORS.PURPLE }}
              >
                <CloseIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </DialogTitle>

        <DialogContent sx={{ p: 3 }}>
          {state.loading ? (
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 6 }}>
              <CircularProgress
                size={60}
                sx={{ color: ACE_COLORS.PURPLE, mb: 3 }}
              />
              <Typography variant="h6" sx={{ color: ACE_COLORS.DARK, mb: 1 }}>
                Analyzing optimal posting times...
              </Typography>
              <Typography variant="body2" color="textSecondary">
                This may take a few moments while we analyze your audience data
              </Typography>
              <LinearProgress
                sx={{
                  width: '100%',
                  mt: 3,
                  height: 6,
                  borderRadius: 3,
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: ACE_COLORS.PURPLE
                  }
                }}
              />
            </Box>
          ) : filteredRecommendations ? (
            <Box>
              {/* Information Alert */}
              <Alert
                severity="info"
                sx={{
                  mb: 3,
                  borderColor: ACE_COLORS.PURPLE,
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05)
                }}
                icon={<InfoIcon />}
              >
                <Typography variant="body2">
                  These recommendations are based on your audience&apos;s behavior patterns, ICP characteristics, and location data.
                  {canUseAdvancedFeatures && ' Advanced analytics are enabled for your plan.'}
                </Typography>
              </Alert>

              {/* Error Display */}
              {state.error && (
                <Fade in={true}>
                  <Alert
                    severity="error"
                    sx={{ mb: 3 }}
                    action={
                      state.retryCount < COMPONENT_CONFIG.RETRY_ATTEMPTS && (
                        <IconButton
                          color="inherit"
                          size="small"
                          onClick={handleRetry}
                          aria-label="Retry loading recommendations"
                        >
                          <RefreshIcon />
                        </IconButton>
                      )
                    }
                  >
                    <Typography variant="body2">
                      {state.error}
                    </Typography>
                  </Alert>
                </Fade>
              )}

              {/* Day Selection */}
              <Typography variant="h6" gutterBottom sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
                Select Day
              </Typography>

              <Grid container spacing={2} sx={{ mb: 4 }}>
                {Object.keys(filteredRecommendations.combined_recommendations || {}).map((day) => {
                  const timeSlots = filteredRecommendations.combined_recommendations[day] || [];
                  const isSelected = state.selectedDay === day;

                  return (
                    <Grid item key={day}>
                      <Button
                        variant={isSelected ? "contained" : "outlined"}
                        onClick={() => handleDaySelect(day)}
                        sx={{
                          minWidth: '120px',
                          height: '48px',
                          borderColor: isSelected ? ACE_COLORS.PURPLE : alpha(ACE_COLORS.PURPLE, 0.3),
                          backgroundColor: isSelected ? ACE_COLORS.PURPLE : 'transparent',
                          color: isSelected ? ACE_COLORS.WHITE : ACE_COLORS.PURPLE,
                          '&:hover': {
                            borderColor: ACE_COLORS.PURPLE,
                            backgroundColor: isSelected ? ACE_COLORS.PURPLE : alpha(ACE_COLORS.PURPLE, 0.05)
                          },
                          '&:focus': {
                            outline: `2px solid ${ACE_COLORS.PURPLE}`,
                            outlineOffset: 2
                          }
                        }}
                        startIcon={timeSlots.length > 0 ? <CheckCircleIcon /> : <WarningIcon />}
                        disabled={timeSlots.length === 0}
                      >
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {day}
                          </Typography>
                          <Typography variant="caption" sx={{ opacity: 0.8 }}>
                            {timeSlots.length} slots
                          </Typography>
                        </Box>
                      </Button>
                    </Grid>
                  );
                })}
              </Grid>

              {/* Time Slot Selection */}
              {state.selectedDay && (
                <Fade in={true} timeout={COMPONENT_CONFIG.ANIMATION_DURATION}>
                  <Box>
                    <Typography variant="h6" gutterBottom sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
                      Select Time Slot for {state.selectedDay}
                    </Typography>

                    {filteredRecommendations.combined_recommendations[state.selectedDay]?.length > 0 ? (
                      <Grid container spacing={3}>
                        {filteredRecommendations.combined_recommendations[state.selectedDay].map((timeSlot, index) => {
                          const isSelected = state.selectedTimeSlot === timeSlot;
                          // Note: confidenceLevel can be used for advanced features

                          return (
                            <Grid item xs={12} md={6} lg={4} key={index}>
                              <Card
                                sx={{
                                  cursor: 'pointer',
                                  border: `2px solid ${isSelected ? ACE_COLORS.PURPLE : alpha(ACE_COLORS.PURPLE, 0.1)}`,
                                  borderLeft: `6px solid ${getConfidenceColor(timeSlot.confidence)}`,
                                  backgroundColor: isSelected ? alpha(ACE_COLORS.PURPLE, 0.05) : 'transparent',
                                  transition: `all ${COMPONENT_CONFIG.ANIMATION_DURATION}ms ease`,
                                  '&:hover': {
                                    borderColor: ACE_COLORS.PURPLE,
                                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.02),
                                    transform: 'translateY(-2px)',
                                    boxShadow: `0 4px 12px ${alpha(ACE_COLORS.PURPLE, 0.15)}`
                                  },
                                  '&:focus-within': {
                                    outline: `2px solid ${ACE_COLORS.PURPLE}`,
                                    outlineOffset: 2
                                  }
                                }}
                                onClick={() => handleTimeSlotSelect(timeSlot)}
                                role="button"
                                tabIndex={0}
                                onKeyDown={(e) => {
                                  if (e.key === 'Enter' || e.key === ' ') {
                                    e.preventDefault();
                                    handleTimeSlotSelect(timeSlot);
                                  }
                                }}
                                aria-label={`Select time slot ${timeSlot.time_slot} with ${timeSlot.confidence} confidence`}
                              >
                                <CardContent>
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                      <AccessTimeIcon sx={{ color: ACE_COLORS.PURPLE }} />
                                      <Typography variant="h6" sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
                                        {timeSlot.time_slot}
                                      </Typography>
                                    </Box>

                                    <Chip
                                      label={timeSlot.confidence}
                                      size="small"
                                      sx={{
                                        bgcolor: getConfidenceColor(timeSlot.confidence),
                                        color: 'white',
                                        fontWeight: 600
                                      }}
                                    />
                                  </Box>

                                  <Typography variant="body2" color="textSecondary" sx={{ mb: 2, minHeight: 40 }}>
                                    {timeSlot.reason || `Based on ${timeSlot.source} data`}
                                  </Typography>

                                  {canUseAdvancedFeatures && timeSlot.engagement_score && (
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                                      <TrendingUpIcon sx={{ fontSize: 16, color: ACE_COLORS.PURPLE }} />
                                      <Typography variant="caption" color="textSecondary">
                                        Engagement Score: {(timeSlot.engagement_score * 100).toFixed(1)}%
                                      </Typography>
                                    </Box>
                                  )}

                                  {isSelected && (
                                    <Fade in={true}>
                                      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                                        <Chip
                                          icon={<CheckIcon />}
                                          label="Selected"
                                          sx={{
                                            bgcolor: ACE_COLORS.PURPLE,
                                            color: ACE_COLORS.WHITE,
                                            fontWeight: 600
                                          }}
                                        />
                                      </Box>
                                    </Fade>
                                  )}
                                </CardContent>
                              </Card>
                            </Grid>
                          );
                        })}
                      </Grid>
                    ) : (
                      <Alert
                        severity="warning"
                        sx={{
                          borderColor: ACE_COLORS.YELLOW,
                          backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1)
                        }}
                      >
                        <Typography variant="body2">
                          No recommended time slots available for {state.selectedDay}.
                          {!canUseAdvancedFeatures && ' Upgrade your plan for more recommendations.'}
                        </Typography>
                      </Alert>
                    )}
                  </Box>
                </Fade>
              )}
            </Box>
          ) : (
            <Box sx={{ textAlign: 'center', py: 6 }}>
              <ErrorIcon sx={{ fontSize: 60, color: theme.palette.error.main, mb: 2 }} />
              <Typography variant="h6" gutterBottom sx={{ color: ACE_COLORS.DARK }}>
                Failed to Load Recommendations
              </Typography>
              <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
                {state.error || 'Unable to load posting time recommendations. Please try again later.'}
              </Typography>
              <Button
                variant="outlined"
                onClick={handleRetry}
                startIcon={<RefreshIcon />}
                disabled={state.retryCount >= COMPONENT_CONFIG.RETRY_ATTEMPTS}
                sx={{
                  borderColor: ACE_COLORS.PURPLE,
                  color: ACE_COLORS.PURPLE,
                  '&:hover': {
                    borderColor: ACE_COLORS.PURPLE,
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05)
                  }
                }}
              >
                {state.retryCount >= COMPONENT_CONFIG.RETRY_ATTEMPTS ? 'Max Retries Reached' : 'Try Again'}
              </Button>
            </Box>
          )}
        </DialogContent>

        <DialogActions
          sx={{
            p: 3,
            borderTop: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.1)}`,
            bgcolor: alpha(ACE_COLORS.PURPLE, 0.02)
          }}
        >
          <Button
            onClick={onClose}
            sx={{
              color: ACE_COLORS.PURPLE,
              '&:hover': {
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05)
              }
            }}
          >
            Cancel
          </Button>

          <Button
            onClick={handleConfirm}
            variant="contained"
            disabled={!state.selectedDay || !state.selectedTimeSlot || state.loading}
            startIcon={<ScheduleIcon />}
            sx={{
              bgcolor: ACE_COLORS.PURPLE,
              color: ACE_COLORS.WHITE,
              fontWeight: 600,
              px: 3,
              '&:hover': {
                bgcolor: alpha(ACE_COLORS.PURPLE, 0.9)
              },
              '&:disabled': {
                bgcolor: alpha(ACE_COLORS.PURPLE, 0.3),
                color: alpha(ACE_COLORS.WHITE, 0.7)
              }
            }}
          >
            {state.selectedDay && state.selectedTimeSlot
              ? `Schedule for ${state.selectedDay} ${state.selectedTimeSlot.time_slot}`
              : 'Select Time to Schedule'
            }
          </Button>
        </DialogActions>
      </Dialog>
    </ErrorBoundary>
  );
}));

// ===========================
// PROPTYPES
// ===========================

OptimalTimeSelector.propTypes = {
  // Core props
  /** Whether the dialog is open */
  open: PropTypes.bool,
  /** Callback when dialog is closed */
  onClose: PropTypes.func,
  /** Callback when time is selected */
  onSelectTime: PropTypes.func,
  /** Array of selected platforms */
  selectedPlatforms: PropTypes.arrayOf(PropTypes.string),

  // Enhanced props
  /** User subscription plan */
  userPlan: PropTypes.oneOf(['creator', 'accelerator', 'dominator']),
  /** Enable analytics tracking */
  enableAnalytics: PropTypes.bool,
  /** Enable advanced features */
  enableAdvancedFeatures: PropTypes.bool,
  /** Default timezone */
  defaultTimezone: PropTypes.string,

  // E-commerce props
  /** Array of selected products for performance analysis */
  selectedProducts: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired
  })),
  /** Enable product performance integration */
  enableProductPerformance: PropTypes.bool,
  /** Product performance metrics */
  productMetrics: PropTypes.object,

  // Testing props
  /** Test identifier */
  testId: PropTypes.string,

  // Accessibility props
  /** Accessibility label */
  ariaLabel: PropTypes.string
};

// ===========================
// DEFAULT PROPS
// ===========================

OptimalTimeSelector.defaultProps = {
  open: false,
  selectedPlatforms: [],
  userPlan: 'creator',
  enableAnalytics: true,
  enableAdvancedFeatures: false,
  defaultTimezone: COMPONENT_CONFIG.DEFAULT_TIMEZONE,
  selectedProducts: [],
  enableProductPerformance: false,
  productMetrics: null,
  testId: 'optimal-time-selector'
};

// ===========================
// DISPLAY NAME
// ===========================

OptimalTimeSelector.displayName = 'OptimalTimeSelector';

export default OptimalTimeSelector;
