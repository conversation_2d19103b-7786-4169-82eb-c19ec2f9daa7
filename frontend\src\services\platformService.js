import React from 'react';
import {
  Facebook as FacebookIcon,
  Twitter as TwitterIcon,
  LinkedIn as LinkedInIcon,
  Instagram as InstagramIcon,
  YouTube as YouTubeIcon,
  MusicVideo as TikTokIcon,
  Pinterest as PinterestIcon,
  Reddit as RedditIcon,
  Public as DefaultIcon
} from '@mui/icons-material';

// Enhanced imports for new functionality
import { getDeviceFingerprint } from './fingerprint';
import { PrometheusMetricsCollector } from '../utils/PrometheusMetricsCollector';

/**
 * Enhanced Platform Service for handling social media platform-specific configurations
 * with Prometheus metrics, security enhancements, real-time updates, message deduplication,
 * and platform-specific fingerprinting capabilities.
 *
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 * @since 2024-01-01
 *
 * @example
 * ```javascript
 * import platformService from './platformService';
 *
 * // Basic platform configuration
 * const platform = platformService.getPlatform('linkedin');
 *
 * // Enhanced message deduplication
 * const fingerprint = await platformService.generateMessageFingerprint('linkedin', 'Hello world!');
 *
 * // Platform-specific device fingerprinting
 * const deviceFingerprint = await platformService.generatePlatformFingerprint('facebook');
 * ```
 */
class EnhancedPlatformService {
  constructor() {
    // Core platform configurations
    this.platforms = {
      facebook: {
        name: 'Facebook',
        icon: FacebookIcon,
        color: '#1877F2',
        maxCharacters: 63206,
        engagementTypes: ['likes', 'comments', 'shares'],
        engagementLabels: {
          likes: 'Likes',
          comments: 'Comments',
          shares: 'Shares'
        },
        features: {
          hasImages: true,
          hasVideos: true,
          hasPolls: true,
          hasStories: true,
          hasLiveStreaming: true
        }
      },
      twitter: {
        name: 'Twitter',
        icon: TwitterIcon,
        color: '#1DA1F2',
        maxCharacters: 280,
        engagementTypes: ['likes', 'retweets', 'replies'],
        engagementLabels: {
          likes: 'Likes',
          retweets: 'Retweets',
          replies: 'Replies',
          comments: 'Replies' // Alias for comments
        },
        features: {
          hasImages: true,
          hasVideos: true,
          hasPolls: true,
          hasSpaces: true,
          hasThreads: true
        }
      },
      linkedin: {
        name: 'LinkedIn',
        icon: LinkedInIcon,
        color: '#0A66C2',
        maxCharacters: 3000,
        engagementTypes: ['reactions', 'comments', 'shares'],
        engagementLabels: {
          reactions: 'Reactions',
          likes: 'Reactions', // Alias for reactions
          comments: 'Comments',
          shares: 'Shares'
        },
        features: {
          hasImages: true,
          hasVideos: true,
          hasDocuments: true,
          hasPolls: true,
          hasArticles: true
        }
      },
      instagram: {
        name: 'Instagram',
        icon: InstagramIcon,
        color: '#E4405F',
        maxCharacters: 2200,
        engagementTypes: ['likes', 'comments'],
        engagementLabels: {
          likes: 'Likes',
          comments: 'Comments'
        },
        features: {
          hasImages: true,
          hasVideos: true,
          hasStories: true,
          hasReels: true,
          hasIGTV: true
        }
      },
      youtube: {
        name: 'YouTube',
        icon: YouTubeIcon,
        color: '#FF0000',
        maxCharacters: 5000,
        engagementTypes: ['likes', 'dislikes', 'comments', 'views'],
        engagementLabels: {
          likes: 'Likes',
          dislikes: 'Dislikes',
          comments: 'Comments',
          views: 'Views'
        },
        features: {
          hasVideos: true,
          hasLiveStreaming: true,
          hasShorts: true,
          hasCommunityPosts: true
        }
      },
      tiktok: {
        name: 'TikTok',
        icon: TikTokIcon,
        color: '#000000',
        maxCharacters: 2200, // Updated to match backend specification
        engagementTypes: ['likes', 'comments', 'shares', 'views'],
        engagementLabels: {
          likes: 'Likes',
          comments: 'Comments',
          shares: 'Shares',
          views: 'Views'
        },
        features: {
          hasVideos: true,
          hasLiveStreaming: true,
          hasEffects: true,
          hasDuets: true
        }
      },
      threads: {
        name: 'Threads',
        icon: TikTokIcon, // Using TikTok icon as placeholder for Threads
        color: '#000000',
        maxCharacters: 500, // Added Threads platform
        engagementTypes: ['likes', 'comments', 'shares'],
        engagementLabels: {
          likes: 'Likes',
          comments: 'Comments',
          shares: 'Shares'
        },
        features: {
          hasImages: true,
          hasVideos: true,
          hasPolls: false,
          hasStories: false
        }
      },
      pinterest: {
        name: 'Pinterest',
        icon: PinterestIcon,
        color: '#BD081C',
        maxCharacters: 500,
        engagementTypes: ['saves', 'comments'],
        engagementLabels: {
          saves: 'Saves',
          likes: 'Saves', // Alias for saves
          comments: 'Comments'
        },
        features: {
          hasImages: true,
          hasVideos: true,
          hasBoards: true,
          hasIdeas: true
        }
      },
      reddit: {
        name: 'Reddit',
        icon: RedditIcon,
        color: '#FF4500',
        maxCharacters: 40000,
        engagementTypes: ['upvotes', 'downvotes', 'comments'],
        engagementLabels: {
          upvotes: 'Upvotes',
          downvotes: 'Downvotes',
          likes: 'Upvotes', // Alias for upvotes
          comments: 'Comments'
        },
        features: {
          hasImages: true,
          hasVideos: true,
          hasPolls: true,
          hasAwards: true
        }
      }
    };

    // Enhanced service initialization
    this._initializeEnhancedServices();
  }

  /**
   * Initialize enhanced services (Prometheus metrics, security, WebSocket, deduplication)
   *
   * @private
   */
  _initializeEnhancedServices() {
    try {
      // 1. Prometheus Metrics Integration
      this._initializeMetrics();

      // 2. Configuration Integrity Checking
      this._initializeSecurityFeatures();

      // 3. Real-time Updates - Dynamic Platform Configuration
      this._initializeRealTimeUpdates();

      // 4. Message Deduplication Service
      this._initializeMessageDeduplication();

      // 5. Platform-specific Fingerprinting
      this._initializePlatformFingerprinting();

      // Performance tracking
      this.performanceMetrics = {
        startTime: Date.now(),
        operationCounts: new Map(),
        cacheHitRates: new Map(),
        errorCounts: new Map()
      };

      console.log('[PlatformService] Enhanced services initialized successfully');
    } catch (error) {
      console.error('[PlatformService] Error initializing enhanced services:', error);
      // Fallback to basic functionality
      this._initializeFallbackMode();
    }
  }

  /**
   * Initialize Prometheus metrics integration
   *
   * @private
   */
  _initializeMetrics() {
    try {
      // Initialize Prometheus metrics collector
      this.metricsCollector = new PrometheusMetricsCollector({
        enabled: true,
        endpoint: '/api/metrics/platform-service',
        batchSize: 50,
        flushInterval: 30000
      });

      // Platform-specific metrics
      this.metrics = {
        platformRequests: new Map(),
        engagementFormatting: new Map(),
        featureChecks: new Map(),
        configurationAccess: new Map(),
        errors: new Map(),
        responseTime: new Map(),
        cacheHitRate: new Map(),
        validationFailures: new Map()
      };

      // Initialize metric tracking
      this._setupMetricTracking();

      console.log('[PlatformService] Prometheus metrics initialized');
    } catch (error) {
      console.warn('[PlatformService] Metrics initialization failed:', error);
      this.metricsCollector = null;
    }
  }

  /**
   * Initialize security features and configuration integrity checking
   *
   * @private
   */
  _initializeSecurityFeatures() {
    try {
      // Generate configuration hash for integrity checking
      this.configurationHash = this._generateConfigurationHash();

      // Security settings
      this.securityConfig = {
        integrityCheckEnabled: true,
        configurationValidation: true,
        fingerprintingEnabled: true,
        lastIntegrityCheck: Date.now()
      };

      // Validate configuration integrity
      this._validateConfigurationIntegrity();

      // Schedule periodic integrity checks
      this._scheduleIntegrityChecks();

      console.log('[PlatformService] Security features initialized');
    } catch (error) {
      console.warn('[PlatformService] Security initialization failed:', error);
      this.securityConfig = { integrityCheckEnabled: false };
    }
  }

  /**
   * Initialize real-time updates and dynamic configuration
   *
   * @private
   */
  _initializeRealTimeUpdates() {
    try {
      // WebSocket connection for real-time configuration updates
      this.realTimeConfig = {
        enabled: true,
        websocket: null,
        reconnectAttempts: 0,
        maxReconnectAttempts: 5,
        reconnectInterval: 5000,
        configurationVersion: '1.0.0',
        lastUpdate: Date.now()
      };

      // Configuration versioning
      this.configurationVersions = new Map();
      this.configurationVersions.set('1.0.0', {
        platforms: { ...this.platforms },
        timestamp: Date.now(),
        checksum: this.configurationHash
      });

      // Initialize WebSocket connection
      this._initializeWebSocketConnection();

      console.log('[PlatformService] Real-time updates initialized');
    } catch (error) {
      console.warn('[PlatformService] Real-time updates initialization failed:', error);
      this.realTimeConfig = { enabled: false };
    }
  }

  /**
   * Initialize message deduplication service
   *
   * @private
   */
  _initializeMessageDeduplication() {
    try {
      // Message deduplication configuration
      this.deduplicationConfig = {
        enabled: true,
        windowMs: 30000, // 30 seconds default
        similarityThreshold: 0.95,
        platformSpecificRules: {
          linkedin: { windowMs: 45000, strictMode: true },
          twitter: { windowMs: 15000, strictMode: false },
          facebook: { windowMs: 30000, strictMode: true },
          instagram: { windowMs: 30000, strictMode: true }
        }
      };

      // Message fingerprint cache
      this.messageCache = new Map();
      this.messageCacheStats = {
        hits: 0,
        misses: 0,
        duplicatesDetected: 0,
        cacheSize: 0
      };

      // Initialize cleanup timer for message cache
      this._initializeMessageCacheCleanup();

      console.log('[PlatformService] Message deduplication initialized');
    } catch (error) {
      console.warn('[PlatformService] Message deduplication initialization failed:', error);
      this.deduplicationConfig = { enabled: false };
    }
  }

  /**
   * Initialize platform-specific fingerprinting
   *
   * @private
   */
  _initializePlatformFingerprinting() {
    try {
      // Platform fingerprinting configuration
      this.fingerprintingConfig = {
        enabled: true,
        cacheEnabled: true,
        cacheTTL: 3600000, // 1 hour
        enhancedSecurity: true
      };

      // Fingerprint cache
      this.fingerprintCache = new Map();
      this.fingerprintStats = {
        generated: 0,
        cached: 0,
        securityChecks: 0,
        validationFailures: 0
      };

      console.log('[PlatformService] Platform fingerprinting initialized');
    } catch (error) {
      console.warn('[PlatformService] Platform fingerprinting initialization failed:', error);
      this.fingerprintingConfig = { enabled: false };
    }
  }

  /**
   * Get platform configuration with enhanced metrics tracking
   *
   * @param {string} platformName - The platform name
   * @returns {Object} Platform configuration object
   */
  getPlatform(platformName) {
    const startTime = Date.now();

    try {
      // Track platform request
      this._recordMetric('platformRequests', platformName);

      const normalizedName = this.normalizePlatformName(platformName);
      const platform = this.platforms[normalizedName] || this.getDefaultPlatform(platformName);

      // Record response time
      const responseTime = Date.now() - startTime;
      this._recordResponseTime('getPlatform', responseTime);

      // Validate configuration integrity if enabled
      if (this.securityConfig?.integrityCheckEnabled) {
        this._validatePlatformConfiguration(platform, platformName);
      }

      return platform;
    } catch (error) {
      this._recordError('getPlatform', error, { platformName });
      throw error;
    }
  }

  /**
   * Get default platform configuration for unknown platforms
   */
  getDefaultPlatform(platformName) {
    return {
      name: platformName || 'Unknown Platform',
      icon: DefaultIcon,
      color: '#666666',
      maxCharacters: 1000,
      engagementTypes: ['likes', 'comments', 'shares'],
      engagementLabels: {
        likes: 'Likes',
        comments: 'Comments',
        shares: 'Shares'
      },
      features: {
        hasImages: true,
        hasVideos: false,
        hasPolls: false
      }
    };
  }

  /**
   * Normalize platform name to lowercase and handle aliases
   */
  normalizePlatformName(platformName) {
    if (!platformName || typeof platformName !== 'string') {
      return 'unknown';
    }

    const normalized = platformName.toLowerCase().trim();
    
    // Handle common aliases
    const aliases = {
      'fb': 'facebook',
      'ig': 'instagram',
      'yt': 'youtube',
      'li': 'linkedin',
      'tw': 'twitter',
      'tt': 'tiktok',
      'pin': 'pinterest'
    };

    return aliases[normalized] || normalized;
  }

  /**
   * Get platform icon component with enhanced error handling
   */
  getPlatformIcon(platformName, props = {}) {
    try {
      const platform = this.getPlatform(platformName);
      const IconComponent = platform.icon;

      if (!IconComponent) {
        console.warn(`No icon component found for platform: ${platformName}`);
        return React.createElement(DefaultIcon, {
          ...props,
          sx: {
            color: '#666666',
            ...props.sx
          }
        });
      }

      return React.createElement(IconComponent, {
        ...props,
        sx: {
          color: platform.color,
          ...props.sx
        }
      });
    } catch (error) {
      console.error(`Error creating platform icon for ${platformName}:`, error);
      return React.createElement(DefaultIcon, {
        ...props,
        sx: {
          color: '#666666',
          ...props.sx
        }
      });
    }
  }

  /**
   * Get platform color
   */
  getPlatformColor(platformName) {
    const platform = this.getPlatform(platformName);
    return platform.color;
  }

  /**
   * Format engagement numbers with platform-specific logic, enhanced error handling, and metrics tracking
   *
   * @param {string} platformName - The platform name
   * @param {string} engagementType - The engagement type (likes, comments, etc.)
   * @param {number|string} value - The engagement value to format
   * @returns {string} Formatted engagement string
   */
  formatEngagement(platformName, engagementType, value) {
    const startTime = Date.now();

    try {
      // Track engagement formatting request
      const metricKey = `${platformName}_${engagementType}`;
      this._recordMetric('engagementFormatting', metricKey);

      const platform = this.getPlatform(platformName);
      const normalizedType = this.normalizeEngagementType(platform, engagementType);

      // Enhanced input validation
      if (value === null || value === undefined || value === '' || isNaN(value)) {
        this._recordMetric('validationFailures', 'invalid_input');
        return '0';
      }

      const num = parseInt(value);

      // Handle negative numbers
      if (num < 0 && platform.name !== 'Reddit') {
        console.warn(`Negative engagement value ${num} for platform ${platformName}`);
        this._recordMetric('validationFailures', 'negative_value');
        return '0';
      }

    // Platform-specific formatting rules based on normalized type
    const formatRules = this.getEngagementFormatRules(platform, normalizedType);

    // Apply platform-specific thresholds and formatting
    if (num >= formatRules.millionThreshold) {
      const formatted = (num / 1000000).toFixed(formatRules.millionDecimals);
      return `${formatted}${formatRules.millionSuffix}`;
    } else if (num >= formatRules.thousandThreshold) {
      const formatted = (num / 1000).toFixed(formatRules.thousandDecimals);
      return `${formatted}${formatRules.thousandSuffix}`;
    } else if (num >= formatRules.hundredThreshold && formatRules.showHundreds) {
      const formatted = (num / 100).toFixed(formatRules.hundredDecimals);
      return `${formatted}${formatRules.hundredSuffix}`;
    }

    // Apply special formatting for specific engagement types
    if (normalizedType === 'views' && platform.name === 'YouTube') {
      // YouTube views often show more precision
      if (num >= 1000000) {
        return `${(num / 1000000).toFixed(2)}M views`;
      } else if (num >= 1000) {
        return `${(num / 1000).toFixed(1)}K views`;
      }
      return `${num} views`;
    }

    if (normalizedType === 'upvotes' && platform.name === 'Reddit') {
      // Reddit upvotes can be negative
      const sign = num >= 0 ? '' : '-';
      const absNum = Math.abs(num);
      if (absNum >= 1000) {
        return `${sign}${(absNum / 1000).toFixed(1)}K`;
      }
      return `${sign}${absNum}`;
    }

    if (normalizedType === 'saves' && platform.name === 'Pinterest') {
      // Pinterest saves often use different thresholds
      if (num >= 100000) {
        return `${(num / 1000).toFixed(0)}K saves`;
      } else if (num >= 1000) {
        return `${(num / 1000).toFixed(1)}K saves`;
      }
      return `${num} saves`;
    }

    if (normalizedType === 'reactions' && platform.name === 'LinkedIn') {
      // LinkedIn reactions are more conservative in formatting
      if (num >= 10000) {
        return `${(num / 1000).toFixed(0)}K`;
      } else if (num >= 1000) {
        return `${(num / 1000).toFixed(1)}K`;
      }
      return num.toString();
    }

    if (normalizedType === 'retweets' && platform.name === 'Twitter') {
      // Twitter retweets formatting
      if (num >= 1000000) {
        return `${(num / 1000000).toFixed(1)}M RT`;
      } else if (num >= 1000) {
        return `${(num / 1000).toFixed(1)}K RT`;
      }
      return `${num} RT`;
    }

    // Default formatting with platform suffix if applicable
    const suffix = formatRules.defaultSuffix || '';
    const result = `${num}${suffix}`;

    // Record successful formatting time
    const responseTime = Date.now() - startTime;
    this._recordResponseTime('formatEngagement', responseTime);

    return result;

    } catch (error) {
      console.error(`Error formatting engagement for platform ${platformName}, type ${engagementType}, value ${value}:`, error);

      // Record error metrics
      this._recordError('formatEngagement', error, {
        platformName,
        engagementType,
        value,
        responseTime: Date.now() - startTime
      });

      // Fallback to simple formatting
      const num = parseInt(value) || 0;
      if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
      if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
      return num.toString();
    }
  }

  /**
   * Get platform-specific formatting rules for engagement types
   */
  getEngagementFormatRules(platform, normalizedType) {
    // Default formatting rules
    const defaultRules = {
      millionThreshold: 1000000,
      millionDecimals: 1,
      millionSuffix: 'M',
      thousandThreshold: 1000,
      thousandDecimals: 1,
      thousandSuffix: 'K',
      hundredThreshold: 100,
      hundredDecimals: 1,
      hundredSuffix: '',
      showHundreds: false,
      defaultSuffix: ''
    };

    // Platform-specific overrides
    const platformRules = {
      'YouTube': {
        millionDecimals: 2,
        thousandDecimals: 1,
        showHundreds: false
      },
      'TikTok': {
        millionDecimals: 1,
        thousandDecimals: 0,
        showHundreds: true,
        hundredSuffix: ''
      },
      'Instagram': {
        millionDecimals: 1,
        thousandDecimals: 1,
        showHundreds: false
      },
      'Twitter': {
        millionDecimals: 1,
        thousandDecimals: 1,
        showHundreds: false
      },
      'LinkedIn': {
        millionThreshold: 10000000, // More conservative
        thousandThreshold: 10000,   // Higher threshold
        thousandDecimals: 0
      },
      'Pinterest': {
        millionDecimals: 1,
        thousandDecimals: 1,
        showHundreds: false
      },
      'Reddit': {
        millionDecimals: 1,
        thousandDecimals: 1,
        showHundreds: false
      }
    };

    // Engagement type specific overrides
    const typeRules = {
      'views': {
        millionDecimals: 2,
        thousandDecimals: 1
      },
      'subscribers': {
        millionDecimals: 2,
        thousandDecimals: 1
      },
      'followers': {
        millionDecimals: 1,
        thousandDecimals: 1
      }
    };

    // Merge rules in order of precedence
    return {
      ...defaultRules,
      ...(platformRules[platform.name] || {}),
      ...(typeRules[normalizedType] || {})
    };
  }

  /**
   * Normalize engagement type based on platform
   */
  normalizeEngagementType(platform, engagementType) {
    const type = engagementType?.toLowerCase();
    
    // Handle platform-specific aliases
    if (platform.name === 'Twitter' && type === 'shares') {
      return 'retweets';
    }
    if (platform.name === 'LinkedIn' && type === 'likes') {
      return 'reactions';
    }
    if (platform.name === 'Pinterest' && type === 'likes') {
      return 'saves';
    }
    if (platform.name === 'Reddit' && type === 'likes') {
      return 'upvotes';
    }
    
    return type;
  }

  /**
   * Get engagement label for display
   */
  getEngagementLabel(platformName, engagementType) {
    const platform = this.getPlatform(platformName);
    const normalizedType = this.normalizeEngagementType(platform, engagementType);
    
    return platform.engagementLabels[normalizedType] || 
           platform.engagementLabels[engagementType] || 
           engagementType;
  }

  /**
   * Get all supported engagement types for a platform
   */
  getEngagementTypes(platformName) {
    const platform = this.getPlatform(platformName);
    return platform.engagementTypes;
  }

  /**
   * Check if platform supports a specific feature with enhanced metrics tracking
   *
   * @param {string} platformName - The platform name
   * @param {string} feature - The feature to check
   * @returns {boolean} Whether the platform supports the feature
   */
  supportsFeature(platformName, feature) {
    const startTime = Date.now();

    try {
      // Track feature check request
      const metricKey = `${platformName}_${feature}`;
      this._recordMetric('featureChecks', metricKey);

      const platform = this.getPlatform(platformName);
      const supported = platform.features[feature] || false;

      // Record response time
      const responseTime = Date.now() - startTime;
      this._recordResponseTime('supportsFeature', responseTime);

      return supported;
    } catch (error) {
      this._recordError('supportsFeature', error, { platformName, feature });
      return false;
    }
  }

  /**
   * Get character limit for platform
   */
  getCharacterLimit(platformName) {
    const platform = this.getPlatform(platformName);
    return platform.maxCharacters;
  }

  /**
   * Validate content length for platform
   */
  validateContentLength(platformName, content) {
    if (!content || typeof content !== 'string') {
      return { valid: true, remaining: this.getCharacterLimit(platformName) };
    }

    const limit = this.getCharacterLimit(platformName);
    const length = content.length;
    
    return {
      valid: length <= limit,
      remaining: limit - length,
      length,
      limit
    };
  }

  /**
   * Get all supported platforms
   */
  getAllPlatforms() {
    return Object.keys(this.platforms).map(key => ({
      key,
      ...this.platforms[key]
    }));
  }

  /**
   * Check if platform is supported
   */
  isSupported(platformName) {
    const normalized = this.normalizePlatformName(platformName);
    return normalized in this.platforms;
  }

  /**
   * Get platform-specific response tone suggestions
   */
  getResponseToneSuggestions(platformName) {
    const platform = this.getPlatform(platformName);
    
    const baseTones = ['professional', 'friendly', 'casual', 'helpful'];
    
    // Platform-specific tone suggestions
    switch (platform.name.toLowerCase()) {
      case 'linkedin':
        return ['professional', 'thought-leadership', 'industry-expert', 'networking'];
      case 'twitter':
        return ['conversational', 'witty', 'concise', 'trending'];
      case 'instagram':
        return ['visual-focused', 'lifestyle', 'inspirational', 'community-driven'];
      case 'facebook':
        return ['community-focused', 'family-friendly', 'engaging', 'informative'];
      case 'tiktok':
        return ['trendy', 'fun', 'creative', 'youthful'];
      case 'youtube':
        return ['educational', 'entertaining', 'detailed', 'subscriber-focused'];
      default:
        return baseTones;
    }
  }

  /**
   * Get platform-specific content guidelines
   */
  getContentGuidelines(platformName) {
    const platform = this.getPlatform(platformName);

    const baseGuidelines = [
      'Be respectful and professional',
      'Avoid spam or promotional content',
      'Respect community guidelines'
    ];

    // Platform-specific guidelines
    switch (platform.name.toLowerCase()) {
      case 'linkedin':
        return [
          ...baseGuidelines,
          'Focus on professional value',
          'Share industry insights',
          'Maintain business-appropriate tone'
        ];
      case 'twitter':
        return [
          ...baseGuidelines,
          'Keep responses concise',
          'Use relevant hashtags sparingly',
          'Engage in real-time conversations'
        ];
      case 'instagram':
        return [
          ...baseGuidelines,
          'Use high-quality visuals',
          'Include relevant hashtags',
          'Encourage community interaction'
        ];
      default:
        return baseGuidelines;
    }
  }

  /**
   * Get hashtag recommendations for platform (matching backend specifications)
   */
  getHashtagRecommendations(platformName) {
    // Track hashtag recommendation request
    this._recordMetric('hashtagRecommendations', platformName);

    const recommendations = {
      twitter: { max_hashtags: 5, description: "Twitter works best with 1-2 highly relevant hashtags. Using too many can reduce engagement." },
      linkedin: { max_hashtags: 5, description: "LinkedIn works best with 3-5 relevant, professional hashtags. Keep them industry-specific." },
      facebook: { max_hashtags: 3, description: "Facebook posts perform better with minimal hashtags (1-3 max). Too many can appear spammy." },
      instagram: { max_hashtags: 30, description: "Instagram allows up to 30 hashtags, but 5-15 relevant hashtags typically work best." },
      pinterest: { max_hashtags: 20, description: "Pinterest works well with descriptive, keyword-rich hashtags. Use 5-10 relevant hashtags." },
      threads: { max_hashtags: 10, description: "Threads works best with a moderate number of hashtags. Use 3-5 relevant hashtags for best results." },
      tiktok: { max_hashtags: 10, description: "TikTok works best with a mix of trending and niche hashtags. Use 3-5 relevant hashtags." }
    };

    const normalizedName = this.normalizePlatformName(platformName);
    return recommendations[normalizedName] || recommendations.linkedin;
  }

  /**
   * Get image recommendations for platform (matching backend specifications)
   */
  getImageRecommendations(platformName) {
    const recommendations = {
      twitter: {
        optimal_dimensions: "1200 x 675 pixels",
        aspect_ratio: "16:9",
        max_file_size: "5MB",
        formats: ["JPG", "PNG", "GIF", "WEBP"],
        supports_carousel: false,
        supports_video: true
      },
      linkedin: {
        optimal_dimensions: "1200 x 627 pixels",
        aspect_ratio: "1.91:1",
        max_file_size: "5MB",
        formats: ["JPG", "PNG", "GIF"],
        supports_carousel: true,
        supports_pdf: true,
        supports_video: true
      },
      facebook: {
        optimal_dimensions: "1200 x 630 pixels",
        aspect_ratio: "1.91:1",
        max_file_size: "8MB",
        formats: ["JPG", "PNG", "GIF"],
        supports_carousel: true,
        supports_video: true
      },
      instagram: {
        optimal_dimensions: "1080 x 1080 pixels (square), 1080 x 1350 (portrait)",
        aspect_ratio: "1:1 (square), 4:5 (portrait)",
        max_file_size: "8MB",
        formats: ["JPG", "PNG"],
        supports_carousel: true,
        supports_video: true
      },
      pinterest: {
        optimal_dimensions: "1000 x 1500 pixels",
        aspect_ratio: "2:3 (vertical)",
        max_file_size: "10MB",
        formats: ["JPG", "PNG"],
        supports_carousel: true,
        supports_video: true
      },
      threads: {
        optimal_dimensions: "1080 x 1080 pixels (square), 1080 x 1350 (portrait)",
        aspect_ratio: "1:1 (square), 4:5 (portrait)",
        max_file_size: "8MB",
        formats: ["JPG", "PNG"],
        supports_carousel: true,
        supports_video: true
      },
      tiktok: {
        optimal_dimensions: "1080 x 1920 pixels",
        aspect_ratio: "9:16 (vertical)",
        max_file_size: "50MB",
        formats: ["JPG", "PNG", "MP4"],
        supports_carousel: false,
        supports_video: true
      }
    };

    const normalizedName = this.normalizePlatformName(platformName);
    return recommendations[normalizedName] || recommendations.linkedin;
  }

  // ===================================================================
  // ENHANCED METHODS - NEW FUNCTIONALITY
  // ===================================================================

  /**
   * Generate platform-aware message fingerprint for deduplication
   *
   * @param {string} platformName - The platform name
   * @param {string} messageContent - The message content
   * @param {Object} options - Additional options
   * @returns {Promise<string>} Message fingerprint
   */
  async generateMessageFingerprint(platformName, messageContent, options = {}) {
    const startTime = Date.now();

    try {
      // Track fingerprint generation
      this._recordMetric('messageFingerprints', platformName);

      const platform = this.getPlatform(platformName);

      // Normalize content based on platform characteristics
      const normalizedContent = this._normalizeContentForPlatform(platformName, messageContent);

      // Create platform-specific fingerprint components
      const fingerprintComponents = [
        platform.name,
        normalizedContent,
        platform.maxCharacters.toString(),
        options.conversationId || '',
        options.userId || ''
      ];

      // Generate secure hash
      const fingerprint = await this._generateSecureHash(fingerprintComponents.join('|'));

      // Cache fingerprint if enabled
      if (this.deduplicationConfig?.enabled) {
        this._cacheMessageFingerprint(fingerprint, {
          platform: platformName,
          content: messageContent,
          normalizedContent,
          timestamp: Date.now(),
          options
        });
      }

      // Record response time
      const responseTime = Date.now() - startTime;
      this._recordResponseTime('generateMessageFingerprint', responseTime);

      return fingerprint;
    } catch (error) {
      this._recordError('generateMessageFingerprint', error, { platformName, contentLength: messageContent?.length });
      throw error;
    }
  }

  /**
   * Detect duplicate messages across platforms
   *
   * @param {Array} messages - Array of message objects
   * @param {Object} options - Detection options
   * @returns {Promise<Object>} Duplicate detection results
   */
  async detectDuplicateMessages(messages, options = {}) {
    const startTime = Date.now();

    try {
      // Track duplicate detection request
      this._recordMetric('duplicateDetection', 'batch_check');

      const duplicates = [];
      const fingerprints = new Map();
      const similarityThreshold = options.similarityThreshold || this.deduplicationConfig?.similarityThreshold || 0.95;

      for (const message of messages) {
        const fingerprint = await this.generateMessageFingerprint(
          message.platform,
          message.content,
          { conversationId: message.conversationId, userId: message.userId }
        );

        // Check for exact duplicates
        if (fingerprints.has(fingerprint)) {
          duplicates.push({
            type: 'exact',
            original: fingerprints.get(fingerprint),
            duplicate: message,
            similarity: 1.0,
            fingerprint
          });
        } else {
          // Check for similar messages
          for (const [existingFingerprint, existingMessage] of fingerprints.entries()) {
            if (existingMessage.platform === message.platform) {
              const similarity = this._calculateContentSimilarity(
                existingMessage.content,
                message.content
              );

              if (similarity >= similarityThreshold) {
                duplicates.push({
                  type: 'similar',
                  original: existingMessage,
                  duplicate: message,
                  similarity,
                  fingerprint: existingFingerprint
                });
                break;
              }
            }
          }

          fingerprints.set(fingerprint, message);
        }
      }

      // Record response time and results
      const responseTime = Date.now() - startTime;
      this._recordResponseTime('detectDuplicateMessages', responseTime);
      this._recordMetric('duplicatesDetected', duplicates.length.toString());

      return {
        duplicates,
        totalMessages: messages.length,
        duplicateCount: duplicates.length,
        uniqueMessages: messages.length - duplicates.length,
        processingTime: responseTime
      };
    } catch (error) {
      this._recordError('detectDuplicateMessages', error, { messageCount: messages?.length });
      throw error;
    }
  }

  /**
   * Generate platform-specific device fingerprint for enhanced security
   *
   * @param {string} platformName - The platform name
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Platform-specific device fingerprint
   */
  async generatePlatformFingerprint(platformName, options = {}) {
    const startTime = Date.now();

    try {
      // Track platform fingerprint generation
      this._recordMetric('platformFingerprints', platformName);

      const platform = this.getPlatform(platformName);

      // Get base device fingerprint
      const baseFingerprint = await getDeviceFingerprint();

      // Add platform-specific characteristics
      const platformFingerprint = {
        ...baseFingerprint,
        platform: {
          name: platform.name,
          color: platform.color,
          maxCharacters: platform.maxCharacters,
          supportedFeatures: Object.keys(platform.features).filter(
            feature => platform.features[feature]
          ),
          engagementTypes: platform.engagementTypes,
          capabilities: this._getPlatformCapabilities(platformName)
        },
        security: {
          timestamp: Date.now(),
          version: this.realTimeConfig?.configurationVersion || '1.0.0',
          integrityHash: this.configurationHash,
          sessionId: options.sessionId || this._generateSessionId()
        },
        enhanced: options.enhanced || false
      };

      // Cache fingerprint if enabled
      if (this.fingerprintingConfig?.cacheEnabled) {
        this._cachePlatformFingerprint(platformName, platformFingerprint);
      }

      // Record response time
      const responseTime = Date.now() - startTime;
      this._recordResponseTime('generatePlatformFingerprint', responseTime);
      this.fingerprintStats.generated++;

      return platformFingerprint;
    } catch (error) {
      this._recordError('generatePlatformFingerprint', error, { platformName });
      this.fingerprintStats.validationFailures++;
      throw error;
    }
  }

  // ===================================================================
  // PRIVATE HELPER METHODS
  // ===================================================================

  /**
   * Record metric for tracking
   *
   * @private
   * @param {string} metricType - Type of metric
   * @param {string} key - Metric key
   */
  _recordMetric(metricType, key) {
    try {
      if (!this.metrics) return;

      if (!this.metrics[metricType]) {
        this.metrics[metricType] = new Map();
      }

      const current = this.metrics[metricType].get(key) || 0;
      this.metrics[metricType].set(key, current + 1);

      // Send to Prometheus if available
      if (this.metricsCollector) {
        this.metricsCollector.recordCustomMetric(`platform_${metricType}`, 1, {
          platform: key.split('_')[0],
          operation: key
        });
      }
    } catch (error) {
      console.warn('[PlatformService] Error recording metric:', error);
    }
  }

  /**
   * Record response time metric
   *
   * @private
   * @param {string} operation - Operation name
   * @param {number} responseTime - Response time in milliseconds
   */
  _recordResponseTime(operation, responseTime) {
    try {
      if (!this.metrics?.responseTime) return;

      const times = this.metrics.responseTime.get(operation) || [];
      times.push(responseTime);

      // Keep only last 100 measurements
      if (times.length > 100) {
        times.shift();
      }

      this.metrics.responseTime.set(operation, times);

      // Send to Prometheus if available
      if (this.metricsCollector) {
        this.metricsCollector.recordResponseTime(operation, responseTime);
      }
    } catch (error) {
      console.warn('[PlatformService] Error recording response time:', error);
    }
  }

  /**
   * Record error metric
   *
   * @private
   * @param {string} operation - Operation name
   * @param {Error} error - Error object
   * @param {Object} context - Additional context
   */
  _recordError(operation, error, context = {}) {
    try {
      if (!this.metrics?.errors) return;

      const errorKey = `${operation}_${error.name || 'UnknownError'}`;
      const current = this.metrics.errors.get(errorKey) || 0;
      this.metrics.errors.set(errorKey, current + 1);

      // Send to Prometheus if available
      if (this.metricsCollector) {
        this.metricsCollector.recordError(operation, error.message, context);
      }

      console.error(`[PlatformService] ${operation} error:`, error, context);
    } catch (recordError) {
      console.warn('[PlatformService] Error recording error metric:', recordError);
    }
  }

  /**
   * Generate configuration hash for integrity checking
   *
   * @private
   * @returns {string} Configuration hash
   */
  _generateConfigurationHash() {
    try {
      const configString = JSON.stringify(this.platforms, Object.keys(this.platforms).sort());
      return this._simpleHash(configString);
    } catch (error) {
      console.warn('[PlatformService] Error generating configuration hash:', error);
      return 'fallback_hash';
    }
  }

  /**
   * Validate configuration integrity
   *
   * @private
   */
  _validateConfigurationIntegrity() {
    try {
      if (!this.securityConfig?.integrityCheckEnabled) return;

      const currentHash = this._generateConfigurationHash();
      if (currentHash !== this.configurationHash) {
        console.error('[PlatformService] Configuration integrity check failed!');
        this._recordError('configurationIntegrity', new Error('Hash mismatch'), {
          expected: this.configurationHash,
          actual: currentHash
        });

        // Implement security response
        this._handleIntegrityFailure();
      }

      this.securityConfig.lastIntegrityCheck = Date.now();
    } catch (error) {
      console.error('[PlatformService] Error validating configuration integrity:', error);
    }
  }

  /**
   * Validate platform configuration
   *
   * @private
   * @param {Object} platform - Platform configuration
   * @param {string} platformName - Platform name
   */
  _validatePlatformConfiguration(platform, platformName) {
    try {
      if (!platform || !platform.name) {
        throw new Error(`Invalid platform configuration for ${platformName}`);
      }

      // Additional validation logic can be added here
      return true;
    } catch (error) {
      this._recordError('platformValidation', error, { platformName });
      throw error;
    }
  }

  /**
   * Setup metric tracking
   *
   * @private
   */
  _setupMetricTracking() {
    try {
      // Initialize metric maps
      Object.keys(this.metrics).forEach(metricType => {
        if (!this.metrics[metricType]) {
          this.metrics[metricType] = new Map();
        }
      });

      // Setup periodic metric reporting
      if (this.metricsCollector) {
        setInterval(() => {
          this._reportMetrics();
        }, 60000); // Report every minute
      }
    } catch (error) {
      console.warn('[PlatformService] Error setting up metric tracking:', error);
    }
  }

  /**
   * Report metrics to Prometheus
   *
   * @private
   */
  _reportMetrics() {
    try {
      if (!this.metricsCollector || !this.metrics) return;

      // Report cache hit rates
      this.metrics.cacheHitRate.forEach((rate, operation) => {
        this.metricsCollector.recordCacheHitRate(operation, rate);
      });

      // Report error counts
      this.metrics.errors.forEach((count, errorType) => {
        this.metricsCollector.recordErrorCount(errorType, count);
      });

      // Calculate and report average response times
      this.metrics.responseTime.forEach((times, operation) => {
        if (times.length > 0) {
          const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
          this.metricsCollector.recordAverageResponseTime(operation, avgTime);
        }
      });
    } catch (error) {
      console.warn('[PlatformService] Error reporting metrics:', error);
    }
  }

  /**
   * Schedule periodic integrity checks
   *
   * @private
   */
  _scheduleIntegrityChecks() {
    try {
      if (!this.securityConfig?.integrityCheckEnabled) return;

      // Check integrity every 5 minutes
      setInterval(() => {
        this._validateConfigurationIntegrity();
      }, 300000);
    } catch (error) {
      console.warn('[PlatformService] Error scheduling integrity checks:', error);
    }
  }

  /**
   * Handle integrity failure
   *
   * @private
   */
  _handleIntegrityFailure() {
    try {
      // Log security event
      console.error('[PlatformService] SECURITY ALERT: Configuration integrity compromised');

      // Disable enhanced features temporarily
      this.securityConfig.integrityCheckEnabled = false;

      // Emit security event if possible
      if (typeof window !== 'undefined' && window.dispatchEvent) {
        const securityEvent = new CustomEvent('platform_security_alert', {
          detail: {
            type: 'configuration_integrity_failure',
            timestamp: Date.now(),
            service: 'PlatformService'
          }
        });
        window.dispatchEvent(securityEvent);
      }
    } catch (error) {
      console.error('[PlatformService] Error handling integrity failure:', error);
    }
  }

  /**
   * Initialize WebSocket connection for real-time updates
   *
   * @private
   */
  _initializeWebSocketConnection() {
    try {
      if (!this.realTimeConfig?.enabled) return;

      const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/api/platform-config/ws`;

      this.realTimeConfig.websocket = new WebSocket(wsUrl);

      this.realTimeConfig.websocket.onopen = () => {
        console.log('[PlatformService] WebSocket connected for real-time updates');
        this.realTimeConfig.reconnectAttempts = 0;
      };

      this.realTimeConfig.websocket.onmessage = (event) => {
        this._handleWebSocketMessage(event);
      };

      this.realTimeConfig.websocket.onclose = () => {
        console.log('[PlatformService] WebSocket disconnected');
        this._scheduleWebSocketReconnect();
      };

      this.realTimeConfig.websocket.onerror = (error) => {
        console.error('[PlatformService] WebSocket error:', error);
        this._recordError('websocket', error);
      };
    } catch (error) {
      console.warn('[PlatformService] Error initializing WebSocket:', error);
      this.realTimeConfig.enabled = false;
    }
  }

  /**
   * Handle WebSocket message for configuration updates
   *
   * @private
   * @param {MessageEvent} event - WebSocket message event
   */
  _handleWebSocketMessage(event) {
    try {
      const data = JSON.parse(event.data);

      switch (data.type) {
        case 'configuration_update':
          this._handleConfigurationUpdate(data.payload);
          break;
        case 'platform_added':
          this._handlePlatformAdded(data.payload);
          break;
        case 'platform_updated':
          this._handlePlatformUpdated(data.payload);
          break;
        case 'version_update':
          this._handleVersionUpdate(data.payload);
          break;
        default:
          console.warn('[PlatformService] Unknown WebSocket message type:', data.type);
      }
    } catch (error) {
      console.error('[PlatformService] Error handling WebSocket message:', error);
      this._recordError('websocketMessage', error);
    }
  }

  /**
   * Schedule WebSocket reconnection
   *
   * @private
   */
  _scheduleWebSocketReconnect() {
    try {
      if (!this.realTimeConfig?.enabled) return;

      if (this.realTimeConfig.reconnectAttempts >= this.realTimeConfig.maxReconnectAttempts) {
        console.warn('[PlatformService] Max WebSocket reconnect attempts reached');
        this.realTimeConfig.enabled = false;
        return;
      }

      const delay = this.realTimeConfig.reconnectInterval * Math.pow(2, this.realTimeConfig.reconnectAttempts);

      setTimeout(() => {
        this.realTimeConfig.reconnectAttempts++;
        this._initializeWebSocketConnection();
      }, delay);
    } catch (error) {
      console.error('[PlatformService] Error scheduling WebSocket reconnect:', error);
    }
  }

  /**
   * Handle configuration update from WebSocket
   *
   * @private
   * @param {Object} payload - Update payload
   */
  _handleConfigurationUpdate(payload) {
    try {
      if (!payload || !payload.version) return;

      // Validate version
      if (payload.version <= this.realTimeConfig.configurationVersion) {
        console.log('[PlatformService] Ignoring older configuration version');
        return;
      }

      // Backup current configuration
      this.configurationVersions.set(this.realTimeConfig.configurationVersion, {
        platforms: { ...this.platforms },
        timestamp: Date.now(),
        checksum: this.configurationHash
      });

      // Apply new configuration
      if (payload.platforms) {
        this.platforms = { ...this.platforms, ...payload.platforms };
      }

      // Update version and hash
      this.realTimeConfig.configurationVersion = payload.version;
      this.configurationHash = this._generateConfigurationHash();
      this.realTimeConfig.lastUpdate = Date.now();

      console.log(`[PlatformService] Configuration updated to version ${payload.version}`);

      // Emit update event
      this._emitConfigurationUpdateEvent(payload);
    } catch (error) {
      console.error('[PlatformService] Error handling configuration update:', error);
      this._recordError('configurationUpdate', error);
    }
  }

  /**
   * Initialize message cache cleanup
   *
   * @private
   */
  _initializeMessageCacheCleanup() {
    try {
      if (!this.deduplicationConfig?.enabled) return;

      // Clean up expired entries every 5 minutes
      setInterval(() => {
        this._cleanupMessageCache();
      }, 300000);
    } catch (error) {
      console.warn('[PlatformService] Error initializing message cache cleanup:', error);
    }
  }

  /**
   * Clean up expired message cache entries
   *
   * @private
   */
  _cleanupMessageCache() {
    try {
      if (!this.messageCache) return;

      const now = Date.now();
      const windowMs = this.deduplicationConfig.windowMs;
      let cleanedCount = 0;

      for (const [fingerprint, entry] of this.messageCache.entries()) {
        if (now - entry.timestamp > windowMs) {
          this.messageCache.delete(fingerprint);
          cleanedCount++;
        }
      }

      this.messageCacheStats.cacheSize = this.messageCache.size;

      if (cleanedCount > 0) {
        console.log(`[PlatformService] Cleaned up ${cleanedCount} expired message cache entries`);
      }
    } catch (error) {
      console.warn('[PlatformService] Error cleaning up message cache:', error);
    }
  }

  /**
   * Cache message fingerprint
   *
   * @private
   * @param {string} fingerprint - Message fingerprint
   * @param {Object} data - Message data
   */
  _cacheMessageFingerprint(fingerprint, data) {
    try {
      if (!this.messageCache) return;

      this.messageCache.set(fingerprint, data);
      this.messageCacheStats.cacheSize = this.messageCache.size;
    } catch (error) {
      console.warn('[PlatformService] Error caching message fingerprint:', error);
    }
  }

  /**
   * Cache platform fingerprint
   *
   * @private
   * @param {string} platformName - Platform name
   * @param {Object} fingerprint - Platform fingerprint
   */
  _cachePlatformFingerprint(platformName, fingerprint) {
    try {
      if (!this.fingerprintCache) return;

      const cacheKey = `${platformName}_${fingerprint.security.sessionId}`;
      this.fingerprintCache.set(cacheKey, {
        fingerprint,
        timestamp: Date.now(),
        ttl: this.fingerprintingConfig.cacheTTL
      });

      this.fingerprintStats.cached++;
    } catch (error) {
      console.warn('[PlatformService] Error caching platform fingerprint:', error);
    }
  }

  /**
   * Normalize content for platform-specific processing
   *
   * @private
   * @param {string} platformName - Platform name
   * @param {string} content - Content to normalize
   * @returns {string} Normalized content
   */
  _normalizeContentForPlatform(platformName, content) {
    try {
      if (!content || typeof content !== 'string') return '';

      const platform = this.getPlatform(platformName);
      let normalized = content.toLowerCase().trim();

      // Platform-specific normalization
      switch (platform.name.toLowerCase()) {
        case 'twitter':
          // Remove @mentions and #hashtags for similarity comparison
          normalized = normalized.replace(/@\w+/g, '').replace(/#\w+/g, '');
          break;
        case 'linkedin':
          // Preserve professional formatting
          normalized = normalized.replace(/\s+/g, ' ');
          break;
        case 'instagram':
          // Remove hashtags but preserve emojis
          normalized = normalized.replace(/#\w+/g, '');
          break;
        default:
          // Standard normalization
          normalized = normalized.replace(/\s+/g, ' ');
      }

      return normalized.trim();
    } catch (error) {
      console.warn('[PlatformService] Error normalizing content:', error);
      return content?.toLowerCase().trim() || '';
    }
  }

  /**
   * Calculate content similarity
   *
   * @private
   * @param {string} content1 - First content
   * @param {string} content2 - Second content
   * @returns {number} Similarity score (0-1)
   */
  _calculateContentSimilarity(content1, content2) {
    try {
      if (content1 === content2) return 1.0;
      if (!content1 || !content2) return 0.0;

      // Simple Jaccard similarity
      const words1 = new Set(content1.toLowerCase().split(/\s+/));
      const words2 = new Set(content2.toLowerCase().split(/\s+/));

      const intersection = new Set([...words1].filter(x => words2.has(x)));
      const union = new Set([...words1, ...words2]);

      return union.size > 0 ? intersection.size / union.size : 0.0;
    } catch (error) {
      console.warn('[PlatformService] Error calculating content similarity:', error);
      return 0.0;
    }
  }

  /**
   * Generate secure hash
   *
   * @private
   * @param {string} input - Input string to hash
   * @returns {Promise<string>} Hash value
   */
  async _generateSecureHash(input) {
    try {
      // Use SubtleCrypto if available
      if (window.crypto && window.crypto.subtle) {
        const msgBuffer = new TextEncoder().encode(input);
        const hashBuffer = await window.crypto.subtle.digest('SHA-256', msgBuffer);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      }

      // Fallback to simple hash
      return this._simpleHash(input);
    } catch (error) {
      console.warn('[PlatformService] Error generating secure hash, using fallback:', error);
      return this._simpleHash(input);
    }
  }

  /**
   * Get platform capabilities
   *
   * @private
   * @param {string} platformName - Platform name
   * @returns {Object} Platform capabilities
   */
  _getPlatformCapabilities(platformName) {
    try {
      const platform = this.getPlatform(platformName);

      return {
        maxCharacters: platform.maxCharacters,
        engagementTypes: platform.engagementTypes.length,
        features: Object.keys(platform.features).length,
        supportedFeatures: Object.keys(platform.features).filter(
          feature => platform.features[feature]
        ).length,
        hasAdvancedFeatures: platform.features.hasLiveStreaming ||
                           platform.features.hasStories ||
                           platform.features.hasPolls
      };
    } catch (error) {
      console.warn('[PlatformService] Error getting platform capabilities:', error);
      return {};
    }
  }

  /**
   * Generate session ID
   *
   * @private
   * @returns {string} Session ID
   */
  _generateSessionId() {
    try {
      return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    } catch (error) {
      console.warn('[PlatformService] Error generating session ID:', error);
      return `fallback_${Date.now()}`;
    }
  }

  /**
   * Simple hash function (fallback)
   *
   * @private
   * @param {string} str - String to hash
   * @returns {string} Hash value
   */
  _simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Initialize fallback mode
   *
   * @private
   */
  _initializeFallbackMode() {
    try {
      console.warn('[PlatformService] Running in fallback mode - enhanced features disabled');

      // Disable enhanced features
      this.metricsCollector = null;
      this.securityConfig = { integrityCheckEnabled: false };
      this.realTimeConfig = { enabled: false };
      this.deduplicationConfig = { enabled: false };
      this.fingerprintingConfig = { enabled: false };
    } catch (error) {
      console.error('[PlatformService] Error initializing fallback mode:', error);
    }
  }

  /**
   * Emit configuration update event
   *
   * @private
   * @param {Object} payload - Update payload
   */
  _emitConfigurationUpdateEvent(payload) {
    try {
      if (typeof window !== 'undefined' && window.dispatchEvent) {
        const updateEvent = new CustomEvent('platform_configuration_updated', {
          detail: {
            version: payload.version,
            platforms: payload.platforms,
            timestamp: Date.now()
          }
        });
        window.dispatchEvent(updateEvent);
      }
    } catch (error) {
      console.warn('[PlatformService] Error emitting configuration update event:', error);
    }
  }

  /**
   * Handle platform added event
   *
   * @private
   * @param {Object} payload - Platform data
   */
  _handlePlatformAdded(payload) {
    try {
      if (payload.platform && payload.config) {
        this.platforms[payload.platform] = payload.config;
        console.log(`[PlatformService] Platform added: ${payload.platform}`);
      }
    } catch (error) {
      console.error('[PlatformService] Error handling platform added:', error);
    }
  }

  /**
   * Handle platform updated event
   *
   * @private
   * @param {Object} payload - Platform update data
   */
  _handlePlatformUpdated(payload) {
    try {
      if (payload.platform && payload.config && this.platforms[payload.platform]) {
        this.platforms[payload.platform] = { ...this.platforms[payload.platform], ...payload.config };
        console.log(`[PlatformService] Platform updated: ${payload.platform}`);
      }
    } catch (error) {
      console.error('[PlatformService] Error handling platform updated:', error);
    }
  }

  /**
   * Handle version update event
   *
   * @private
   * @param {Object} payload - Version update data
   */
  _handleVersionUpdate(payload) {
    try {
      if (payload.version) {
        this.realTimeConfig.configurationVersion = payload.version;
        console.log(`[PlatformService] Version updated: ${payload.version}`);
      }
    } catch (error) {
      console.error('[PlatformService] Error handling version update:', error);
    }
  }
}

// Export singleton instance
export default new EnhancedPlatformService();
