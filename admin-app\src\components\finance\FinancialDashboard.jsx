import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  CircularProgress,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
} from '@mui/material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { format, subMonths } from 'date-fns';

// API
import api from '../../api';

const FinancialDashboard = ({ dashboardData, onRefresh }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [revenueData, setRevenueData] = useState([]);
  const [planBreakdown, setPlanBreakdown] = useState([]);

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  useEffect(() => {
    if (dashboardData) {
      loadAdditionalData();
    }
  }, [dashboardData]);

  const loadAdditionalData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load revenue trends
      const trendsResponse = await api.get('/api/admin/finance/analytics/revenue-trends?months=12');
      setRevenueData(trendsResponse.data.data || []);

      // Process plan breakdown data
      if (dashboardData.revenue_by_plan) {
        const planData = Object.entries(dashboardData.revenue_by_plan).map(([plan, revenue]) => ({
          name: plan.charAt(0).toUpperCase() + plan.slice(1),
          value: revenue,
          customers: dashboardData.customers_by_plan[plan] || 0,
        }));
        setPlanBreakdown(planData);
      }
    } catch (err) {
      console.error('Error loading additional data:', err);
      setError(err.response?.data?.detail || 'Failed to load additional data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <Paper sx={{ p: 2 }}>
          <Typography variant="body2" gutterBottom>
            {label}
          </Typography>
          {payload.map((entry, index) => (
            <Typography key={index} variant="body2" style={{ color: entry.color }}>
              {entry.name}: {formatCurrency(entry.value)}
            </Typography>
          ))}
        </Paper>
      );
    }
    return null;
  };

  if (!dashboardData) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {loading && <LinearProgress sx={{ mb: 2 }} />}

      <Grid container spacing={3}>
        {/* Revenue Trends Chart */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardHeader 
              title="Revenue Trends (12 Months)"
              subheader="Monthly revenue breakdown by category"
            />
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <AreaChart data={revenueData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`} />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="subscription_revenue"
                    stackId="1"
                    stroke="#8884d8"
                    fill="#8884d8"
                    name="Subscription Revenue"
                  />
                  <Area
                    type="monotone"
                    dataKey="addon_revenue"
                    stackId="1"
                    stroke="#82ca9d"
                    fill="#82ca9d"
                    name="Add-on Revenue"
                  />
                  <Area
                    type="monotone"
                    dataKey="appsumo_revenue"
                    stackId="1"
                    stroke="#ffc658"
                    fill="#ffc658"
                    name="AppSumo Revenue"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Plan Revenue Breakdown */}
        <Grid item xs={12} lg={4}>
          <Card>
            <CardHeader 
              title="Revenue by Plan"
              subheader="Current month breakdown"
            />
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={planBreakdown}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {planBreakdown.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => formatCurrency(value)} />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Key Performance Indicators */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Key Performance Indicators" />
            <CardContent>
              <Box mb={3}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2">Monthly Recurring Revenue</Typography>
                  <Typography variant="h6">{formatCurrency(dashboardData.current_mrr)}</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={Math.min((dashboardData.current_mrr / 50000) * 100, 100)} 
                  sx={{ height: 8, borderRadius: 4 }}
                />
                <Typography variant="caption" color="text.secondary">
                  Target: $50,000/month
                </Typography>
              </Box>

              <Box mb={3}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2">Annual Recurring Revenue</Typography>
                  <Typography variant="h6">{formatCurrency(dashboardData.current_arr)}</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={Math.min((dashboardData.current_arr / 600000) * 100, 100)} 
                  sx={{ height: 8, borderRadius: 4 }}
                />
                <Typography variant="caption" color="text.secondary">
                  Target: $600,000/year
                </Typography>
              </Box>

              <Box mb={3}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2">Customer Lifetime Value</Typography>
                  <Typography variant="h6">{formatCurrency(dashboardData.average_ltv)}</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={Math.min((dashboardData.average_ltv / 5000) * 100, 100)} 
                  sx={{ height: 8, borderRadius: 4 }}
                />
                <Typography variant="caption" color="text.secondary">
                  Target: $5,000 LTV
                </Typography>
              </Box>

              <Box>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2">Churn Rate</Typography>
                  <Chip 
                    label={formatPercentage(dashboardData.churn_rate)}
                    color={dashboardData.churn_rate <= 0.05 ? "success" : "warning"}
                    size="small"
                  />
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={dashboardData.churn_rate * 100} 
                  color={dashboardData.churn_rate <= 0.05 ? "success" : "warning"}
                  sx={{ height: 8, borderRadius: 4 }}
                />
                <Typography variant="caption" color="text.secondary">
                  Target: &lt;5% monthly churn
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Plan Performance Table */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Plan Performance" />
            <CardContent>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Plan</TableCell>
                      <TableCell align="right">Customers</TableCell>
                      <TableCell align="right">Revenue</TableCell>
                      <TableCell align="right">ARPU</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {planBreakdown.map((plan) => (
                      <TableRow key={plan.name}>
                        <TableCell component="th" scope="row">
                          <Box display="flex" alignItems="center">
                            <Box
                              sx={{
                                width: 12,
                                height: 12,
                                borderRadius: '50%',
                                backgroundColor: COLORS[planBreakdown.indexOf(plan) % COLORS.length],
                                mr: 1,
                              }}
                            />
                            {plan.name}
                          </Box>
                        </TableCell>
                        <TableCell align="right">{plan.customers}</TableCell>
                        <TableCell align="right">{formatCurrency(plan.value)}</TableCell>
                        <TableCell align="right">
                          {plan.customers > 0 ? formatCurrency(plan.value / plan.customers) : '$0'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activity Summary */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Financial Health Summary" />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary">
                      {formatCurrency(dashboardData.total_revenue_this_month)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Revenue This Month
                    </Typography>
                    <Typography 
                      variant="body2" 
                      color={dashboardData.revenue_growth >= 0 ? "success.main" : "error.main"}
                    >
                      {dashboardData.revenue_growth >= 0 ? '+' : ''}{formatPercentage(dashboardData.revenue_growth)} vs last month
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary">
                      {dashboardData.total_customers}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Active Customers
                    </Typography>
                    <Typography 
                      variant="body2" 
                      color={dashboardData.customer_growth >= 0 ? "success.main" : "error.main"}
                    >
                      {dashboardData.customer_growth >= 0 ? '+' : ''}{formatPercentage(dashboardData.customer_growth)} growth
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary">
                      {formatPercentage(dashboardData.payment_success_rate)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Payment Success Rate
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {dashboardData.failed_payments} failed payments
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary">
                      {dashboardData.pending_retries}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Pending Retries
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Automatic retry system
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default FinancialDashboard;
