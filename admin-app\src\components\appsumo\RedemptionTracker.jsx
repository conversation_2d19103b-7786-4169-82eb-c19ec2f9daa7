import React, { useState, useMemo } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Alert,
  Skeleton,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  Person as PersonIcon,
  Code as CodeIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { formatDate, formatDateTime, getRelativeTime, exportToCSV, debounce } from '../../utils/appsumoHelpers';

/**
 * Redemption Tracker Component
 * Displays real-time redemption tracking with filtering and search
 */
const RedemptionTracker = ({ 
  data, 
  loading, 
  error,
  onRefresh,
  className,
  ...props 
}) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    status: 'all',
    tier: 'all',
    dateRange: 'all',
  });
  const [filterMenuAnchor, setFilterMenuAnchor] = useState(null);

  // Debounced search function
  const debouncedSearch = useMemo(
    () => debounce((term) => {
      setSearchTerm(term);
      setPage(0); // Reset to first page on search
    }, 300),
    []
  );

  // Filter and search redemptions
  const filteredRedemptions = useMemo(() => {
    if (!data?.redemptions || !Array.isArray(data.redemptions)) {
      return [];
    }

    let filtered = [...data.redemptions];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(redemption => 
        redemption.code?.toLowerCase().includes(term) ||
        redemption.user_email?.toLowerCase().includes(term) ||
        redemption.user_name?.toLowerCase().includes(term) ||
        redemption.tier_type?.toLowerCase().includes(term)
      );
    }

    // Apply status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(redemption => redemption.status === filters.status);
    }

    // Apply tier filter
    if (filters.tier !== 'all') {
      filtered = filtered.filter(redemption => redemption.tier_type === filters.tier);
    }

    // Apply date range filter
    if (filters.dateRange !== 'all') {
      const now = new Date();
      let cutoffDate;
      
      switch (filters.dateRange) {
        case '24h':
          cutoffDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          cutoffDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          cutoffDate = null;
      }
      
      if (cutoffDate) {
        filtered = filtered.filter(redemption => {
          const redemptionDate = new Date(redemption.redeemed_at);
          return redemptionDate >= cutoffDate;
        });
      }
    }

    // Sort by redemption date (newest first)
    filtered.sort((a, b) => new Date(b.redeemed_at) - new Date(a.redeemed_at));

    return filtered;
  }, [data?.redemptions, searchTerm, filters]);

  // Get unique tiers for filter dropdown
  const availableTiers = useMemo(() => {
    if (!data?.redemptions) return [];
    const tiers = [...new Set(data.redemptions.map(r => r.tier_type).filter(Boolean))];
    return tiers.sort();
  }, [data?.redemptions]);

  // Handle pagination
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle search
  const handleSearchChange = (event) => {
    debouncedSearch(event.target.value);
  };

  // Handle filter changes
  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({ ...prev, [filterType]: value }));
    setPage(0);
    setFilterMenuAnchor(null);
  };

  // Handle export
  const handleExport = () => {
    const exportColumns = [
      { key: 'code', label: 'Code' },
      { key: 'user_email', label: 'User Email' },
      { key: 'user_name', label: 'User Name' },
      { key: 'tier_type', label: 'Tier' },
      { key: 'status', label: 'Status' },
      { key: 'redeemed_at', label: 'Redeemed At', type: 'date' },
      { key: 'ip_address', label: 'IP Address' },
    ];
    
    exportToCSV(
      filteredRedemptions, 
      `appsumo-redemptions-${formatDate(new Date())}`, 
      exportColumns
    );
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'refunded':
        return 'error';
      case 'expired':
        return 'warning';
      default:
        return 'default';
    }
  };

  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon fontSize="small" />;
      case 'refunded':
        return <CancelIcon fontSize="small" />;
      case 'expired':
        return <ScheduleIcon fontSize="small" />;
      default:
        return null;
    }
  };

  // Render loading skeleton
  if (loading && !data?.redemptions) {
    return (
      <Box className={className} {...props}>
        <Card variant="glass">
          <CardContent>
            <Skeleton variant="text" width="40%" height={32} sx={{ mb: 2 }} />
            {[1, 2, 3, 4, 5].map((item) => (
              <Skeleton key={item} variant="rectangular" height={60} sx={{ mb: 1 }} />
            ))}
          </CardContent>
        </Card>
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Box className={className} {...props}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  // Render empty state
  if (!data?.redemptions || data.redemptions.length === 0) {
    return (
      <Box className={className} {...props}>
        <Card variant="glass">
          <CardContent sx={{ textAlign: 'center', py: 6 }}>
            <CodeIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No Redemptions Found
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Redemption data will appear here once users start redeeming AppSumo codes.
            </Typography>
          </CardContent>
        </Card>
      </Box>
    );
  }

  const paginatedRedemptions = filteredRedemptions.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  return (
    <Box className={className} {...props}>
      <Card variant="glass">
        <CardContent>
          {/* Header */}
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <CodeIcon />
              Redemption Tracker
            </Typography>
            <Box display="flex" gap={1}>
              <Tooltip title="Export Data">
                <IconButton onClick={handleExport} disabled={filteredRedemptions.length === 0}>
                  <DownloadIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Filters">
                <IconButton onClick={(e) => setFilterMenuAnchor(e.currentTarget)}>
                  <FilterIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* Search and Filters */}
          <Box display="flex" gap={2} mb={3}>
            <TextField
              placeholder="Search by code, email, or user..."
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ flexGrow: 1 }}
            />
          </Box>

          {/* Results Summary */}
          <Typography variant="body2" color="text.secondary" mb={2}>
            Showing {paginatedRedemptions.length} of {filteredRedemptions.length} redemptions
          </Typography>

          {/* Redemptions Table */}
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Code</TableCell>
                  <TableCell>User</TableCell>
                  <TableCell>Tier</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Redeemed</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedRedemptions.map((redemption) => (
                  <TableRow key={redemption.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontFamily="monospace" fontWeight="medium">
                        {redemption.code}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="medium">
                          {redemption.user_name || 'Unknown User'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {redemption.user_email}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={redemption.tier_type?.toUpperCase() || 'Unknown'}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={getStatusIcon(redemption.status)}
                        label={redemption.status?.charAt(0).toUpperCase() + redemption.status?.slice(1) || 'Unknown'}
                        color={getStatusColor(redemption.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2">
                          {formatDate(redemption.redeemed_at)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {getRelativeTime(redemption.redeemed_at)}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Tooltip title="View Details">
                        <IconButton size="small">
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          <TablePagination
            component="div"
            count={filteredRedemptions.length}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={[10, 25, 50, 100]}
          />
        </CardContent>
      </Card>

      {/* Filter Menu */}
      <Menu
        anchorEl={filterMenuAnchor}
        open={Boolean(filterMenuAnchor)}
        onClose={() => setFilterMenuAnchor(null)}
      >
        <MenuItem disabled>
          <Typography variant="subtitle2">Filter Options</Typography>
        </MenuItem>
        
        <MenuItem>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              label="Status"
            >
              <MenuItem value="all">All Statuses</MenuItem>
              <MenuItem value="active">Active</MenuItem>
              <MenuItem value="refunded">Refunded</MenuItem>
              <MenuItem value="expired">Expired</MenuItem>
            </Select>
          </FormControl>
        </MenuItem>

        <MenuItem>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Tier</InputLabel>
            <Select
              value={filters.tier}
              onChange={(e) => handleFilterChange('tier', e.target.value)}
              label="Tier"
            >
              <MenuItem value="all">All Tiers</MenuItem>
              {availableTiers.map((tier) => (
                <MenuItem key={tier} value={tier}>
                  {tier.toUpperCase()}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </MenuItem>

        <MenuItem>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Date Range</InputLabel>
            <Select
              value={filters.dateRange}
              onChange={(e) => handleFilterChange('dateRange', e.target.value)}
              label="Date Range"
            >
              <MenuItem value="all">All Time</MenuItem>
              <MenuItem value="24h">Last 24 Hours</MenuItem>
              <MenuItem value="7d">Last 7 Days</MenuItem>
              <MenuItem value="30d">Last 30 Days</MenuItem>
            </Select>
          </FormControl>
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default RedemptionTracker;
