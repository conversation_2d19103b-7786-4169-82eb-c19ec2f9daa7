"""
Schemas for competitor-related API requests and responses.
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, HttpUrl, ConfigDict

class CompetitorSocialMediaBase(BaseModel):
    platform: str
    account_name: str
    account_url: str
    followers_count: Optional[int] = None
    posts_frequency: Optional[str] = None

class CompetitorSocialMediaCreate(CompetitorSocialMediaBase):
    pass

class CompetitorSocialMediaUpdate(BaseModel):
    platform: Optional[str] = None
    account_name: Optional[str] = None
    account_url: Optional[str] = None
    followers_count: Optional[int] = None
    posts_frequency: Optional[str] = None

class CompetitorSocialMediaResponse(CompetitorSocialMediaBase):
    last_updated: datetime

    model_config = ConfigDict(from_attributes=True)

class CompetitorPostBase(BaseModel):
    platform: str
    post_url: str
    post_date: datetime
    content_type: str
    content_text: Optional[str] = None
    image_url: Optional[str] = None
    video_url: Optional[str] = None
    likes: Optional[int] = None
    comments: Optional[int] = None
    shares: Optional[int] = None
    views: Optional[int] = None
    engagement_rate: Optional[float] = None
    hashtags: List[str] = []
    mentions: List[str] = []
    topics: List[str] = []
    sentiment: Optional[str] = None

class CompetitorPostCreate(CompetitorPostBase):
    pass

class CompetitorPostResponse(CompetitorPostBase):
    model_config = ConfigDict(from_attributes=True)

class CompetitorAnalysisBase(BaseModel):
    content_strategy: Optional[str] = None
    posting_routine: Optional[Dict[str, Any]] = None
    content_types_distribution: Optional[Dict[str, float]] = None
    top_performing_content: Optional[List[Dict[str, Any]]] = None
    engagement_metrics: Optional[Dict[str, Any]] = None
    strengths: List[str] = []
    weaknesses: List[str] = []
    opportunities: List[str] = []

class CompetitorAnalysisResponse(CompetitorAnalysisBase):
    generated_at: datetime

    model_config = ConfigDict(from_attributes=True)

class CompetitorBase(BaseModel):
    name: str
    description: Optional[str] = None
    website: Optional[str] = None
    industry: Optional[str] = None
    company_size: Optional[str] = None
    target_audience: Optional[str] = None
    is_active: bool = True

class CompetitorCreate(CompetitorBase):
    social_media: List[CompetitorSocialMediaCreate] = []

class CompetitorUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    website: Optional[str] = None
    industry: Optional[str] = None
    company_size: Optional[str] = None
    target_audience: Optional[str] = None
    is_active: Optional[bool] = None
    social_media: Optional[List[CompetitorSocialMediaCreate]] = None

class CompetitorResponse(CompetitorBase):
    id: str
    user_id: str
    social_media: List[CompetitorSocialMediaResponse] = []
    analysis: Optional[CompetitorAnalysisResponse] = None
    created_at: datetime
    updated_at: datetime
    last_synced: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

class CompetitorDetailResponse(CompetitorResponse):
    posts: List[CompetitorPostResponse] = []

class SyncCompetitorRequest(BaseModel):
    competitor_id: str
    platforms: List[str] = []  # If empty, sync all platforms

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "competitor_id": "60d5ec9af682dbd12a0a9fb7",
                "platforms": ["instagram", "twitter"]
            }
        }
    )

class CompetitorAnalysisRequest(BaseModel):
    competitor_id: str

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "competitor_id": "60d5ec9af682dbd12a0a9fb7"
            }
        }
    )

class CompetitorComparisonRequest(BaseModel):
    competitor_ids: List[str]
    metrics: List[str] = ["engagement_rate", "posting_frequency", "content_types"]
    time_period: str = "last_30_days"  # "last_7_days", "last_30_days", "last_90_days"

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "competitor_ids": ["60d5ec9af682dbd12a0a9fb7", "60d5ec9af682dbd12a0a9fb8"],
                "metrics": ["engagement_rate", "posting_frequency", "content_types"],
                "time_period": "last_30_days"
            }
        }
    )

class CompetitorRecommendationRequest(BaseModel):
    competitor_ids: List[str] = []  # If empty, use all active competitors
    focus_areas: List[str] = ["content_strategy", "posting_schedule", "engagement"]

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "competitor_ids": ["60d5ec9af682dbd12a0a9fb7"],
                "focus_areas": ["content_strategy", "posting_schedule", "engagement"]
            }
        }
    )

class CompetitorRecommendationResponse(BaseModel):
    recommendations: List[Dict[str, Any]]
    generated_at: datetime

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "recommendations": [
                    {
                        "area": "content_strategy",
                        "recommendation": "Increase video content to match competitor performance",
                        "reasoning": "Competitor's video content achieves 30% higher engagement",
                        "priority": "high"
                    },
                    {
                        "area": "posting_schedule",
                        "recommendation": "Post more frequently on Wednesdays",
                        "reasoning": "Competitor posts get highest engagement on Wednesdays",
                        "priority": "medium"
                    }
                ],
                "generated_at": "2023-01-01T12:00:00"
            }
        }
    )

# Price tracking schemas
class ProductToTrack(BaseModel):
    """Schema for products to track in competitor price monitoring."""
    id: str
    name: str
    url: str
    current_price: Optional[float] = None
    currency: str = "USD"
    category: Optional[str] = None

class CompetitorPriceTrackingRequest(BaseModel):
    """Request schema for starting competitor price tracking."""
    competitor_id: str
    products: List[ProductToTrack]
    check_frequency: str = Field(default="daily", pattern="^(hourly|daily|weekly)$")
    price_change_threshold: float = Field(default=0.05, ge=0.0, le=1.0)
    alert_settings: Dict[str, bool] = Field(default_factory=lambda: {
        "email_alerts": True,
        "push_notifications": True,
        "price_drop_alerts": True,
        "price_increase_alerts": False
    })

class CompetitorPriceAlert(BaseModel):
    """Schema for competitor price alerts."""
    id: str
    competitor_id: str
    product_name: str
    product_url: Optional[str] = None
    old_price: float
    new_price: float
    price_change: float
    price_change_percentage: float
    alert_type: str = "price_change"
    created_at: datetime
    is_read: bool = False

class CompetitorPriceHistory(BaseModel):
    """Schema for competitor price history."""
    product_id: str
    product_name: str
    price: float
    currency: str = "USD"
    source_url: str
    checked_at: datetime
