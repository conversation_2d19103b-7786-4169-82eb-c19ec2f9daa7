import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
  Alert,
  Skeleton,
  useTheme,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Analytics as AnalyticsIcon,
  Code as CodeIcon,
  Redeem as RedeemIcon,
  Timeline as TimelineIcon,
} from '@mui/icons-material';
import { calculateRedemptionStats, formatCurrency, formatDate, exportToCSV } from '../../utils/appsumoHelpers';

/**
 * AppSumo Analytics Dashboard Component
 * Displays real-time analytics with charts and statistics
 */
const AppSumoAnalytics = ({ 
  data, 
  loading, 
  error, 
  onRefresh,
  className,
  ...props 
}) => {
  const theme = useTheme();
  const [refreshing, setRefreshing] = useState(false);

  // Calculate analytics from data
  const analytics = useMemo(() => {
    if (!data?.codes || !Array.isArray(data.codes)) {
      return null;
    }

    const stats = calculateRedemptionStats(data.codes);
    
    // Calculate revenue estimates (assuming average deal value)
    const avgDealValue = 59; // AppSumo typical deal price
    const estimatedRevenue = stats.redeemed * avgDealValue;
    
    // Calculate conversion trends
    const last7Days = data.codes.filter(code => {
      if (!code.redeemed_at) return false;
      const redemptionDate = new Date(code.redeemed_at);
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      return redemptionDate >= sevenDaysAgo;
    });
    
    const prev7Days = data.codes.filter(code => {
      if (!code.redeemed_at) return false;
      const redemptionDate = new Date(code.redeemed_at);
      const fourteenDaysAgo = new Date();
      fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 14);
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      return redemptionDate >= fourteenDaysAgo && redemptionDate < sevenDaysAgo;
    });
    
    const weeklyTrend = last7Days.length - prev7Days.length;
    const weeklyTrendPercent = prev7Days.length > 0 ? 
      ((last7Days.length - prev7Days.length) / prev7Days.length) * 100 : 0;

    return {
      ...stats,
      estimatedRevenue,
      weeklyRedemptions: last7Days.length,
      weeklyTrend,
      weeklyTrendPercent,
    };
  }, [data]);

  // Handle refresh
  const handleRefresh = async () => {
    if (onRefresh) {
      setRefreshing(true);
      try {
        await onRefresh();
      } finally {
        setRefreshing(false);
      }
    }
  };

  // Handle export
  const handleExport = () => {
    if (!data?.codes) return;
    
    const exportColumns = [
      { key: 'code', label: 'Code' },
      { key: 'tier_type', label: 'Tier' },
      { key: 'status', label: 'Status' },
      { key: 'redeemed_at', label: 'Redeemed At', type: 'date' },
      { key: 'created_at', label: 'Created At', type: 'date' },
    ];
    
    exportToCSV(data.codes, `appsumo-analytics-${formatDate(new Date())}`, exportColumns);
  };

  // Render loading skeleton
  if (loading && !analytics) {
    return (
      <Box className={className} {...props}>
        <Grid container spacing={3}>
          {[1, 2, 3, 4].map((item) => (
            <Grid item xs={12} sm={6} md={3} key={item}>
              <Card variant="glass">
                <CardContent>
                  <Skeleton variant="text" width="60%" height={24} />
                  <Skeleton variant="text" width="40%" height={32} />
                  <Skeleton variant="text" width="80%" height={20} />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Box className={className} {...props}>
        <Alert 
          severity="error" 
          action={
            <IconButton
              color="inherit"
              size="small"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshIcon />
            </IconButton>
          }
        >
          {error}
        </Alert>
      </Box>
    );
  }

  // Render empty state
  if (!analytics) {
    return (
      <Box className={className} {...props}>
        <Card variant="glass">
          <CardContent sx={{ textAlign: 'center', py: 6 }}>
            <AnalyticsIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No Analytics Data Available
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Analytics will appear here once you have AppSumo codes and redemptions.
            </Typography>
          </CardContent>
        </Card>
      </Box>
    );
  }

  return (
    <Box className={className} {...props}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AnalyticsIcon />
          AppSumo Analytics
        </Typography>
        <Box>
          <Tooltip title="Export Data">
            <IconButton onClick={handleExport} disabled={!data?.codes?.length}>
              <DownloadIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Refresh Data">
            <IconButton onClick={handleRefresh} disabled={refreshing}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Analytics Cards */}
      <Grid container spacing={3}>
        {/* Total Codes */}
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <Typography variant="body2" color="text.secondary">
                  Total Codes
                </Typography>
                <CodeIcon color="primary" />
              </Box>
              <Typography variant="h4" fontWeight="bold" color="primary.main">
                {analytics.total.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Generated codes
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Redeemed Codes */}
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <Typography variant="body2" color="text.secondary">
                  Redeemed
                </Typography>
                <RedeemIcon color="success" />
              </Box>
              <Typography variant="h4" fontWeight="bold" color="success.main">
                {analytics.redeemed.toLocaleString()}
              </Typography>
              <Box display="flex" alignItems="center" gap={1} mt={1}>
                <LinearProgress
                  variant="determinate"
                  value={analytics.redemptionRate}
                  sx={{ flexGrow: 1, height: 6, borderRadius: 3 }}
                  color="success"
                />
                <Typography variant="body2" color="text.secondary">
                  {analytics.redemptionRate.toFixed(1)}%
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Weekly Trend */}
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <Typography variant="body2" color="text.secondary">
                  Weekly Trend
                </Typography>
                <TimelineIcon color="info" />
              </Box>
              <Typography variant="h4" fontWeight="bold" color="info.main">
                {analytics.weeklyRedemptions}
              </Typography>
              <Box display="flex" alignItems="center" gap={1} mt={1}>
                {analytics.weeklyTrend >= 0 ? (
                  <TrendingUpIcon color="success" fontSize="small" />
                ) : (
                  <TrendingDownIcon color="error" fontSize="small" />
                )}
                <Typography 
                  variant="body2" 
                  color={analytics.weeklyTrend >= 0 ? 'success.main' : 'error.main'}
                >
                  {analytics.weeklyTrend >= 0 ? '+' : ''}{analytics.weeklyTrend} 
                  ({analytics.weeklyTrendPercent.toFixed(1)}%)
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Estimated Revenue */}
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <Typography variant="body2" color="text.secondary">
                  Est. Revenue
                </Typography>
                <TrendingUpIcon color="warning" />
              </Box>
              <Typography variant="h4" fontWeight="bold" color="warning.main">
                {formatCurrency(analytics.estimatedRevenue)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Based on redemptions
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Tier Breakdown */}
        <Grid item xs={12} md={6}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Redemptions by Tier
              </Typography>
              {Object.entries(analytics.byTier).map(([tier, stats]) => (
                <Box key={tier} mb={2}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2" fontWeight="medium">
                      {tier.toUpperCase()}
                    </Typography>
                    <Chip 
                      label={`${stats.redeemed}/${stats.total}`}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={stats.total > 0 ? (stats.redeemed / stats.total) * 100 : 0}
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={6}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Activity
              </Typography>
              <Box>
                <Box display="flex" justifyContent="space-between" py={1}>
                  <Typography variant="body2" color="text.secondary">
                    Last 7 days
                  </Typography>
                  <Typography variant="body2" fontWeight="medium">
                    {analytics.weeklyRedemptions} redemptions
                  </Typography>
                </Box>
                <Box display="flex" justifyContent="space-between" py={1}>
                  <Typography variant="body2" color="text.secondary">
                    Last 30 days
                  </Typography>
                  <Typography variant="body2" fontWeight="medium">
                    {analytics.recentRedemptions} redemptions
                  </Typography>
                </Box>
                <Box display="flex" justifyContent="space-between" py={1}>
                  <Typography variant="body2" color="text.secondary">
                    Available codes
                  </Typography>
                  <Typography variant="body2" fontWeight="medium">
                    {analytics.available} codes
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AppSumoAnalytics;
