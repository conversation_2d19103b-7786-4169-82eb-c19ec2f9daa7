import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Alert,
  Switch,
  FormControlLabel,
  Chip,
  IconButton,
  Tooltip,
  <PERSON>per,
  Step,
  StepLabel,
  StepContent,
  Grid,
  Card,
  CardContent,
  Divider,
  InputAdornment,
} from '@mui/material';
import {
  Close as CloseIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Info as InfoIcon,
  AttachMoney as MoneyIcon,
  Schedule as ScheduleIcon,
  Category as CategoryIcon,
  Security as SecurityIcon,
  Preview as PreviewIcon,
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { 
  validateFormData, 
  formatCurrency, 
  generateCouponCode,
  validateCouponCode,
  formatDiscount,
  calculateDiscountAmount,
} from '../../utils/couponHelpers';
import api from '../../api';

/**
 * Advanced Coupon Creator Component
 * Comprehensive interface for creating coupons with validation and preview
 */
const CouponCreator = ({ 
  open, 
  onClose, 
  onCouponCreated,
  editingCoupon = null,
  ...props 
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    description: '',
    discount_type: 'percentage',
    discount_value: 0,
    applicable_to: 'all',
    specific_items: [],
    minimum_purchase_amount: 0,
    max_discount_amount: null,
    max_redemptions: null,
    max_redemptions_per_user: 1,
    start_date: new Date(),
    end_date: null,
    first_time_users_only: false,
    requires_subscription: false,
    is_active: true,
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [newItem, setNewItem] = useState('');

  const steps = [
    'Basic Information',
    'Discount Configuration',
    'Usage Restrictions',
    'Review & Create',
  ];

  // Available discount types
  const discountTypes = [
    { value: 'percentage', label: 'Percentage Off', description: 'Discount as percentage of total' },
    { value: 'fixed_amount', label: 'Fixed Amount Off', description: 'Fixed dollar amount discount' },
    { value: 'free_trial_extension', label: 'Free Trial Extension', description: 'Extend trial period' },
    { value: 'free_addon', label: 'Free Addon', description: 'Free addon or feature' },
  ];

  // Available applicable items
  const applicableItems = [
    { value: 'all', label: 'All Products', description: 'Apply to any purchase' },
    { value: 'subscription', label: 'Subscriptions Only', description: 'Apply to subscription plans' },
    { value: 'addon', label: 'Addons Only', description: 'Apply to addon purchases' },
  ];

  // Form validation rules for each step
  const validationRules = {
    0: [ // Basic Information
      { key: 'code', label: 'Coupon Code', type: 'string', minLength: 3, maxLength: 50 },
      { key: 'name', label: 'Name', type: 'string', minLength: 3, maxLength: 100 },
      { key: 'description', label: 'Description', type: 'string', minLength: 10, maxLength: 500 },
    ],
    1: [ // Discount Configuration
      { key: 'discount_value', label: 'Discount Value', type: 'number', min: 0 },
    ],
    2: [ // Usage Restrictions
      { key: 'max_redemptions_per_user', label: 'Max Redemptions Per User', type: 'number', min: 1 },
    ],
  };

  // Initialize form data when editing
  React.useEffect(() => {
    if (editingCoupon) {
      setFormData({
        ...editingCoupon,
        start_date: new Date(editingCoupon.start_date),
        end_date: editingCoupon.end_date ? new Date(editingCoupon.end_date) : null,
      });
    }
  }, [editingCoupon]);

  // Handle form field changes
  const handleFieldChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  // Generate random coupon code
  const handleGenerateCode = () => {
    const code = generateCouponCode('', 8);
    handleFieldChange('code', code);
  };

  // Handle specific items management
  const handleAddItem = () => {
    if (newItem.trim()) {
      setFormData(prev => ({
        ...prev,
        specific_items: [...prev.specific_items, newItem.trim()],
      }));
      setNewItem('');
    }
  };

  const handleRemoveItem = (index) => {
    setFormData(prev => ({
      ...prev,
      specific_items: prev.specific_items.filter((_, i) => i !== index),
    }));
  };

  // Validate current step
  const validateStep = (step) => {
    const rules = validationRules[step] || [];
    const validation = validateFormData(formData, rules);
    
    // Additional validations
    if (step === 0) {
      const codeValidation = validateCouponCode(formData.code);
      if (!codeValidation.isValid) {
        validation.errors.code = codeValidation.error;
        validation.isValid = false;
      }
    }
    
    if (step === 1) {
      if (formData.discount_type === 'percentage' && (formData.discount_value < 0 || formData.discount_value > 100)) {
        validation.errors.discount_value = 'Percentage must be between 0 and 100';
        validation.isValid = false;
      }
    }
    
    if (step === 2) {
      if (formData.end_date && formData.end_date <= formData.start_date) {
        validation.errors.end_date = 'End date must be after start date';
        validation.isValid = false;
      }
    }
    
    setErrors(validation.errors);
    return validation.isValid;
  };

  // Handle next step
  const handleNext = () => {
    if (validateStep(activeStep)) {
      if (activeStep === steps.length - 1) {
        handleSave();
      } else {
        setActiveStep(prev => prev + 1);
      }
    }
  };

  // Handle previous step
  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  // Handle save
  const handleSave = async () => {
    if (!validateStep(activeStep)) return;
    
    setLoading(true);
    try {
      const couponData = {
        ...formData,
        start_date: formData.start_date.toISOString(),
        end_date: formData.end_date ? formData.end_date.toISOString() : null,
      };
      
      let response;
      if (editingCoupon) {
        response = await api.put(`/api/coupons/${editingCoupon.id}`, couponData);
      } else {
        response = await api.post('/api/coupons', couponData);
      }
      
      if (onCouponCreated) {
        onCouponCreated(response.data);
      }
      
      handleClose();
    } catch (error) {
      console.error('Error saving coupon:', error);
      setErrors({ 
        save: error.response?.data?.detail || 'Failed to save coupon' 
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle close
  const handleClose = () => {
    setActiveStep(0);
    setFormData({
      code: '',
      name: '',
      description: '',
      discount_type: 'percentage',
      discount_value: 0,
      applicable_to: 'all',
      specific_items: [],
      minimum_purchase_amount: 0,
      max_discount_amount: null,
      max_redemptions: null,
      max_redemptions_per_user: 1,
      start_date: new Date(),
      end_date: null,
      first_time_users_only: false,
      requires_subscription: false,
      is_active: true,
    });
    setErrors({});
    setNewItem('');
    onClose();
  };

  // Calculate preview discount
  const previewDiscount = calculateDiscountAmount(100, formData);

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="lg" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxHeight: '90vh',
        }
      }}
      {...props}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">
            {editingCoupon ? 'Edit Coupon' : 'Create New Coupon'}
          </Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box display="flex" flexDirection="column" gap={3} mt={2}>
          {errors.save && (
            <Alert severity="error">{errors.save}</Alert>
          )}

          <Stepper activeStep={activeStep} orientation="vertical">
            {/* Step 1: Basic Information */}
            <Step>
              <StepLabel>Basic Information</StepLabel>
              <StepContent>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={8}>
                    <TextField
                      label="Coupon Code"
                      value={formData.code}
                      onChange={(e) => handleFieldChange('code', e.target.value.toUpperCase())}
                      error={!!errors.code}
                      helperText={errors.code || 'Unique identifier for this coupon'}
                      fullWidth
                      required
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <Tooltip title="Generate Random Code">
                              <IconButton onClick={handleGenerateCode} edge="end">
                                <AddIcon />
                              </IconButton>
                            </Tooltip>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Coupon Name"
                      value={formData.name}
                      onChange={(e) => handleFieldChange('name', e.target.value)}
                      error={!!errors.name}
                      helperText={errors.name || 'Display name for this coupon'}
                      fullWidth
                      required
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Description"
                      value={formData.description}
                      onChange={(e) => handleFieldChange('description', e.target.value)}
                      error={!!errors.description}
                      helperText={errors.description || 'Detailed description of the offer'}
                      fullWidth
                      multiline
                      rows={3}
                      required
                    />
                  </Grid>
                </Grid>
              </StepContent>
            </Step>

            {/* Step 2: Discount Configuration */}
            <Step>
              <StepLabel>Discount Configuration</StepLabel>
              <StepContent>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Discount Type</InputLabel>
                      <Select
                        value={formData.discount_type}
                        onChange={(e) => handleFieldChange('discount_type', e.target.value)}
                        label="Discount Type"
                      >
                        {discountTypes.map((type) => (
                          <MenuItem key={type.value} value={type.value}>
                            <Box>
                              <Typography variant="body2" fontWeight="medium">
                                {type.label}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {type.description}
                              </Typography>
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="Discount Value"
                      type="number"
                      value={formData.discount_value}
                      onChange={(e) => handleFieldChange('discount_value', parseFloat(e.target.value) || 0)}
                      error={!!errors.discount_value}
                      helperText={errors.discount_value}
                      fullWidth
                      required
                      InputProps={{
                        startAdornment: formData.discount_type === 'fixed_amount' ? (
                          <InputAdornment position="start">$</InputAdornment>
                        ) : formData.discount_type === 'percentage' ? (
                          <InputAdornment position="end">%</InputAdornment>
                        ) : null,
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="Minimum Purchase Amount"
                      type="number"
                      value={formData.minimum_purchase_amount}
                      onChange={(e) => handleFieldChange('minimum_purchase_amount', parseFloat(e.target.value) || 0)}
                      fullWidth
                      InputProps={{
                        startAdornment: <InputAdornment position="start">$</InputAdornment>,
                      }}
                      helperText="Minimum order value required"
                    />
                  </Grid>
                  {formData.discount_type === 'percentage' && (
                    <Grid item xs={12} md={6}>
                      <TextField
                        label="Maximum Discount Amount"
                        type="number"
                        value={formData.max_discount_amount || ''}
                        onChange={(e) => handleFieldChange('max_discount_amount', parseFloat(e.target.value) || null)}
                        fullWidth
                        InputProps={{
                          startAdornment: <InputAdornment position="start">$</InputAdornment>,
                        }}
                        helperText="Cap the maximum discount (optional)"
                      />
                    </Grid>
                  )}
                </Grid>
                
                {/* Preview */}
                <Card variant="outlined" sx={{ mt: 2, p: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Preview: {formatDiscount(formData)} discount
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    On a $100 order: Save {formatCurrency(previewDiscount)}
                  </Typography>
                </Card>
              </StepContent>
            </Step>

            {/* Step 3: Usage Restrictions */}
            <Step>
              <StepLabel>Usage Restrictions</StepLabel>
              <StepContent>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Applicable To</InputLabel>
                      <Select
                        value={formData.applicable_to}
                        onChange={(e) => handleFieldChange('applicable_to', e.target.value)}
                        label="Applicable To"
                      >
                        {applicableItems.map((item) => (
                          <MenuItem key={item.value} value={item.value}>
                            <Box>
                              <Typography variant="body2" fontWeight="medium">
                                {item.label}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {item.description}
                              </Typography>
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="Max Redemptions Per User"
                      type="number"
                      value={formData.max_redemptions_per_user}
                      onChange={(e) => handleFieldChange('max_redemptions_per_user', parseInt(e.target.value) || 1)}
                      error={!!errors.max_redemptions_per_user}
                      helperText={errors.max_redemptions_per_user}
                      fullWidth
                      inputProps={{ min: 1 }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="Total Max Redemptions (Optional)"
                      type="number"
                      value={formData.max_redemptions || ''}
                      onChange={(e) => handleFieldChange('max_redemptions', parseInt(e.target.value) || null)}
                      fullWidth
                      helperText="Leave empty for unlimited"
                      inputProps={{ min: 1 }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <DateTimePicker
                      label="Start Date"
                      value={formData.start_date}
                      onChange={(date) => handleFieldChange('start_date', date)}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          required: true,
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <DateTimePicker
                      label="End Date (Optional)"
                      value={formData.end_date}
                      onChange={(date) => handleFieldChange('end_date', date)}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          error: !!errors.end_date,
                          helperText: errors.end_date,
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Box display="flex" flexDirection="column" gap={1}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.first_time_users_only}
                            onChange={(e) => handleFieldChange('first_time_users_only', e.target.checked)}
                          />
                        }
                        label="First-time users only"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.requires_subscription}
                            onChange={(e) => handleFieldChange('requires_subscription', e.target.checked)}
                          />
                        }
                        label="Requires active subscription"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.is_active}
                            onChange={(e) => handleFieldChange('is_active', e.target.checked)}
                          />
                        }
                        label="Active coupon"
                      />
                    </Box>
                  </Grid>
                </Grid>
              </StepContent>
            </Step>

            {/* Step 4: Review & Create */}
            <Step>
              <StepLabel>Review & Create</StepLabel>
              <StepContent>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Coupon Summary
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary">Code:</Typography>
                        <Typography variant="body1" fontWeight="medium" fontFamily="monospace">
                          {formData.code}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary">Name:</Typography>
                        <Typography variant="body1" fontWeight="medium">
                          {formData.name}
                        </Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="body2" color="text.secondary">Description:</Typography>
                        <Typography variant="body1">
                          {formData.description}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary">Discount:</Typography>
                        <Typography variant="body1" fontWeight="medium">
                          {formatDiscount(formData)}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary">Usage Limit:</Typography>
                        <Typography variant="body1">
                          {formData.max_redemptions_per_user} per user
                          {formData.max_redemptions && `, ${formData.max_redemptions} total`}
                        </Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </StepContent>
            </Step>
          </Stepper>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        {activeStep > 0 && (
          <Button onClick={handleBack} disabled={loading}>
            Back
          </Button>
        )}
        <Button 
          onClick={handleNext} 
          variant="contained"
          disabled={loading}
        >
          {loading ? 'Saving...' : (activeStep === steps.length - 1 ? 
            (editingCoupon ? 'Update Coupon' : 'Create Coupon') : 'Next')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CouponCreator;
