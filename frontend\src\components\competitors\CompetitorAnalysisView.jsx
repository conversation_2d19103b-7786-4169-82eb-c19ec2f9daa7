/**
 * CompetitorAnalysisView Component - Enterprise-grade competitor analysis for ACE Social platform
 * Features: Advanced competitor research patterns, intelligent analysis generation, dynamic competitor optimization,
 * advanced competitor profiling, smart competitor tracking, adaptive analysis behaviors, contextual competitor states, accessibility-focused analysis navigation, responsive competitor patterns, and production-ready competitor analysis functionality
 */

import { memo, useState, useEffect, useCallback, useRef } from 'react';
import PropTypes from 'prop-types';
import { useLocation } from 'react-router-dom';
import {
  Box,
  Button,
  Chip,
  Grid,
  Paper,
  Typography,
  useTheme,
  alpha,
  useMediaQuery,
  Alert,
  CircularProgress
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Analytics as AnalyticsIcon,
  Lightbulb as LightbulbIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Stop as StopIcon,
  Monitor as MonitorIcon
} from '@mui/icons-material';
import { CustomCard, CustomCardContent } from '../common';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ianG<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer } from 'recharts';

// Enhanced styled components for enterprise-grade competitor analysis
const StyledAnalysisContainer = styled(Box)(({ theme, variant, isLoading, hasError }) => ({
  position: 'relative',
  width: '100%',
  transition: theme.transitions.create(['all'], {
    duration: theme.transitions.duration.standard
  }),
  ...(variant === 'premium' && {
    background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.02)}, ${alpha(theme.palette.secondary.main, 0.02)})`,
    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
  }),
  ...(hasError && {
    border: `2px solid ${theme.palette.error.main}`,
    backgroundColor: alpha(theme.palette.error.main, 0.05)
  }),
  ...(isLoading && {
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: '2px',
      background: `linear-gradient(90deg, transparent, ${theme.palette.primary.main}, transparent)`,
      zIndex: 1000,
      pointerEvents: 'none'
    }
  }),
  // Enhanced accessibility
  '&:focus-visible': {
    outline: `3px solid ${theme.palette.primary.main}`,
    outlineOffset: '2px'
  },
  // Enhanced responsive design
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(1)
  },
  // Enhanced high contrast mode
  '@media (prefers-contrast: high)': {
    border: `3px solid ${theme.palette.text.primary}`,
    background: theme.palette.background.paper
  },
  // Enhanced reduced motion support
  '@media (prefers-reduced-motion: reduce)': {
    transition: 'none',
    '&::before': {
      display: 'none'
    }
  }
}));

// Analysis constants and configurations
const ANALYSIS_VARIANTS = {
  STANDARD: 'standard',
  PREMIUM: 'premium',
  GLASS: 'glass',
  MINIMAL: 'minimal'
};

const ANALYSIS_ANALYTICS_EVENTS = {
  ANALYSIS_VIEW: 'competitor_analysis_view',
  ANALYSIS_RUN: 'competitor_analysis_run',
  ANALYSIS_EXPORT: 'competitor_analysis_export',
  ANALYSIS_SHARE: 'competitor_analysis_share',
  ANALYSIS_BOOKMARK: 'competitor_analysis_bookmark',
  ANALYSIS_ERROR: 'competitor_analysis_error'
};

/**
 * Enhanced enterprise-grade competitor analysis view component with comprehensive competitor research patterns,
 * intelligent analysis generation, dynamic competitor optimization, price tracking capabilities, and production-ready competitor analysis functionality
 */
const CompetitorAnalysisView = memo(({
  competitor,
  onAnalyze,
  // Enhanced props
  variant = ANALYSIS_VARIANTS.STANDARD,
  enableAnalytics = true,
  enableAccessibility = true,
  loading = false,
  error = null,
  onAnalytics,
  onError,
  userPlan = 'creator',
  testId,
  // Price tracking props
  enablePriceTracking = false,
  onPriceTrackingStart,
  onPriceTrackingStop,
  priceAlerts = []
}) => {
  const theme = useTheme();
  const location = useLocation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Enhanced state management
  const [isLoading, setIsLoading] = useState(loading);
  const [analysisError, setAnalysisError] = useState(error);

  // Price tracking state
  const [priceTrackingState, setPriceTrackingState] = useState({
    isTracking: false,
    trackedProducts: [],
    alerts: priceAlerts,
    showPriceDialog: false,
    loading: false,
    error: null
  });

  // Refs for performance optimization
  const containerRef = useRef(null);

  // Analytics tracking
  const trackAnalytics = useCallback((action, data = {}) => {
    if (!enableAnalytics || !onAnalytics) return;

    onAnalytics({
      component: 'CompetitorAnalysisView',
      action,
      variant,
      timestamp: Date.now(),
      path: location.pathname,
      userPlan,
      isMobile,
      competitorId: competitor?.id,
      hasAnalysis: !!(competitor && competitor.analysis),
      ...data
    });
  }, [enableAnalytics, onAnalytics, variant, location.pathname, userPlan, isMobile, competitor]);

  // Enhanced error handler
  const handleError = useCallback((error, context) => {
    setAnalysisError(error.message);

    if (onError) {
      onError(error, context);
    }

    trackAnalytics(ANALYSIS_ANALYTICS_EVENTS.ANALYSIS_ERROR, {
      error: error.message,
      context
    });
  }, [onError, trackAnalytics]);

  // Enhanced analysis handler
  const handleAnalyze = useCallback(async () => {
    if (!onAnalyze) return;

    try {
      setIsLoading(true);
      setAnalysisError(null);

      await onAnalyze(competitor);

      trackAnalytics(ANALYSIS_ANALYTICS_EVENTS.ANALYSIS_RUN, {
        competitorId: competitor?.id,
        competitorName: competitor?.name
      });
    } catch (error) {
      handleError(error, 'analyze_competitor');
    } finally {
      setIsLoading(false);
    }
  }, [onAnalyze, competitor, trackAnalytics, handleError]);

  // Price tracking handlers
  const handleStartPriceTracking = useCallback(async (products) => {
    if (!enablePriceTracking || !onPriceTrackingStart) return;

    try {
      setPriceTrackingState(prev => ({ ...prev, loading: true, error: null }));

      await onPriceTrackingStart(competitor.id, products);

      setPriceTrackingState(prev => ({
        ...prev,
        isTracking: true,
        trackedProducts: products,
        loading: false,
        showPriceDialog: false
      }));

      trackAnalytics('price_tracking_started', {
        competitorId: competitor.id,
        productsCount: products.length
      });
    } catch (error) {
      setPriceTrackingState(prev => ({
        ...prev,
        loading: false,
        error: error.message
      }));
      handleError(error, 'start_price_tracking');
    }
  }, [enablePriceTracking, onPriceTrackingStart, competitor, trackAnalytics, handleError]);

  const handleStopPriceTracking = useCallback(async () => {
    if (!enablePriceTracking || !onPriceTrackingStop) return;

    try {
      setPriceTrackingState(prev => ({ ...prev, loading: true, error: null }));

      await onPriceTrackingStop(competitor.id);

      setPriceTrackingState(prev => ({
        ...prev,
        isTracking: false,
        trackedProducts: [],
        loading: false
      }));

      trackAnalytics('price_tracking_stopped', {
        competitorId: competitor.id
      });
    } catch (error) {
      setPriceTrackingState(prev => ({
        ...prev,
        loading: false,
        error: error.message
      }));
      handleError(error, 'stop_price_tracking');
    }
  }, [enablePriceTracking, onPriceTrackingStop, competitor, trackAnalytics, handleError]);

  // Enhanced handlers (simplified for production)

  // Effects
  useEffect(() => {
    setIsLoading(loading);
  }, [loading]);

  useEffect(() => {
    setAnalysisError(error);
  }, [error]);

  useEffect(() => {
    // Track analysis view
    trackAnalytics(ANALYSIS_ANALYTICS_EVENTS.ANALYSIS_VIEW, {
      competitorId: competitor?.id,
      hasAnalysis: !!(competitor && competitor.analysis)
    });
  }, [competitor, trackAnalytics]);

  // Check if analysis exists
  const hasAnalysis = competitor && competitor.analysis;

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  // Prepare data for content type distribution chart
  const prepareContentTypeData = () => {
    if (!hasAnalysis || !competitor.analysis.content_types_distribution) {
      return [];
    }

    return Object.entries(competitor.analysis.content_types_distribution).map(([type, value]) => ({
      name: type.charAt(0).toUpperCase() + type.slice(1),
      value: value * 100 // Convert to percentage
    }));
  };

  // Prepare data for top performing content chart
  const prepareTopPerformingData = () => {
    if (!hasAnalysis || !competitor.analysis.top_performing_content) {
      return [];
    }

    return competitor.analysis.top_performing_content.map(content => ({
      name: `${content.type} - ${content.topic}`,
      engagement: content.engagement_rate
    }));
  };

  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  if (!competitor) {
    return null;
  }

  if (!hasAnalysis) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <AnalyticsIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          No Analysis Available
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          This competitor hasn&apos;t been analyzed yet. Run an analysis to get insights into their social media strategy.
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AnalyticsIcon />}
          onClick={handleAnalyze}
          disabled={isLoading}
          aria-label={enableAccessibility ? 'Run competitor analysis' : undefined}
        >
          {isLoading ? 'Analyzing...' : 'Run Analysis'}
        </Button>
      </Box>
    );
  }

  return (
    <StyledAnalysisContainer
      ref={containerRef}
      variant={variant}
      isLoading={isLoading}
      hasError={!!analysisError}
      data-testid={testId}
      tabIndex={enableAccessibility ? 0 : -1}
      role={enableAccessibility ? "region" : undefined}
      aria-label={enableAccessibility ? `Competitor analysis for ${competitor?.name || 'competitor'}` : undefined}
      sx={{ width: '100%' }}
    >
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            Analysis Summary
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Generated: {formatDate(competitor.analysis.generated_at)}
          </Typography>
        </Box>

        <Typography variant="body1" paragraph>
          {competitor.analysis.content_strategy || 'No content strategy analysis available.'}
        </Typography>
      </Paper>

      {/* Price Tracking Section */}
      {enablePriceTracking && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
              <TrendingUpIcon sx={{ mr: 1, color: 'primary.main' }} />
              Price Tracking
            </Typography>
            <Box>
              {priceTrackingState.isTracking ? (
                <Button
                  variant="outlined"
                  color="error"
                  onClick={handleStopPriceTracking}
                  disabled={priceTrackingState.loading}
                  startIcon={priceTrackingState.loading ? <CircularProgress size={16} /> : <StopIcon />}
                >
                  Stop Tracking
                </Button>
              ) : (
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => setPriceTrackingState(prev => ({ ...prev, showPriceDialog: true }))}
                  disabled={priceTrackingState.loading}
                  startIcon={<MonitorIcon />}
                >
                  Start Price Tracking
                </Button>
              )}
            </Box>
          </Box>

          {priceTrackingState.isTracking && (
            <Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Tracking {priceTrackingState.trackedProducts.length} products
              </Typography>

              {priceTrackingState.alerts.length > 0 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Recent Price Alerts
                  </Typography>
                  {priceTrackingState.alerts.slice(0, 3).map((alert, index) => (
                    <Alert
                      key={index}
                      severity={alert.price_change < 0 ? "success" : "warning"}
                      sx={{ mb: 1 }}
                    >
                      <Typography variant="body2">
                        <strong>{alert.product_name}</strong>:
                        ${alert.old_price} → ${alert.new_price}
                        ({alert.price_change_percentage > 0 ? '+' : ''}{alert.price_change_percentage.toFixed(1)}%)
                      </Typography>
                    </Alert>
                  ))}
                </Box>
              )}
            </Box>
          )}

          {priceTrackingState.error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {priceTrackingState.error}
            </Alert>
          )}
        </Paper>
      )}

      <Grid container spacing={3}>
        {/* Content Types Distribution */}
        <Grid item xs={12} md={6}>
          <CustomCard variant="outlined" elevation={0}>
            <CustomCardContent>
              <Typography variant="h6" gutterBottom>
                Content Types Distribution
              </Typography>

              {competitor.analysis.content_types_distribution ? (
                <Box sx={{
                  height: {
                    xs: 200, // Mobile
                    sm: 250, // Tablet
                    md: 300  // Desktop
                  }
                }}
                aria-label="Content types distribution chart">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={prepareContentTypeData()}
                        cx="50%"
                        cy="50%"
                        labelLine={true}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {prepareContentTypeData().map((_, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => `${value.toFixed(1)}%`} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No content type distribution data available.
                </Typography>
              )}
            </CustomCardContent>
          </CustomCard>
        </Grid>

        {/* Top Performing Content */}
        <Grid item xs={12} md={6}>
          <CustomCard variant="outlined" elevation={0}>
            <CustomCardContent>
              <Typography variant="h6" gutterBottom>
                Top Performing Content
              </Typography>

              {competitor.analysis.top_performing_content && competitor.analysis.top_performing_content.length > 0 ? (
                <Box sx={{
                  height: {
                    xs: 200, // Mobile
                    sm: 250, // Tablet
                    md: 300  // Desktop
                  }
                }}
                aria-label="Top performing content chart">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={prepareTopPerformingData()}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis label={{ value: 'Engagement Rate (%)', angle: -90, position: 'insideLeft' }} />
                      <Tooltip formatter={(value) => `${value.toFixed(2)}%`} />
                      <Bar dataKey="engagement" fill="#8884d8" name="Engagement Rate">
                        {prepareTopPerformingData().map((_, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No top performing content data available.
                </Typography>
              )}
            </CustomCardContent>
          </CustomCard>
        </Grid>

        {/* Posting Routine */}
        <Grid item xs={12} md={6}>
          <CustomCard variant="outlined" elevation={0}>
            <CustomCardContent>
              <Typography variant="h6" gutterBottom>
                Posting Routine
              </Typography>

              {competitor.analysis.posting_routine ? (
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      Frequency
                    </Typography>
                    <Typography variant="body1">
                      {competitor.analysis.posting_routine.frequency || 'Not specified'}
                    </Typography>
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      Best Days
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                      {competitor.analysis.posting_routine.best_days && competitor.analysis.posting_routine.best_days.length > 0 ? (
                        competitor.analysis.posting_routine.best_days.map((day, index) => (
                          <Chip key={index} label={day} size="small" color="primary" />
                        ))
                      ) : (
                        <Typography variant="body1">Not specified</Typography>
                      )}
                    </Box>
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      Best Times
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                      {competitor.analysis.posting_routine.best_times && competitor.analysis.posting_routine.best_times.length > 0 ? (
                        competitor.analysis.posting_routine.best_times.map((time, index) => (
                          <Chip key={index} label={time} size="small" color="secondary" />
                        ))
                      ) : (
                        <Typography variant="body1">Not specified</Typography>
                      )}
                    </Box>
                  </Grid>
                </Grid>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No posting routine data available.
                </Typography>
              )}
            </CustomCardContent>
          </CustomCard>
        </Grid>

        {/* Engagement Metrics */}
        <Grid item xs={12} md={6}>
          <CustomCard variant="outlined" elevation={0}>
            <CustomCardContent>
              <Typography variant="h6" gutterBottom>
                Engagement Metrics
              </Typography>

              {competitor.analysis.engagement_metrics ? (
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      Average Likes
                    </Typography>
                    <Typography variant="h5">
                      {competitor.analysis.engagement_metrics.avg_likes?.toFixed(0) || 'N/A'}
                    </Typography>
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      Average Comments
                    </Typography>
                    <Typography variant="h5">
                      {competitor.analysis.engagement_metrics.avg_comments?.toFixed(0) || 'N/A'}
                    </Typography>
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      Average Engagement Rate
                    </Typography>
                    <Typography variant="h5">
                      {competitor.analysis.engagement_metrics.avg_engagement_rate ?
                        `${competitor.analysis.engagement_metrics.avg_engagement_rate.toFixed(2)}%` :
                        'N/A'}
                    </Typography>
                  </Grid>
                </Grid>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No engagement metrics data available.
                </Typography>
              )}
            </CustomCardContent>
          </CustomCard>
        </Grid>

        {/* SWOT Analysis */}
        <Grid item xs={12}>
          <CustomCard variant="outlined" elevation={0}>
            <CustomCardContent>
              <Typography variant="h6" gutterBottom>
                SWOT Analysis
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Paper variant="outlined" sx={{ height: '100%', p: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <TrendingUpIcon color="success" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1">
                        Strengths
                      </Typography>
                    </Box>

                    {competitor.analysis.strengths && competitor.analysis.strengths.length > 0 ? (
                      <ul>
                        {competitor.analysis.strengths.map((strength, index) => (
                          <li key={index}>
                            <Typography variant="body2">{strength}</Typography>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        No strengths identified.
                      </Typography>
                    )}
                  </Paper>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Paper variant="outlined" sx={{ height: '100%', p: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <TrendingDownIcon color="error" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1">
                        Weaknesses
                      </Typography>
                    </Box>

                    {competitor.analysis.weaknesses && competitor.analysis.weaknesses.length > 0 ? (
                      <ul>
                        {competitor.analysis.weaknesses.map((weakness, index) => (
                          <li key={index}>
                            <Typography variant="body2">{weakness}</Typography>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        No weaknesses identified.
                      </Typography>
                    )}
                  </Paper>
                </Grid>

                <Grid item xs={12}>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <LightbulbIcon color="warning" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1">
                        Opportunities
                      </Typography>
                    </Box>

                    {competitor.analysis.opportunities && competitor.analysis.opportunities.length > 0 ? (
                      <ul>
                        {competitor.analysis.opportunities.map((opportunity, index) => (
                          <li key={index}>
                            <Typography variant="body2">{opportunity}</Typography>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        No opportunities identified.
                      </Typography>
                    )}
                  </Paper>
                </Grid>
              </Grid>
            </CustomCardContent>
          </CustomCard>
        </Grid>
      </Grid>
    </StyledAnalysisContainer>
  );
});

// Set display name for debugging
CompetitorAnalysisView.displayName = 'CompetitorAnalysisView';

// Comprehensive PropTypes for enterprise-grade competitor analysis view
CompetitorAnalysisView.propTypes = {
  /** Competitor object with analysis data */
  competitor: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    name: PropTypes.string,
    platform: PropTypes.string,
    avatar: PropTypes.string,
    analysis: PropTypes.shape({
      generated_at: PropTypes.string,
      content_strategy: PropTypes.string,
      content_types_distribution: PropTypes.object,
      top_performing_content: PropTypes.arrayOf(
        PropTypes.shape({
          type: PropTypes.string,
          topic: PropTypes.string,
          engagement_rate: PropTypes.number
        })
      ),
      posting_routine: PropTypes.shape({
        frequency: PropTypes.string,
        best_days: PropTypes.arrayOf(PropTypes.string),
        best_times: PropTypes.arrayOf(PropTypes.string)
      }),
      engagement_metrics: PropTypes.shape({
        avg_likes: PropTypes.number,
        avg_comments: PropTypes.number,
        avg_engagement_rate: PropTypes.number
      }),
      strengths: PropTypes.arrayOf(PropTypes.string),
      weaknesses: PropTypes.arrayOf(PropTypes.string),
      opportunities: PropTypes.arrayOf(PropTypes.string)
    })
  }),

  /** Analysis trigger handler */
  onAnalyze: PropTypes.func,

  /** Analysis variant */
  variant: PropTypes.oneOf(Object.values(ANALYSIS_VARIANTS)),

  /** Whether to enable analytics tracking */
  enableAnalytics: PropTypes.bool,

  /** Whether to enable accessibility features */
  enableAccessibility: PropTypes.bool,

  /** Loading state */
  loading: PropTypes.bool,

  /** Error state */
  error: PropTypes.string,

  /** Analytics event handler */
  onAnalytics: PropTypes.func,

  /** Error handler */
  onError: PropTypes.func,

  /** User's subscription plan */
  userPlan: PropTypes.oneOf(['creator', 'accelerator', 'dominator']),

  /** Test ID for testing */
  testId: PropTypes.string,

  // Price tracking props
  /** Enable price tracking functionality */
  enablePriceTracking: PropTypes.bool,
  /** Handler for starting price tracking */
  onPriceTrackingStart: PropTypes.func,
  /** Handler for stopping price tracking */
  onPriceTrackingStop: PropTypes.func,
  /** Array of price alerts */
  priceAlerts: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string,
    product_name: PropTypes.string,
    old_price: PropTypes.number,
    new_price: PropTypes.number,
    price_change: PropTypes.number,
    price_change_percentage: PropTypes.number
  }))
};

CompetitorAnalysisView.defaultProps = {
  competitor: null,
  onAnalyze: null,
  variant: ANALYSIS_VARIANTS.STANDARD,
  enableAnalytics: true,
  enableAccessibility: true,
  loading: false,
  error: null,
  onAnalytics: null,
  onError: null,
  userPlan: 'creator',
  testId: null,
  enablePriceTracking: false,
  onPriceTrackingStart: null,
  onPriceTrackingStop: null,
  priceAlerts: []
};

export default CompetitorAnalysisView;
