@echo off
echo 🚀 Setting up VS Code for ACEO Platform...
echo.

echo 1. Testing Python virtual environment...
backend\venv\Scripts\python.exe -c "import fastapi, motor.motor_asyncio, pymongo.errors, tenacity, bson; print('✅ All Python packages verified')"

echo.
echo 2. Opening VS Code with workspace configuration...
code aceo-platform.code-workspace

echo.
echo ✅ Setup complete!
echo.
echo 📋 Next steps to fix Pylance import errors:
echo 1. In VS Code, press Ctrl+Shift+P
echo 2. Type "Python: Select Interpreter"
echo 3. Choose: .\backend\venv\Scripts\python.exe
echo 4. Reload the window (Ctrl+Shift+P -^> "Developer: Reload Window")
echo 5. Open any Python file in backend/ to verify imports work
echo.
echo 🔧 If imports still show errors:
echo - Check the status bar shows: Python 3.11.x ('venv': venv)
echo - Try: Ctrl+Shift+P -^> "Python: Refresh Language Server"
echo - Restart VS Code completely if needed
echo.
pause
