import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Alert,
  Switch,
  FormControlLabel,
  Chip,
  IconButton,
  Tooltip,
  Grid,
  Card,
  CardContent,
  Divider,
} from '@mui/material';
import {
  Close as CloseIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Info as InfoIcon,
  AttachMoney as MoneyIcon,
  Schedule as ScheduleIcon,
  Category as CategoryIcon,
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { validateFormData, formatCurrency, getTierConfig } from '../../utils/appsumoHelpers';
import api from '../../api';

/**
 * Package Creator Component
 * Comprehensive interface for creating AppSumo packages/deals with pricing configuration
 */
const PackageCreator = ({ 
  open, 
  onClose, 
  onPackageCreated,
  editingPackage = null,
  ...props 
}) => {
  const [formData, setFormData] = useState({
    deal_id: '',
    name: '',
    description: '',
    pricing: {
      regular_price: 0,
      appsumo_price: 59,
      discount_percentage: 0,
    },
    tiers: ['tier1'],
    start_date: new Date(),
    end_date: null,
    features: [],
    terms_and_conditions: '',
    redemption_url_pattern: '/appsumo/redeem/{code}',
    metadata: {
      max_stacking: 3,
      allow_upgrades: true,
      lifetime_access: true,
    },
    is_active: true,
  });
  
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [newFeature, setNewFeature] = useState('');

  // Available tier types
  const availableTiers = [
    { value: 'tier1', label: 'Single (1 User)', config: getTierConfig('tier1') },
    { value: 'tier2', label: 'Double (2 Users)', config: getTierConfig('tier2') },
    { value: 'tier3', label: 'Triple (3 Users)', config: getTierConfig('tier3') },
  ];

  // Form validation rules
  const validationRules = [
    { key: 'deal_id', label: 'Deal ID', type: 'string', minLength: 3, maxLength: 50 },
    { key: 'name', label: 'Package Name', type: 'string', minLength: 3, maxLength: 100 },
    { key: 'description', label: 'Description', type: 'string', minLength: 10, maxLength: 500 },
    { key: 'pricing.regular_price', label: 'Regular Price', type: 'number', min: 0 },
    { key: 'pricing.appsumo_price', label: 'AppSumo Price', type: 'number', min: 0 },
  ];

  // Initialize form data when editing
  React.useEffect(() => {
    if (editingPackage) {
      setFormData({
        ...editingPackage,
        start_date: new Date(editingPackage.start_date),
        end_date: editingPackage.end_date ? new Date(editingPackage.end_date) : null,
      });
    }
  }, [editingPackage]);

  // Calculate discount percentage
  React.useEffect(() => {
    const { regular_price, appsumo_price } = formData.pricing;
    if (regular_price > 0 && appsumo_price > 0) {
      const discount = ((regular_price - appsumo_price) / regular_price) * 100;
      setFormData(prev => ({
        ...prev,
        pricing: {
          ...prev.pricing,
          discount_percentage: Math.round(discount * 100) / 100,
        },
      }));
    }
  }, [formData.pricing.regular_price, formData.pricing.appsumo_price]);

  // Handle form field changes
  const handleFieldChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value,
        },
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  // Handle tier selection
  const handleTierToggle = (tierValue) => {
    setFormData(prev => ({
      ...prev,
      tiers: prev.tiers.includes(tierValue)
        ? prev.tiers.filter(t => t !== tierValue)
        : [...prev.tiers, tierValue],
    }));
  };

  // Handle feature management
  const handleAddFeature = () => {
    if (newFeature.trim()) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()],
      }));
      setNewFeature('');
    }
  };

  const handleRemoveFeature = (index) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index),
    }));
  };

  // Handle metadata changes
  const handleMetadataChange = (key, value) => {
    setFormData(prev => ({
      ...prev,
      metadata: {
        ...prev.metadata,
        [key]: value,
      },
    }));
  };

  // Validate form
  const validateForm = () => {
    const validation = validateFormData(formData, validationRules);
    
    // Additional validations
    if (formData.tiers.length === 0) {
      validation.errors.tiers = 'At least one tier must be selected';
      validation.isValid = false;
    }
    
    if (formData.features.length === 0) {
      validation.errors.features = 'At least one feature must be added';
      validation.isValid = false;
    }
    
    if (formData.end_date && formData.end_date <= formData.start_date) {
      validation.errors.end_date = 'End date must be after start date';
      validation.isValid = false;
    }
    
    setErrors(validation.errors);
    return validation.isValid;
  };

  // Handle save
  const handleSave = async () => {
    if (!validateForm()) return;
    
    setLoading(true);
    try {
      const packageData = {
        ...formData,
        start_date: formData.start_date.toISOString(),
        end_date: formData.end_date ? formData.end_date.toISOString() : null,
      };
      
      let response;
      if (editingPackage) {
        response = await api.put(`/api/appsumo/deals/${editingPackage.id}`, packageData);
      } else {
        response = await api.post('/api/appsumo/deals', packageData);
      }
      
      if (onPackageCreated) {
        onPackageCreated(response.data);
      }
      
      handleClose();
    } catch (error) {
      console.error('Error saving package:', error);
      setErrors({ 
        save: error.response?.data?.detail || 'Failed to save package' 
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle close
  const handleClose = () => {
    setFormData({
      deal_id: '',
      name: '',
      description: '',
      pricing: {
        regular_price: 0,
        appsumo_price: 59,
        discount_percentage: 0,
      },
      tiers: ['tier1'],
      start_date: new Date(),
      end_date: null,
      features: [],
      terms_and_conditions: '',
      redemption_url_pattern: '/appsumo/redeem/{code}',
      metadata: {
        max_stacking: 3,
        allow_upgrades: true,
        lifetime_access: true,
      },
      is_active: true,
    });
    setErrors({});
    setNewFeature('');
    onClose();
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="lg" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxHeight: '90vh',
        }
      }}
      {...props}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">
            {editingPackage ? 'Edit Package' : 'Create New Package'}
          </Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box display="flex" flexDirection="column" gap={3} mt={2}>
          {errors.save && (
            <Alert severity="error">{errors.save}</Alert>
          )}

          {/* Basic Information */}
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CategoryIcon />
                Basic Information
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <TextField
                    label="Deal ID"
                    value={formData.deal_id}
                    onChange={(e) => handleFieldChange('deal_id', e.target.value)}
                    error={!!errors.deal_id}
                    helperText={errors.deal_id || 'Unique identifier for this deal'}
                    fullWidth
                    required
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    label="Package Name"
                    value={formData.name}
                    onChange={(e) => handleFieldChange('name', e.target.value)}
                    error={!!errors.name}
                    helperText={errors.name}
                    fullWidth
                    required
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    label="Description"
                    value={formData.description}
                    onChange={(e) => handleFieldChange('description', e.target.value)}
                    error={!!errors.description}
                    helperText={errors.description}
                    fullWidth
                    multiline
                    rows={3}
                    required
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Pricing Configuration */}
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <MoneyIcon />
                Pricing Configuration
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <TextField
                    label="Regular Price"
                    type="number"
                    value={formData.pricing.regular_price}
                    onChange={(e) => handleFieldChange('pricing.regular_price', parseFloat(e.target.value) || 0)}
                    error={!!errors['pricing.regular_price']}
                    helperText={errors['pricing.regular_price']}
                    InputProps={{
                      startAdornment: '$',
                    }}
                    fullWidth
                    required
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    label="AppSumo Price"
                    type="number"
                    value={formData.pricing.appsumo_price}
                    onChange={(e) => handleFieldChange('pricing.appsumo_price', parseFloat(e.target.value) || 0)}
                    error={!!errors['pricing.appsumo_price']}
                    helperText={errors['pricing.appsumo_price']}
                    InputProps={{
                      startAdornment: '$',
                    }}
                    fullWidth
                    required
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    label="Discount"
                    value={`${formData.pricing.discount_percentage}%`}
                    InputProps={{
                      readOnly: true,
                    }}
                    fullWidth
                    helperText="Calculated automatically"
                  />
                </Grid>
              </Grid>
              
              {formData.pricing.discount_percentage > 0 && (
                <Alert severity="info" sx={{ mt: 2 }}>
                  Customers save {formatCurrency(formData.pricing.regular_price - formData.pricing.appsumo_price)} 
                  ({formData.pricing.discount_percentage}% discount)
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Tier Selection */}
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Available Tiers
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Select which tiers will be available for this package
              </Typography>
              
              <Box display="flex" flexWrap="wrap" gap={2} mt={2}>
                {availableTiers.map((tier) => (
                  <Card 
                    key={tier.value}
                    variant={formData.tiers.includes(tier.value) ? "outlined" : "elevation"}
                    sx={{ 
                      cursor: 'pointer',
                      border: formData.tiers.includes(tier.value) ? 2 : 1,
                      borderColor: formData.tiers.includes(tier.value) ? 'primary.main' : 'divider',
                    }}
                    onClick={() => handleTierToggle(tier.value)}
                  >
                    <CardContent sx={{ p: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        {tier.label}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {tier.config?.description}
                      </Typography>
                      <Box mt={1}>
                        <Chip 
                          label={`${tier.config?.maxUsers} Users`}
                          size="small"
                          color={formData.tiers.includes(tier.value) ? 'primary' : 'default'}
                        />
                      </Box>
                    </CardContent>
                  </Card>
                ))}
              </Box>
              
              {errors.tiers && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {errors.tiers}
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Features */}
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Package Features
              </Typography>
              
              <Box display="flex" gap={1} mb={2}>
                <TextField
                  label="Add Feature"
                  value={newFeature}
                  onChange={(e) => setNewFeature(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleAddFeature()}
                  fullWidth
                  size="small"
                />
                <Button
                  variant="outlined"
                  onClick={handleAddFeature}
                  disabled={!newFeature.trim()}
                  startIcon={<AddIcon />}
                >
                  Add
                </Button>
              </Box>
              
              <Box display="flex" flexWrap="wrap" gap={1}>
                {formData.features.map((feature, index) => (
                  <Chip
                    key={index}
                    label={feature}
                    onDelete={() => handleRemoveFeature(index)}
                    deleteIcon={<RemoveIcon />}
                    variant="outlined"
                  />
                ))}
              </Box>
              
              {errors.features && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {errors.features}
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Schedule */}
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <ScheduleIcon />
                Schedule
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <DateTimePicker
                    label="Start Date"
                    value={formData.start_date}
                    onChange={(date) => handleFieldChange('start_date', date)}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        required: true,
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <DateTimePicker
                    label="End Date (Optional)"
                    value={formData.end_date}
                    onChange={(date) => handleFieldChange('end_date', date)}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                      },
                    }}
                  />
                </Grid>
              </Grid>
              
              {errors.end_date && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {errors.end_date}
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Advanced Settings */}
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Advanced Settings
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <TextField
                    label="Max Stacking"
                    type="number"
                    value={formData.metadata.max_stacking}
                    onChange={(e) => handleMetadataChange('max_stacking', parseInt(e.target.value) || 1)}
                    helperText="Maximum number of codes a user can stack"
                    fullWidth
                    inputProps={{ min: 1, max: 10 }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Box mt={2}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={formData.metadata.allow_upgrades}
                          onChange={(e) => handleMetadataChange('allow_upgrades', e.target.checked)}
                        />
                      }
                      label="Allow Tier Upgrades"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={formData.metadata.lifetime_access}
                          onChange={(e) => handleMetadataChange('lifetime_access', e.target.checked)}
                        />
                      }
                      label="Lifetime Access"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={formData.is_active}
                          onChange={(e) => handleFieldChange('is_active', e.target.checked)}
                        />
                      }
                      label="Active Package"
                    />
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        <Button 
          onClick={handleSave} 
          variant="contained"
          disabled={loading}
        >
          {loading ? 'Saving...' : (editingPackage ? 'Update Package' : 'Create Package')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PackageCreator;
