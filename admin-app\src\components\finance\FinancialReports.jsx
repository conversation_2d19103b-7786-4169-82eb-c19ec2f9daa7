import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Alert,
  CircularProgress,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
} from '@mui/material';
import {
  Download as DownloadIcon,
  Assessment as ReportIcon,
  Schedule as ScheduleIcon,
  Email as EmailIcon,
  PictureAsPdf as PdfIcon,
  TableChart as CsvIcon,
  InsertDriveFile as ExcelIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { format, subMonths, startOfMonth, endOfMonth } from 'date-fns';

const FinancialReports = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // Report configuration
  const [reportConfig, setReportConfig] = useState({
    type: 'revenue_summary',
    period_start: startOfMonth(subMonths(new Date(), 1)),
    period_end: endOfMonth(subMonths(new Date(), 1)),
    format: 'excel',
    include_plan_breakdown: true,
    include_customer_segments: false,
    include_forecasts: false,
    email_delivery: false,
    schedule_frequency: 'none',
  });

  const reportTypes = [
    {
      value: 'revenue_summary',
      label: 'Revenue Summary',
      description: 'Comprehensive revenue breakdown and metrics',
      icon: <ReportIcon />,
    },
    {
      value: 'churn_analysis',
      label: 'Churn Analysis',
      description: 'Customer churn rates and revenue impact',
      icon: <ReportIcon />,
    },
    {
      value: 'ltv_analysis',
      label: 'Customer LTV Analysis',
      description: 'Customer lifetime value calculations and trends',
      icon: <ReportIcon />,
    },
    {
      value: 'payment_failures',
      label: 'Payment Failures Report',
      description: 'Failed payments and retry analysis',
      icon: <ReportIcon />,
    },
    {
      value: 'financial_forecast',
      label: 'Financial Forecast',
      description: 'Revenue predictions and growth projections',
      icon: <ReportIcon />,
    },
  ];

  const exportFormats = [
    { value: 'excel', label: 'Excel (.xlsx)', icon: <ExcelIcon /> },
    { value: 'csv', label: 'CSV (.csv)', icon: <CsvIcon /> },
    { value: 'pdf', label: 'PDF (.pdf)', icon: <PdfIcon /> },
    { value: 'json', label: 'JSON (.json)', icon: <ReportIcon /> },
  ];

  const scheduleOptions = [
    { value: 'none', label: 'One-time Export' },
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'monthly', label: 'Monthly' },
    { value: 'quarterly', label: 'Quarterly' },
  ];

  const handleConfigChange = (field, value) => {
    setReportConfig(prev => ({ ...prev, [field]: value }));
  };

  const handleGenerateReport = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      // TODO: Implement actual report generation API call
      // const response = await api.post('/api/admin/finance/reports/generate', reportConfig);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setSuccess(`${reportTypes.find(t => t.value === reportConfig.type)?.label} report generated successfully!`);
      
      // TODO: Handle file download or email delivery based on configuration
      
    } catch (err) {
      console.error('Error generating report:', err);
      setError(err.response?.data?.detail || 'Failed to generate report');
    } finally {
      setLoading(false);
    }
  };

  const setQuickDateRange = (months) => {
    const end = endOfMonth(new Date());
    const start = startOfMonth(subMonths(end, months - 1));
    handleConfigChange('period_start', start);
    handleConfigChange('period_end', end);
  };

  const selectedReportType = reportTypes.find(t => t.value === reportConfig.type);

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Report Configuration */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardHeader title="Report Configuration" />
            <CardContent>
              <Grid container spacing={3}>
                {/* Report Type */}
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Report Type</InputLabel>
                    <Select
                      value={reportConfig.type}
                      label="Report Type"
                      onChange={(e) => handleConfigChange('type', e.target.value)}
                    >
                      {reportTypes.map((type) => (
                        <MenuItem key={type.value} value={type.value}>
                          <Box display="flex" alignItems="center" gap={1}>
                            {type.icon}
                            <Box>
                              <Typography variant="body2" fontWeight="bold">
                                {type.label}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {type.description}
                              </Typography>
                            </Box>
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {/* Date Range */}
                <Grid item xs={12} sm={6}>
                  <DatePicker
                    label="Start Date"
                    value={reportConfig.period_start}
                    onChange={(value) => handleConfigChange('period_start', value)}
                    slotProps={{ textField: { fullWidth: true } }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <DatePicker
                    label="End Date"
                    value={reportConfig.period_end}
                    onChange={(value) => handleConfigChange('period_end', value)}
                    slotProps={{ textField: { fullWidth: true } }}
                  />
                </Grid>

                {/* Quick Date Range Buttons */}
                <Grid item xs={12}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Quick Date Ranges:
                  </Typography>
                  <Box display="flex" gap={1} flexWrap="wrap">
                    <Button size="small" onClick={() => setQuickDateRange(1)}>
                      Last Month
                    </Button>
                    <Button size="small" onClick={() => setQuickDateRange(3)}>
                      Last 3 Months
                    </Button>
                    <Button size="small" onClick={() => setQuickDateRange(6)}>
                      Last 6 Months
                    </Button>
                    <Button size="small" onClick={() => setQuickDateRange(12)}>
                      Last Year
                    </Button>
                  </Box>
                </Grid>

                {/* Export Format */}
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Export Format</InputLabel>
                    <Select
                      value={reportConfig.format}
                      label="Export Format"
                      onChange={(e) => handleConfigChange('format', e.target.value)}
                    >
                      {exportFormats.map((format) => (
                        <MenuItem key={format.value} value={format.value}>
                          <Box display="flex" alignItems="center" gap={1}>
                            {format.icon}
                            {format.label}
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {/* Schedule */}
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Schedule</InputLabel>
                    <Select
                      value={reportConfig.schedule_frequency}
                      label="Schedule"
                      onChange={(e) => handleConfigChange('schedule_frequency', e.target.value)}
                    >
                      {scheduleOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {/* Report Options */}
                <Grid item xs={12}>
                  <Typography variant="body2" fontWeight="bold" gutterBottom>
                    Report Options:
                  </Typography>
                  <FormGroup>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={reportConfig.include_plan_breakdown}
                          onChange={(e) => handleConfigChange('include_plan_breakdown', e.target.checked)}
                        />
                      }
                      label="Include plan breakdown"
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={reportConfig.include_customer_segments}
                          onChange={(e) => handleConfigChange('include_customer_segments', e.target.checked)}
                        />
                      }
                      label="Include customer segments"
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={reportConfig.include_forecasts}
                          onChange={(e) => handleConfigChange('include_forecasts', e.target.checked)}
                        />
                      }
                      label="Include forecasts and predictions"
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={reportConfig.email_delivery}
                          onChange={(e) => handleConfigChange('email_delivery', e.target.checked)}
                        />
                      }
                      label="Email delivery"
                    />
                  </FormGroup>
                </Grid>

                {/* Generate Button */}
                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    size="large"
                    startIcon={loading ? <CircularProgress size={20} /> : <DownloadIcon />}
                    onClick={handleGenerateReport}
                    disabled={loading}
                    fullWidth
                  >
                    {loading ? 'Generating Report...' : 'Generate Report'}
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Report Preview & Info */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader title="Report Preview" />
            <CardContent>
              {selectedReportType && (
                <Box>
                  <Box display="flex" alignItems="center" gap={1} mb={2}>
                    {selectedReportType.icon}
                    <Typography variant="h6">
                      {selectedReportType.label}
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {selectedReportType.description}
                  </Typography>

                  <Divider sx={{ my: 2 }} />

                  <Typography variant="body2" fontWeight="bold" gutterBottom>
                    Report Details:
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText
                        primary="Period"
                        secondary={`${format(reportConfig.period_start, 'MMM dd, yyyy')} - ${format(reportConfig.period_end, 'MMM dd, yyyy')}`}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Format"
                        secondary={exportFormats.find(f => f.value === reportConfig.format)?.label}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Schedule"
                        secondary={scheduleOptions.find(s => s.value === reportConfig.schedule_frequency)?.label}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Delivery"
                        secondary={reportConfig.email_delivery ? "Email + Download" : "Download Only"}
                      />
                    </ListItem>
                  </List>

                  <Divider sx={{ my: 2 }} />

                  <Typography variant="body2" fontWeight="bold" gutterBottom>
                    Included Sections:
                  </Typography>
                  <Box display="flex" flexDirection="column" gap={0.5}>
                    <Chip label="Core Metrics" size="small" color="primary" />
                    {reportConfig.include_plan_breakdown && (
                      <Chip label="Plan Breakdown" size="small" color="secondary" />
                    )}
                    {reportConfig.include_customer_segments && (
                      <Chip label="Customer Segments" size="small" color="secondary" />
                    )}
                    {reportConfig.include_forecasts && (
                      <Chip label="Forecasts" size="small" color="secondary" />
                    )}
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>

          {/* Recent Reports */}
          <Card sx={{ mt: 3 }}>
            <CardHeader title="Recent Reports" />
            <CardContent>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <ReportIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Revenue Summary - December 2024"
                    secondary="Generated 2 hours ago"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <ReportIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Churn Analysis - Q4 2024"
                    secondary="Generated yesterday"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <ScheduleIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Monthly Revenue Report"
                    secondary="Scheduled for tomorrow"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default FinancialReports;
