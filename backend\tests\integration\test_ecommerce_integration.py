"""
Integration tests for e-commerce features with ACE Social platform.
Tests the complete workflow from product scheduling to behavioral analysis.
"""

import pytest
import asyncio
from datetime import datetime, timezone
from unittest.mock import AsyncMock, patch

from app.services.competitor_service import CompetitorService
from app.services.sentiment_analysis import SentimentAnalysisService
from app.services.ecommerce_icp_generator import ecommerce_icp_generator


class TestEcommerceIntegration:
    """Integration tests for e-commerce features."""
    
    @pytest.fixture
    def sample_user_id(self):
        return "60d5ec9af682dbd12a0a9fb7"
    
    @pytest.fixture
    def sample_store_id(self):
        return "store_123"
    
    @pytest.fixture
    def sample_competitor_id(self):
        return "competitor_456"
    
    @pytest.fixture
    def sample_product_data(self):
        return {
            "id": "product_789",
            "name": "Premium Wireless Headphones",
            "price": 199.99,
            "category": "Electronics",
            "description": "High-quality wireless headphones with noise cancellation"
        }
    
    @pytest.fixture
    def sample_reviews(self):
        return [
            {
                "id": "review_1",
                "text": "Amazing sound quality! Love these headphones.",
                "rating": 5,
                "date": "2024-01-15T10:00:00Z"
            },
            {
                "id": "review_2", 
                "text": "Good headphones but a bit expensive for what you get.",
                "rating": 3,
                "date": "2024-01-10T14:30:00Z"
            },
            {
                "id": "review_3",
                "text": "Terrible battery life, very disappointed.",
                "rating": 2,
                "date": "2024-01-08T09:15:00Z"
            }
        ]
    
    @pytest.fixture
    def sample_behavioral_data(self):
        return {
            "purchase_history": [
                {
                    "id": "purchase_1",
                    "amount": 199.99,
                    "date": "2024-01-15T10:00:00Z",
                    "categories": ["Electronics", "Audio"]
                },
                {
                    "id": "purchase_2",
                    "amount": 89.99,
                    "date": "2023-12-20T15:30:00Z",
                    "categories": ["Electronics", "Accessories"]
                }
            ],
            "browsing_patterns": {
                "page_views": [
                    {
                        "page": "/products/headphones",
                        "category": "Electronics",
                        "timestamp": "2024-01-14T20:00:00Z",
                        "device": "mobile"
                    },
                    {
                        "page": "/products/speakers",
                        "category": "Electronics", 
                        "timestamp": "2024-01-14T20:05:00Z",
                        "device": "mobile"
                    }
                ],
                "average_session_duration": 180,
                "bounce_rate": 0.3
            },
            "engagement_data": {
                "email_opens": 5,
                "email_clicks": 2,
                "social_media_interactions": 3
            }
        }

    @pytest.mark.asyncio
    async def test_competitor_price_tracking_integration(
        self, 
        sample_user_id, 
        sample_competitor_id,
        sample_product_data
    ):
        """Test competitor price tracking integration."""
        competitor_service = CompetitorService()
        
        # Mock database operations
        with patch('app.db.mongodb.get_database') as mock_db, \
             patch('app.core.redis.get_redis_client') as mock_redis:
            
            mock_db_instance = AsyncMock()
            mock_db.return_value = mock_db_instance
            mock_redis.return_value = AsyncMock()
            
            # Mock competitor exists
            mock_competitor = AsyncMock()
            mock_competitor.user_id = sample_user_id
            
            with patch.object(competitor_service, 'get_competitor', return_value=mock_competitor):
                # Test price tracking setup
                products_to_track = [
                    {
                        "id": sample_product_data["id"],
                        "name": sample_product_data["name"],
                        "url": f"https://competitor.com/product/{sample_product_data['id']}",
                        "current_price": sample_product_data["price"]
                    }
                ]
                
                result = await competitor_service.track_competitor_prices(
                    sample_competitor_id,
                    sample_user_id,
                    products_to_track
                )
                
                # Verify tracking was set up
                assert result["status"] == "success"
                assert result["products_tracked"] == 1
                assert "tracking_id" in result
                
                # Verify database insertion was called
                mock_db_instance.competitor_price_tracking.insert_one.assert_called_once()

    @pytest.mark.asyncio
    async def test_product_review_sentiment_analysis(
        self,
        sample_user_id,
        sample_product_data,
        sample_reviews
    ):
        """Test product review sentiment analysis integration."""
        sentiment_service = SentimentAnalysisService()
        
        # Mock database operations
        with patch('app.db.mongodb.get_database') as mock_db:
            mock_db_instance = AsyncMock()
            mock_db.return_value = mock_db_instance
            
            # Test review analysis
            result = await sentiment_service.analyze_product_reviews(
                sample_user_id,
                sample_product_data["id"],
                sample_reviews,
                "accelerator"
            )
            
            # Verify analysis results
            assert result["product_id"] == sample_product_data["id"]
            assert result["total_reviews"] == 3
            assert "overall_sentiment" in result
            assert "sentiment_distribution" in result
            assert "key_themes" in result
            assert "content_suggestions" in result
            
            # Verify sentiment distribution
            sentiment_dist = result["sentiment_distribution"]
            assert "positive" in sentiment_dist
            assert "neutral" in sentiment_dist
            assert "negative" in sentiment_dist
            
            # Verify database storage
            mock_db_instance.product_review_analysis.insert_one.assert_called_once()

    @pytest.mark.asyncio
    async def test_behavioral_segmentation_analysis(
        self,
        sample_user_id,
        sample_store_id,
        sample_behavioral_data
    ):
        """Test behavioral segmentation analysis integration."""
        
        # Mock database operations
        with patch('app.db.mongodb.get_database') as mock_db:
            mock_db_instance = AsyncMock()
            mock_db.return_value = mock_db_instance
            
            # Test behavioral segmentation
            result = await ecommerce_icp_generator.generate_behavioral_segments(
                sample_user_id,
                sample_store_id,
                sample_behavioral_data,
                "dominator"
            )
            
            # Verify segmentation results
            assert result["user_id"] == sample_user_id
            assert result["store_id"] == sample_store_id
            assert "segments" in result
            assert "behavioral_insights" in result
            assert "targeting_recommendations" in result
            assert "purchase_analysis" in result
            assert "browsing_analysis" in result
            
            # Verify purchase analysis
            purchase_analysis = result["purchase_analysis"]
            assert purchase_analysis["total_purchases"] == 2
            assert purchase_analysis["purchase_frequency"] == "medium"
            assert "category_preferences" in purchase_analysis
            
            # Verify browsing analysis
            browsing_analysis = result["browsing_analysis"]
            assert browsing_analysis["total_page_views"] == 2
            assert browsing_analysis["behavior_type"] in ["engaged_browser", "active_researcher", "casual_browser", "quick_visitor"]
            
            # Verify segments were generated
            assert len(result["segments"]) > 0
            for segment in result["segments"]:
                assert "name" in segment
                assert "description" in segment
                assert "characteristics" in segment
                assert "targeting_priority" in segment
            
            # Verify database storage
            mock_db_instance.behavioral_segmentation.insert_one.assert_called_once()

    @pytest.mark.asyncio
    async def test_end_to_end_ecommerce_workflow(
        self,
        sample_user_id,
        sample_store_id,
        sample_competitor_id,
        sample_product_data,
        sample_reviews,
        sample_behavioral_data
    ):
        """Test complete e-commerce workflow integration."""
        
        # Mock all database operations
        with patch('app.db.mongodb.get_database') as mock_db, \
             patch('app.core.redis.get_redis_client') as mock_redis:
            
            mock_db_instance = AsyncMock()
            mock_db.return_value = mock_db_instance
            mock_redis.return_value = AsyncMock()
            
            # Step 1: Set up competitor price tracking
            competitor_service = CompetitorService()
            mock_competitor = AsyncMock()
            mock_competitor.user_id = sample_user_id
            
            with patch.object(competitor_service, 'get_competitor', return_value=mock_competitor):
                price_tracking_result = await competitor_service.track_competitor_prices(
                    sample_competitor_id,
                    sample_user_id,
                    [{
                        "id": sample_product_data["id"],
                        "name": sample_product_data["name"],
                        "url": f"https://competitor.com/product/{sample_product_data['id']}",
                        "current_price": sample_product_data["price"]
                    }]
                )
                
                assert price_tracking_result["status"] == "success"
            
            # Step 2: Analyze product reviews
            sentiment_service = SentimentAnalysisService()
            review_analysis_result = await sentiment_service.analyze_product_reviews(
                sample_user_id,
                sample_product_data["id"],
                sample_reviews,
                "dominator"
            )
            
            assert review_analysis_result["total_reviews"] == 3
            assert "content_suggestions" in review_analysis_result
            
            # Step 3: Generate behavioral segments
            segmentation_result = await ecommerce_icp_generator.generate_behavioral_segments(
                sample_user_id,
                sample_store_id,
                sample_behavioral_data,
                "dominator"
            )
            
            assert len(segmentation_result["segments"]) > 0
            assert len(segmentation_result["targeting_recommendations"]) > 0
            
            # Step 4: Verify data quality and integration
            assert segmentation_result["data_quality_score"] > 0.0
            assert segmentation_result["subscription_tier"] == "dominator"
            
            # Verify all database operations were called
            assert mock_db_instance.competitor_price_tracking.insert_one.call_count >= 1
            assert mock_db_instance.product_review_analysis.insert_one.call_count >= 1
            assert mock_db_instance.behavioral_segmentation.insert_one.call_count >= 1

    @pytest.mark.asyncio
    async def test_subscription_tier_feature_gating(
        self,
        sample_user_id,
        sample_store_id,
        sample_behavioral_data
    ):
        """Test that features are properly gated by subscription tier."""
        
        with patch('app.db.mongodb.get_database') as mock_db:
            mock_db_instance = AsyncMock()
            mock_db.return_value = mock_db_instance
            
            # Test creator tier (basic features)
            creator_result = await ecommerce_icp_generator.generate_behavioral_segments(
                sample_user_id,
                sample_store_id,
                sample_behavioral_data,
                "creator"
            )
            
            # Creator should have basic analysis
            creator_purchase = creator_result["purchase_analysis"]
            assert "seasonal_patterns" in creator_purchase
            assert not creator_purchase["seasonal_patterns"]  # Should be empty for creator
            
            # Test dominator tier (advanced features)
            dominator_result = await ecommerce_icp_generator.generate_behavioral_segments(
                sample_user_id,
                sample_store_id,
                sample_behavioral_data,
                "dominator"
            )
            
            # Dominator should have advanced analysis
            dominator_purchase = dominator_result["purchase_analysis"]
            dominator_browsing = dominator_result["browsing_analysis"]
            
            # Should have seasonal patterns and device preferences
            assert "seasonal_patterns" in dominator_purchase
            assert "time_patterns" in dominator_browsing
            assert "device_preferences" in dominator_browsing

    def test_data_quality_scoring(self):
        """Test data quality scoring functionality."""
        
        # Test with complete data
        complete_data = {
            "purchase_history": [{"amount": 100, "date": "2024-01-01"}] * 5,
            "browsing_patterns": {
                "page_views": [{"page": "/test"}] * 10,
                "average_session_duration": 300
            },
            "engagement_data": {"email_opens": 5}
        }
        
        score = ecommerce_icp_generator._calculate_data_quality_score(complete_data)
        assert score == 1.0  # Perfect score
        
        # Test with minimal data
        minimal_data = {
            "purchase_history": [],
            "browsing_patterns": {},
            "engagement_data": {}
        }
        
        score = ecommerce_icp_generator._calculate_data_quality_score(minimal_data)
        assert score == 0.0  # No data score
