import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  IconButton,
  Tooltip,
  Alert,
  CircularProgress,
  Avatar,
  LinearProgress,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Person as PersonIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Add as AddIcon,
  Star as StarIcon,
  Schedule as ScheduleIcon,
  Assignment as AssignmentIcon,
  CheckCircle as OnlineIcon,
  Cancel as OfflineIcon,
} from '@mui/icons-material';

const AgentManagement = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Mock data for demonstration
  const agents = [
    {
      id: '1',
      agent_id: 'AGT-001',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'senior_agent',
      department: 'Technical Support',
      specializations: ['technical', 'billing'],
      is_active: true,
      is_online: true,
      current_ticket_count: 8,
      max_concurrent_tickets: 12,
      average_resolution_time_hours: 4.2,
      customer_satisfaction_score: 4.6,
      total_tickets_handled: 1250,
    },
    {
      id: '2',
      agent_id: 'AGT-002',
      name: '<PERSON> <PERSON>',
      email: '<EMAIL>',
      role: 'agent',
      department: 'General Support',
      specializations: ['general', 'account'],
      is_active: true,
      is_online: false,
      current_ticket_count: 5,
      max_concurrent_tickets: 10,
      average_resolution_time_hours: 6.1,
      customer_satisfaction_score: 4.3,
      total_tickets_handled: 890,
    },
    {
      id: '3',
      agent_id: 'AGT-003',
      name: 'Emily Rodriguez',
      email: '<EMAIL>',
      role: 'supervisor',
      department: 'Technical Support',
      specializations: ['technical', 'integration', 'training'],
      is_active: true,
      is_online: true,
      current_ticket_count: 3,
      max_concurrent_tickets: 8,
      average_resolution_time_hours: 3.8,
      customer_satisfaction_score: 4.8,
      total_tickets_handled: 2100,
    },
  ];

  const getRoleColor = (role) => {
    switch (role) {
      case 'manager':
        return 'error';
      case 'supervisor':
        return 'warning';
      case 'senior_agent':
        return 'info';
      case 'agent':
        return 'primary';
      default:
        return 'default';
    }
  };

  const getUtilizationColor = (current, max) => {
    const utilization = current / max;
    if (utilization >= 0.9) return 'error';
    if (utilization >= 0.7) return 'warning';
    return 'success';
  };

  const formatPercentage = (value) => {
    return `${(value * 100).toFixed(0)}%`;
  };

  const formatHours = (hours) => {
    if (hours < 1) {
      return `${Math.round(hours * 60)}m`;
    }
    return `${hours.toFixed(1)}h`;
  };

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Agent Overview Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Total Agents
                  </Typography>
                  <Typography variant="h5" component="div">
                    {agents.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    All departments
                  </Typography>
                </Box>
                <PersonIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Online Agents
                  </Typography>
                  <Typography variant="h5" component="div">
                    {agents.filter(a => a.is_online).length}
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    Available now
                  </Typography>
                </Box>
                <OnlineIcon color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Average CSAT
                  </Typography>
                  <Typography variant="h5" component="div">
                    {(agents.reduce((sum, a) => sum + a.customer_satisfaction_score, 0) / agents.length).toFixed(1)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Out of 5.0
                  </Typography>
                </Box>
                <StarIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Active Tickets
                  </Typography>
                  <Typography variant="h5" component="div">
                    {agents.reduce((sum, a) => sum + a.current_ticket_count, 0)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Currently assigned
                  </Typography>
                </Box>
                <AssignmentIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Agents Table */}
      <Card>
        <CardHeader 
          title="Support Agents"
          action={
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => {/* TODO: Implement add agent */}}
            >
              Add Agent
            </Button>
          }
        />
        <CardContent sx={{ p: 0 }}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Agent</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell>Department</TableCell>
                  <TableCell>Specializations</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Workload</TableCell>
                  <TableCell>Performance</TableCell>
                  <TableCell>CSAT</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={9} align="center" sx={{ py: 4 }}>
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : (
                  agents.map((agent) => (
                    <TableRow key={agent.id} hover>
                      <TableCell>
                        <Box display="flex" alignItems="center" gap={2}>
                          <Avatar sx={{ bgcolor: agent.is_online ? 'success.main' : 'grey.400' }}>
                            {agent.name.split(' ').map(n => n[0]).join('')}
                          </Avatar>
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              {agent.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {agent.agent_id} • {agent.email}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={agent.role.replace('_', ' ')}
                          color={getRoleColor(agent.role)}
                          size="small"
                          sx={{ textTransform: 'capitalize' }}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {agent.department}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" flexWrap="wrap" gap={0.5}>
                          {agent.specializations.map((spec) => (
                            <Chip
                              key={spec}
                              label={spec.replace('_', ' ')}
                              size="small"
                              variant="outlined"
                              sx={{ textTransform: 'capitalize' }}
                            />
                          ))}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" alignItems="center" gap={1}>
                          {agent.is_online ? (
                            <OnlineIcon color="success" fontSize="small" />
                          ) : (
                            <OfflineIcon color="action" fontSize="small" />
                          )}
                          <Typography variant="body2">
                            {agent.is_online ? 'Online' : 'Offline'}
                          </Typography>
                        </Box>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={agent.is_active}
                              size="small"
                            />
                          }
                          label="Active"
                          sx={{ mt: 0.5 }}
                        />
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" gutterBottom>
                            {agent.current_ticket_count}/{agent.max_concurrent_tickets} tickets
                          </Typography>
                          <LinearProgress
                            variant="determinate"
                            value={(agent.current_ticket_count / agent.max_concurrent_tickets) * 100}
                            color={getUtilizationColor(agent.current_ticket_count, agent.max_concurrent_tickets)}
                            sx={{ height: 6, borderRadius: 3 }}
                          />
                          <Typography variant="caption" color="text.secondary">
                            {formatPercentage(agent.current_ticket_count / agent.max_concurrent_tickets)} utilization
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">
                            Avg Resolution: {formatHours(agent.average_resolution_time_hours)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {agent.total_tickets_handled.toLocaleString()} total tickets
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" alignItems="center" gap={0.5}>
                          <StarIcon fontSize="small" color="warning" />
                          <Typography variant="body2" fontWeight="bold">
                            {agent.customer_satisfaction_score.toFixed(1)}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell align="center">
                        <Box display="flex" gap={0.5}>
                          <Tooltip title="View Profile">
                            <IconButton size="small">
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Edit Agent">
                            <IconButton size="small">
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Agent Performance Summary */}
      <Grid container spacing={3} sx={{ mt: 3 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Top Performers" />
            <CardContent>
              {agents
                .sort((a, b) => b.customer_satisfaction_score - a.customer_satisfaction_score)
                .slice(0, 3)
                .map((agent, index) => (
                  <Box key={agent.id} display="flex" alignItems="center" gap={2} mb={2}>
                    <Typography variant="h6" color="primary">
                      #{index + 1}
                    </Typography>
                    <Avatar sx={{ bgcolor: 'success.main' }}>
                      {agent.name.split(' ').map(n => n[0]).join('')}
                    </Avatar>
                    <Box flex={1}>
                      <Typography variant="body2" fontWeight="bold">
                        {agent.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        CSAT: {agent.customer_satisfaction_score.toFixed(1)} • 
                        Avg Resolution: {formatHours(agent.average_resolution_time_hours)}
                      </Typography>
                    </Box>
                  </Box>
                ))}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Department Distribution" />
            <CardContent>
              {Object.entries(
                agents.reduce((acc, agent) => {
                  acc[agent.department] = (acc[agent.department] || 0) + 1;
                  return acc;
                }, {})
              ).map(([department, count]) => (
                <Box key={department} mb={2}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={0.5}>
                    <Typography variant="body2">{department}</Typography>
                    <Typography variant="body2" fontWeight="bold">
                      {count} agents
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={(count / agents.length) * 100}
                    sx={{ height: 6, borderRadius: 3 }}
                  />
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AgentManagement;
