from typing import List, Dict, Any, Tu<PERSON>, Optional, Mapping
from datetime import datetime, timezone
from bson import ObjectId
import json
import re
import logging

from app.utils.openai_client import generate_content, generate_image
from app.utils.platform_limits import get_headline_recommendations, truncate_text_for_platform
from app.db.repositories.brand_guidelines import get_brand_guideline_documents_by_user, get_auto_response_settings_by_user
from app.models.brand_guidelines import BrandGuidelineDocument, AutoResponseSettings
from app.schemas.content import (
    GenerateContentRequest,
    GenerateImageRequest,
    BulkContentGenerateRequest,
    ContentResponse,
    ContentUpdate,
    ImageMetadataResponse,
    ContentAnalyticsResponse,
    BrandingData
)

# Set up logging
logger = logging.getLogger(__name__)
from app.models.content import Content, ImageMetadata
from app.models.user import PyObjectId
from app.db.repositories.content import create_content_in_db, update_content_by_id
from app.services.url_shortener import detect_and_process_urls
from app.services.user import get_user_by_id

async def generate_content_service(request: GenerateContentRequest, user_id: ObjectId) -> ContentResponse:
    """
    Generate content using OpenAI and save it to the database.
    First generates text content, then generates a headline for image overlay if requested,
    and finally generates an image based on the text content.
    """
    # Get policy documents for the user if policy compliance is enabled
    policy_documents = None
    use_policy_compliance = True

    # Check user's policy compliance preference
    auto_response_settings = await get_auto_response_settings_by_user(str(user_id))
    if auto_response_settings:
        use_policy_compliance = auto_response_settings.use_policy_compliance

    # Only get policy documents if compliance is enabled
    if use_policy_compliance:
        policy_documents = await get_brand_guideline_documents_by_user(str(user_id))

    # Create prompt for content generation
    prompt = _create_content_prompt(request, policy_documents, use_policy_compliance)

    # Generate content and ensure it respects platform limitations
    text_content = await generate_content(prompt)
    text_content = truncate_text_for_platform(text_content, request.platform)

    # Process URLs in the content - detect, shorten, and add tracking
    if request.process_urls:
        text_content, shortened_urls = await detect_and_process_urls(
            content=text_content,
            user_id=str(user_id),
            content_id="temp_id",  # Will be updated after content creation
            platform=request.platform
        )

    # Generate headline for image overlay if requested
    headline = None
    if request.include_headline_on_image:
        headline = await _generate_headline(request.topic, request.platform, text_content)

    # Generate image if requested - AFTER text content is generated to ensure synchronization
    images = []
    if request.generate_image:
        # Use product data for enhanced image generation if available
        if request.product_data and request.include_product_details:
            image_prompt = request.image_prompt or _create_product_image_prompt(
                product_data=request.product_data,
                text_content=text_content,
                headline=headline
            )
        else:
            # Use the generated text content to inform the image prompt
            image_prompt = request.image_prompt or _create_image_prompt_from_text(
                topic=request.topic,
                text_content=text_content,
                headline=headline,
                platform=request.platform
            )

        # Add headline information to the prompt if needed
        if headline and request.include_headline_on_image and not request.image_prompt:
            # Only add this if we're not using a custom image prompt
            image_prompt += f" The image should work well with the headline: '{headline}'."

        image_urls = await generate_image(image_prompt)

        for url in image_urls:
            images.append(ImageMetadata(
                url=url,
                prompt=image_prompt,
                size="1024x1024",
                style="vivid",
                generated_at=datetime.now(timezone.utc)
            ))

    # Create content object
    content = Content(
        user_id=PyObjectId(user_id),  # Convert ObjectId to PyObjectId
        title=request.topic,
        headline=headline,  # Add the generated headline
        text_content=text_content,
        images=images,
        platforms=[request.platform],
        tags=_extract_hashtags(text_content) if request.include_hashtags else [],
        status="draft",
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
        metadata={
            "generation_params": request.model_dump(),
            "source": "ai_generated",
            "headline_for_image": headline is not None
        }
    )

    # Save to database
    created_content = await create_content_in_db(content)

    # If URLs were processed with a temporary ID, update them with the actual content ID
    if request.process_urls and 'shortened_urls' in locals():
        # Update the content with the actual shortened URLs using the real content ID
        text_content, updated_shortened_urls = await detect_and_process_urls(
            content=text_content,
            user_id=str(user_id),
            content_id=str(created_content.id),
            platform=request.platform
        )

        # Update the content with the processed text that contains shortened URLs
        created_content.text_content = text_content
        created_content.metadata["shortened_urls"] = [url["short_url"] for url in updated_shortened_urls]

        # Update the content in the database
        await update_content_by_id(str(created_content.id), ContentUpdate(
            text_content=text_content,
            metadata=created_content.metadata
        ))

    # Convert to response model
    return _content_to_response(created_content)

async def generate_image_service(request: GenerateImageRequest) -> List[str]:
    """
    Generate images using OpenAI with branding information and adaptive text margins.
    """
    # Enhance the prompt with branding information if available
    enhanced_prompt = request.prompt

    if request.branding:
        # Add branding information to the prompt with size-aware text margins
        enhanced_prompt = _enhance_prompt_with_branding(enhanced_prompt, request.branding, request.size)

    # Generate image with the enhanced prompt
    image_urls = await generate_image(
        prompt=enhanced_prompt,
        size=request.size,
        style=request.style,
        n=request.n
    )

    return image_urls

def _enhance_prompt_with_branding(prompt: str, branding: BrandingData, image_size: str = "1024x1024") -> str:
    """
    Enhanced branding integration with sophisticated prompt engineering.
    Applies brand guidelines systematically for consistent brand-aligned images.

    Args:
        prompt: Base image generation prompt
        branding: Branding data containing style, colors, composition, etc.
        image_size: Image dimensions (e.g., "1024x1024", "1792x1024") for adaptive text margins
    """
    if not branding:
        return prompt

    branding_elements = []

    # 1. Color System Enhancement with sophisticated application
    if branding.colorSystem:
        color_enhancement = _build_color_system_enhancement(branding.colorSystem)
        if color_enhancement:
            branding_elements.append(color_enhancement)

    # 2. Visual Style Integration with advanced parameters
    if branding.visualStyle:
        style_enhancement = _build_visual_style_enhancement(branding.visualStyle)
        if style_enhancement:
            branding_elements.append(style_enhancement)

    # 3. Composition Framework with professional techniques (including adaptive text margins)
    if branding.imageComposition:
        composition_enhancement = _build_composition_enhancement(branding.imageComposition, image_size)
        if composition_enhancement:
            branding_elements.append(composition_enhancement)

    # 4. Visual Elements Integration with brand consistency
    if branding.visualElements:
        elements_enhancement = _build_visual_elements_enhancement(branding.visualElements)
        if elements_enhancement:
            branding_elements.append(elements_enhancement)

    # 5. Overall Brand Style Application
    if branding.style:
        brand_style_enhancement = _build_brand_style_enhancement(branding.style)
        if brand_style_enhancement:
            branding_elements.append(brand_style_enhancement)

    # Combine prompt with branding enhancements
    if branding_elements:
        enhanced_prompt = f"{prompt} {' '.join(branding_elements)}"
        # Apply branding-specific optimization
        enhanced_prompt = _optimize_branded_prompt(enhanced_prompt)
        return enhanced_prompt

    return prompt

def _build_color_system_enhancement(color_system: Dict[str, Any]) -> str:
    """
    Build sophisticated color system enhancement following professional prompt patterns.
    Enhanced to create rich, descriptive color palettes like high-end commercial prompts.
    """
    color_descriptions = []

    # Enhanced primary color application with descriptive language
    if color_system.get('primary'):
        primary = color_system['primary']
        color_descriptions.append(f"rich {primary} as the dominant brand color")

    # Sophisticated secondary color integration
    if color_system.get('secondary'):
        secondary = color_system['secondary']
        color_descriptions.append(f"elegant {secondary} as supporting tones")

    # Accent color with specific application
    if color_system.get('accent'):
        accent = color_system['accent']
        color_descriptions.append(f"vibrant {accent} accent highlights for visual interest")

    # Background color with atmospheric description
    if color_system.get('background'):
        background = color_system['background']
        color_descriptions.append(f"sophisticated {background} background tones")

    if not color_descriptions:
        return ""

    # Create sophisticated color palette description
    if len(color_descriptions) == 1:
        base_instruction = f"Use {color_descriptions[0]} throughout the composition."
    elif len(color_descriptions) == 2:
        base_instruction = f"Apply a sophisticated color palette featuring {color_descriptions[0]} and {color_descriptions[1]}."
    else:
        base_instruction = f"Implement a harmonious color palette with {', '.join(color_descriptions[:-1])}, and {color_descriptions[-1]}."

    # Add sophisticated color relationship descriptions
    if color_system.get('relationship'):
        relationship = color_system['relationship']
        if relationship == 'complementary':
            base_instruction += " Create dynamic visual impact through complementary color harmony with strategic contrast."
        elif relationship == 'analogous':
            base_instruction += " Achieve cohesive visual flow through analogous color relationships and tonal harmony."
        elif relationship == 'triadic':
            base_instruction += " Balance vibrancy and sophistication through triadic color relationships with expert application."
        elif relationship == 'monochromatic':
            base_instruction += " Develop sophisticated depth through monochromatic variations and tonal gradations."
        elif relationship == 'warm':
            base_instruction += " Create inviting, warm atmosphere through carefully selected warm color harmonies."
        elif relationship == 'cool':
            base_instruction += " Establish sophisticated, cool aesthetic through refined cool color applications."
    else:
        # Default sophisticated color application
        base_instruction += " Apply colors with professional color theory principles for maximum visual impact and brand consistency."

    return base_instruction

def _build_visual_style_enhancement(visual_style: Dict[str, Any]) -> str:
    """
    Build advanced visual style enhancement with professional parameters.
    """
    style_parts = []

    # Photography style application
    if visual_style.get('photographyStyle'):
        photo_style = visual_style['photographyStyle']
        style_parts.append(f"Apply {photo_style} photography aesthetic")

    # Lighting sophistication
    if visual_style.get('lighting'):
        lighting = visual_style['lighting']
        if lighting == 'dramatic':
            style_parts.append("with dramatic lighting and strong shadows")
        elif lighting == 'soft':
            style_parts.append("with soft, diffused lighting")
        elif lighting == 'natural':
            style_parts.append("with natural, balanced lighting")
        elif lighting == 'studio':
            style_parts.append("with professional studio lighting")
        else:
            style_parts.append(f"with {lighting} lighting")

    # Advanced visual parameters
    if visual_style.get('saturation'):
        saturation = visual_style['saturation']
        if saturation == 'high':
            style_parts.append("using vibrant, saturated colors")
        elif saturation == 'low':
            style_parts.append("with muted, desaturated tones")
        elif saturation == 'natural':
            style_parts.append("maintaining natural color saturation")

    if visual_style.get('contrast'):
        contrast = visual_style['contrast']
        if contrast == 'high':
            style_parts.append("with high contrast for impact")
        elif contrast == 'low':
            style_parts.append("with subtle, low contrast")
        elif contrast == 'balanced':
            style_parts.append("with balanced contrast levels")

    return ". ".join(style_parts) + "." if style_parts else ""

def _build_composition_enhancement(composition: Dict[str, Any], image_size: str = "1024x1024") -> str:
    """
    Build professional composition enhancement with advanced techniques.
    Includes adaptive text margin handling based on image dimensions.
    """
    comp_parts = []

    # Layout application
    if composition.get('layout'):
        layout = composition['layout']
        if layout == 'rule_of_thirds':
            comp_parts.append("Compose using rule of thirds for dynamic balance")
        elif layout == 'centered':
            comp_parts.append("Use centered composition for formal symmetry")
        elif layout == 'asymmetrical':
            comp_parts.append("Apply asymmetrical composition for visual interest")
        elif layout == 'control-hierarchy':
            comp_parts.append("Use hierarchical control layout with central command structure")
        else:
            comp_parts.append(f"Compose using {layout} layout principles")

    # Subject positioning
    if composition.get('subjectPosition'):
        position = composition['subjectPosition']
        if position == 'central-command':
            comp_parts.append("Position main elements in central command arrangement")
        else:
            comp_parts.append(f"Position main subject {position}")

    # Text margins handling with size-adaptive calculations
    if composition.get('textMargins'):
        margins = composition['textMargins']
        text_margin_instructions = _build_adaptive_text_margins(margins, image_size)
        if text_margin_instructions:
            comp_parts.append(text_margin_instructions)

    # Depth of field
    if composition.get('depthOfField'):
        dof = composition['depthOfField']
        if dof == 'shallow':
            comp_parts.append("with shallow depth of field for subject isolation")
        elif dof == 'deep':
            comp_parts.append("with deep depth of field for comprehensive focus")
        else:
            comp_parts.append(f"using {dof} depth of field")

    # Framing techniques
    if composition.get('framing'):
        framing = composition['framing']
        comp_parts.append(f"Apply {framing} framing technique")

    return ". ".join(comp_parts) + "." if comp_parts else ""

def _build_adaptive_text_margins(margins: Dict[str, Any], image_size: str) -> str:
    """
    Build adaptive text margin instructions based on image dimensions.
    Calculates optimal text placement areas considering image aspect ratio and size.

    Args:
        margins: Dictionary containing margin specifications (top, bottom, sides)
        image_size: Image size string (e.g., "1024x1024", "1792x1024")

    Returns:
        String with text margin instructions for the AI model
    """
    if not margins:
        return ""

    # Parse image dimensions
    try:
        width, height = map(int, image_size.split('x'))
        aspect_ratio = width / height
        is_landscape = width > height
        is_portrait = height > width
        is_square = width == height
    except (ValueError, AttributeError):
        # Fallback to default if parsing fails
        width, height = 1024, 1024
        aspect_ratio = 1.0
        is_landscape = is_portrait = False
        is_square = True

    margin_instructions = []

    # Process top margin
    if margins.get('top'):
        top_margin = margins['top']
        if is_landscape:
            # For landscape images, reduce top margin slightly as vertical space is limited
            adjusted_top = _adjust_margin_for_aspect_ratio(top_margin, 0.8)
            margin_instructions.append(f"Reserve {adjusted_top} clear space at the top edge for headline text placement")
        else:
            margin_instructions.append(f"Reserve {top_margin} clear space at the top edge for headline text placement")

    # Process bottom margin
    if margins.get('bottom'):
        bottom_margin = margins['bottom']
        if is_landscape:
            # For landscape images, reduce bottom margin slightly
            adjusted_bottom = _adjust_margin_for_aspect_ratio(bottom_margin, 0.8)
            margin_instructions.append(f"Reserve {adjusted_bottom} clear space at the bottom edge for CTA and branding")
        else:
            margin_instructions.append(f"Reserve {bottom_margin} clear space at the bottom edge for CTA and branding")

    # Process side margins
    if margins.get('sides'):
        side_margin = margins['sides']
        if is_portrait:
            # For portrait images, reduce side margins as horizontal space is limited
            adjusted_sides = _adjust_margin_for_aspect_ratio(side_margin, 0.7)
            margin_instructions.append(f"Maintain {adjusted_sides} clear margins on left and right sides for text readability")
        elif is_landscape:
            # For landscape images, increase side margins to utilize extra width
            adjusted_sides = _adjust_margin_for_aspect_ratio(side_margin, 1.2)
            margin_instructions.append(f"Maintain {adjusted_sides} clear margins on left and right sides for text readability")
        else:
            margin_instructions.append(f"Maintain {side_margin} clear margins on left and right sides for text readability")

    # Add size-specific text placement guidance
    size_guidance = _get_size_specific_text_guidance(width, height, aspect_ratio)
    if size_guidance:
        margin_instructions.append(size_guidance)

    if margin_instructions:
        return "Text placement zones: " + ", ".join(margin_instructions) + ". Ensure these areas have high contrast backgrounds for optimal text legibility"

    return ""

def _adjust_margin_for_aspect_ratio(margin_str: str, multiplier: float) -> str:
    """
    Adjust margin percentage based on aspect ratio considerations.

    Args:
        margin_str: Original margin string (e.g., "10%")
        multiplier: Adjustment multiplier

    Returns:
        Adjusted margin string
    """
    try:
        if margin_str.endswith('%'):
            percentage = float(margin_str[:-1])
            adjusted_percentage = max(2.0, min(25.0, percentage * multiplier))  # Clamp between 2% and 25%
            return f"{adjusted_percentage:.1f}%"
        else:
            return margin_str
    except (ValueError, AttributeError):
        return margin_str

def _get_size_specific_text_guidance(width: int, height: int, aspect_ratio: float) -> str:
    """
    Provide size-specific text placement guidance based on image dimensions.

    Args:
        width: Image width in pixels
        height: Image height in pixels
        aspect_ratio: Width/height ratio

    Returns:
        Size-specific guidance string
    """
    total_pixels = width * height

    # Ultra-wide formats (like 1792x1024)
    if aspect_ratio >= 1.6:
        return "Optimize for ultra-wide format with horizontal text flow and side-by-side content areas"

    # Wide formats
    elif aspect_ratio >= 1.3:
        return "Design for wide format with emphasis on horizontal text placement and landscape orientation"

    # Portrait formats
    elif aspect_ratio <= 0.8:
        return "Optimize for portrait format with vertical text stacking and mobile-friendly layout"

    # Square or near-square formats
    else:
        return "Balance text placement for square format with centered focal points and equal margin distribution"

def _build_visual_elements_enhancement(visual_elements: Dict[str, Any]) -> str:
    """
    Build visual elements enhancement for brand consistency.
    """
    elements_parts = []

    # Shapes integration
    if visual_elements.get('shapes'):
        shapes = visual_elements['shapes']
        if isinstance(shapes, dict):
            shape_type = shapes.get('type', 'geometric')
            shape_size = shapes.get('size', 'medium')
            elements_parts.append(f"Incorporate {shape_type} shapes of {shape_size} size")

    # Patterns application
    if visual_elements.get('patterns'):
        patterns = visual_elements['patterns']
        elements_parts.append(f"Include subtle {patterns} pattern elements")

    # Textures integration
    if visual_elements.get('textures'):
        textures = visual_elements['textures']
        elements_parts.append(f"Apply {textures} texture elements")

    return ". ".join(elements_parts) + "." if elements_parts else ""

def _build_brand_style_enhancement(brand_style: str) -> str:
    """
    Build overall brand style enhancement.
    """
    style_mappings = {
        'professional': "Maintain professional, corporate aesthetic with clean lines and sophisticated presentation",
        'creative': "Apply creative, artistic flair with innovative visual elements and dynamic composition",
        'modern': "Use modern, contemporary design with minimalist principles and current trends",
        'classic': "Employ classic, timeless design with traditional composition and elegant styling",
        'bold': "Create bold, impactful visuals with strong contrast and confident presentation",
        'elegant': "Develop elegant, refined aesthetics with sophisticated color harmony and graceful composition",
        'playful': "Incorporate playful, energetic elements with vibrant colors and dynamic arrangements",
        'minimalist': "Apply minimalist principles with clean space, simple elements, and focused composition"
    }

    return style_mappings.get(brand_style.lower(), f"Apply {brand_style} brand aesthetic throughout the composition.")

def _optimize_branded_prompt(prompt: str) -> str:
    """
    Apply branding-specific prompt optimization.
    """
    # Remove redundant branding terms
    optimized = prompt

    # Ensure brand consistency keywords are present
    brand_keywords = ['brand', 'consistent', 'professional', 'quality']
    has_brand_focus = any(keyword in optimized.lower() for keyword in brand_keywords)

    if not has_brand_focus:
        optimized = f"Create a brand-consistent, {optimized}"

    # Apply general optimization
    optimized = _optimize_prompt_structure(optimized)

    return optimized

async def bulk_generate_content_service(
    request: BulkContentGenerateRequest,
    user_id: ObjectId
) -> List[ContentResponse]:
    """
    Generate multiple content pieces in bulk.
    Each piece will have text content generated first, then a headline if requested,
    and finally an image based on the text content to ensure synchronization.
    """
    contents = []

    for topic in request.topics:
        # Create individual request for each topic
        content_request = GenerateContentRequest(
            topic=topic,
            tone=request.tone,
            platform=request.platform,
            content_type=request.content_type,
            length=request.length,
            include_hashtags=request.include_hashtags,
            target_audience=request.target_audience,
            generate_image=request.generate_images,
            include_headline_on_image=True,  # Always include headlines for bulk generation
            product_data=None,  # Bulk generation doesn't support product data yet
            call_to_action_text=None
        )

        # Generate content
        content = await generate_content_service(content_request, user_id)
        contents.append(content)

    return contents

def _create_content_prompt(request: GenerateContentRequest, policy_documents: Optional[List[BrandGuidelineDocument]] = None, use_policy_compliance: bool = True) -> str:
    """
    Create a prompt for content generation based on the request.
    Includes platform-specific instructions for different social media platforms.
    """
    length_guide = {
        "short": "100-150 words",
        "medium": "200-300 words",
        "long": "400-600 words"
    }

    key_points_text = ""
    if request.key_points:
        key_points_text = "\\nInclude these key points:\\n" + "\\n".join([f"- {point}" for point in request.key_points])

    audience_text = f"\\nTarget audience: {request.target_audience}" if request.target_audience else ""

    # Product data integration
    product_context = ""
    if request.product_data and request.include_product_details:
        product_context = _create_product_context(request.product_data, request)

    # Platform-specific instructions
    platform_instructions = _get_platform_specific_instructions(request.platform, request.content_type)

    # Extract policy information if available and enabled
    policy_info = ""
    if use_policy_compliance and policy_documents:
        policy_info = _extract_policy_information(request.topic, policy_documents)

    prompt = f"""
    Create a {request.tone} {request.content_type} for {request.platform} about "{request.topic}".
    Length: {length_guide.get(request.length, "200-300 words")}
    {audience_text}
    {key_points_text}
    {product_context}

    {"Include relevant hashtags at the end." if request.include_hashtags else ""}

    {platform_instructions}

    {policy_info}

    Make it engaging, well-structured, and optimized for {request.platform}.
    IMPORTANT: Ensure the content strictly complies with all company policies and guidelines provided above.
    """

    return prompt

def _get_platform_specific_instructions(platform: str, content_type: Optional[str] = None) -> str:
    """
    Get platform-specific instructions for content creation.

    Args:
        platform: The social media platform
        content_type: The type of content (post, article, caption)

    Returns:
        Platform-specific instructions
    """
    # Use content_type to customize instructions if needed in the future
    platform = platform.lower()

    if platform == "pinterest":
        return """
        For Pinterest:
        - Create a descriptive, keyword-rich caption that will help the pin get discovered
        - Focus on inspiring, actionable content that solves a problem or teaches something
        - Include a clear call-to-action
        - Use vertical, visually appealing descriptions
        - Incorporate relevant keywords naturally throughout the text
        - Keep descriptions concise but informative (around 100-200 words)
        - Include 5-10 relevant hashtags at the end
        """
    elif platform == "threads":
        return """
        For Threads:
        - Keep the content conversational and authentic
        - Use a more casual, personal tone even for professional content
        - Focus on creating discussion-worthy content that invites replies
        - Break longer thoughts into multiple paragraphs for readability
        - Limit to 500 characters (approximately 100 words)
        - Include 3-5 relevant hashtags
        - End with a question or call for opinions to encourage engagement
        """
    elif platform == "tiktok":
        return """
        For TikTok:
        - Create short, catchy, and attention-grabbing captions
        - Use casual, trendy language that resonates with TikTok's audience
        - Include relevant trending hashtags (3-5 is optimal)
        - Keep captions concise (ideally under 150 characters for optimal display)
        - Include a call-to-action that encourages engagement (comments, shares)
        - Use emojis strategically to enhance the message
        - Reference trending sounds, challenges, or formats when relevant
        """
    elif platform == "linkedin":
        return """
        For LinkedIn:
        - Use a professional tone while remaining conversational
        - Structure content with clear paragraphs and bullet points for readability
        - Include industry insights, professional advice, or thought leadership
        - Back claims with data or experience when possible
        - End with a question or call-to-action to encourage engagement
        - Use 3-5 relevant industry hashtags
        """
    elif platform == "twitter":
        return """
        For Twitter:
        - Be concise and direct (280 character limit)
        - Use a conversational, authentic voice
        - Include 1-2 relevant hashtags maximum
        - Consider using a hook or question to encourage engagement
        - Leave room for others to comment when sharing links or media
        """
    elif platform == "facebook":
        return """
        For Facebook:
        - Use a conversational, friendly tone
        - Tell a story or share an experience when possible
        - Keep paragraphs short for mobile readability
        - Include a clear call-to-action
        - Use minimal hashtags (1-3 maximum)
        - Consider the emotional appeal of your content
        """
    elif platform == "instagram":
        return """
        For Instagram:
        - Create visually descriptive content that complements images
        - Use a mix of short and medium-length sentences for rhythm
        - Include a strong opening line to hook readers
        - Break text into readable paragraphs
        - Use 5-15 relevant hashtags, preferably at the end
        - Include a call-to-action to encourage engagement
        """
    else:
        return """
        Create content that is optimized for social media with:
        - Clear, concise messaging
        - Engaging opening line
        - Readable structure with short paragraphs
        - A compelling call-to-action
        - Relevant hashtags if appropriate
        """

def _extract_policy_information(topic: str, policy_documents: Optional[List[BrandGuidelineDocument]]) -> str:
    """
    Extract relevant policy information based on the content topic.

    Args:
        topic: The content topic
        policy_documents: List of policy documents

    Returns:
        Formatted policy information string
    """
    if not policy_documents:
        return ""

    # Check if the topic contains keywords related to policies
    topic_lower = topic.lower()

    # Define policy-related keywords
    terms_keywords = ["terms", "conditions", "agreement", "service", "tos", "rules",
                     "legal", "rights", "obligations", "contract", "violate",
                     "compliance", "accept", "acceptable", "prohibited", "restriction"]

    privacy_keywords = ["privacy", "data", "information", "personal", "collect", "cookie",
                       "tracking", "gdpr", "ccpa", "consent", "opt-out", "opt out",
                       "third-party", "third party", "share", "delete", "access",
                       "security", "breach", "confidential", "anonymous"]

    brand_keywords = ["brand", "logo", "color", "design", "style", "voice", "tone",
                     "identity", "guidelines", "visual", "messaging", "slogan"]

    # Check if topic contains policy-related keywords
    has_terms_keywords = any(keyword in topic_lower for keyword in terms_keywords)
    has_privacy_keywords = any(keyword in topic_lower for keyword in privacy_keywords)
    has_brand_keywords = any(keyword in topic_lower for keyword in brand_keywords)

    # Always include a general policy reminder
    policy_info = """
    POLICY COMPLIANCE REQUIREMENTS:
    Ensure all content complies with the company's Terms of Service, Privacy Policy, and Brand Guidelines.
    """

    # Add specific policy information if relevant
    if has_terms_keywords or has_privacy_keywords or has_brand_keywords:
        policy_info += "\nRELEVANT POLICY INFORMATION:\n"

        # Add Terms of Service information if relevant
        if has_terms_keywords:
            tos_docs = [doc for doc in policy_documents if doc.document_type == "terms_of_service"]
            if tos_docs:
                policy_info += "\nTERMS OF SERVICE GUIDELINES:\n"
                for doc in tos_docs:
                    if hasattr(doc, 'key_points') and doc.key_points:
                        # Use structured key points if available
                        key_points = doc.key_points
                        if "user_rights" in key_points:
                            policy_info += "- User Rights: " + ", ".join(key_points["user_rights"][:3]) + "\n"
                        if "user_obligations" in key_points:
                            policy_info += "- User Obligations: " + ", ".join(key_points["user_obligations"][:3]) + "\n"
                        if "prohibited_activities" in key_points:
                            policy_info += "- Prohibited Activities: " + ", ".join(key_points["prohibited_activities"][:3]) + "\n"
                    elif doc.extracted_text:
                        # Use a snippet of the extracted text
                        policy_info += doc.extracted_text[:300] + "...\n"

        # Add Privacy Policy information if relevant
        if has_privacy_keywords:
            privacy_docs = [doc for doc in policy_documents if doc.document_type == "privacy_policy"]
            if privacy_docs:
                policy_info += "\nPRIVACY POLICY GUIDELINES:\n"
                for doc in privacy_docs:
                    if hasattr(doc, 'key_points') and doc.key_points:
                        # Use structured key points if available
                        key_points = doc.key_points
                        if "data_collected" in key_points:
                            policy_info += "- Data Collected: " + ", ".join(key_points["data_collected"][:3]) + "\n"
                        if "data_usage" in key_points:
                            policy_info += "- Data Usage: " + ", ".join(key_points["data_usage"][:3]) + "\n"
                        if "user_rights" in key_points:
                            policy_info += "- User Rights: " + ", ".join(key_points["user_rights"][:3]) + "\n"
                    elif doc.extracted_text:
                        # Use a snippet of the extracted text
                        policy_info += doc.extracted_text[:300] + "...\n"

        # Add Brand Guidelines information if relevant
        if has_brand_keywords:
            brand_docs = [doc for doc in policy_documents if doc.document_type == "brand_guidelines" or doc.document_type == "tone_of_voice"]
            if brand_docs:
                policy_info += "\nBRAND GUIDELINES:\n"
                for doc in brand_docs:
                    if doc.extracted_text:
                        # Use a snippet of the extracted text
                        policy_info += doc.extracted_text[:300] + "...\n"

    return policy_info

def _extract_hashtags(text: str) -> List[str]:
    """
    Extract hashtags from the generated content.
    """
    import re
    hashtags = re.findall(r'#(\w+)', text)
    return hashtags

async def _generate_headline(topic: str, platform: str, text_content: str) -> str:
    """
    Generate a headline for image overlay based on the topic, platform, and text content.

    Args:
        topic: The content topic
        platform: The social media platform
        text_content: The generated text content

    Returns:
        A headline suitable for overlay on an image
    """
    # Get platform-specific headline recommendations
    headline_recs = get_headline_recommendations(platform)
    max_length = headline_recs.get("max_length", 70)
    style = headline_recs.get("style", "Professional and engaging")

    # Create prompt for headline generation
    prompt = f"""
    Generate a short, attention-grabbing headline for an image that will accompany this social media post:

    Topic: {topic}
    Platform: {platform}
    Style: {style}

    The headline should:
    1. Be no more than {max_length} characters
    2. Be visually impactful when overlaid on an image
    3. Capture the essence of the post
    4. Follow best practices for {platform} headlines
    5. Be in title case

    Here's the post content to base the headline on:
    {text_content[:500]}...

    Return ONLY the headline text, nothing else.
    """

    # Generate headline
    headline = await generate_content(prompt)

    # Clean up the headline (remove quotes, extra spaces, etc.)
    headline = headline.strip().strip('"\'').strip()

    # Ensure it's not too long
    if len(headline) > max_length:
        headline = headline[:max_length-3] + "..."

    return headline

def _create_image_prompt_from_text(
    topic: str,
    text_content: str,
    headline: Optional[str] = None,
    platform: Optional[str] = None,
    company_info: Optional[Dict[str, Any]] = None
) -> str:
    """
    Create a detailed image generation prompt based on text content and company info.
    Enhanced with improved prompt engineering patterns for consistent, high-quality results.
    """
    # Enhanced content extraction with better keyword identification
    key_content = _extract_key_content_for_prompt(text_content)

    # Build prompt using structured template approach
    prompt_parts = []

    # 1. Base instruction with enhanced specificity
    base_instruction = _build_base_prompt_instruction(topic, platform, company_info)
    prompt_parts.append(base_instruction)

    # 2. Content context with semantic enhancement
    content_context = _build_content_context(key_content, topic)
    prompt_parts.append(content_context)

    # 3. Headline integration with optimal placement
    if headline:
        headline_context = _build_headline_context(headline, platform)
        prompt_parts.append(headline_context)

    # 4. Platform-specific optimization
    if platform:
        platform_optimization = _get_enhanced_platform_instructions(platform)
        prompt_parts.append(platform_optimization)

    # 5. Quality and composition framework
    quality_framework = _build_quality_framework(platform, topic)
    prompt_parts.append(quality_framework)

    # 6. Technical specifications for consistency
    technical_specs = _build_technical_specifications(platform)
    prompt_parts.append(technical_specs)

    # Combine all parts with proper spacing
    final_prompt = " ".join(prompt_parts)

    # Apply prompt optimization and validation
    optimized_prompt = _optimize_prompt_structure(final_prompt)

    return optimized_prompt

def _extract_key_content_for_prompt(text_content: str) -> str:
    """
    Enhanced content extraction for prompt generation with better keyword identification.
    """
    if not text_content:
        return ""

    # Split into sentences and clean
    sentences = [s.strip() for s in text_content.split('.') if s.strip()]

    # Take first 3 sentences but ensure we have meaningful content
    key_sentences = sentences[:3]

    # If sentences are very short, take more
    if len(' '.join(key_sentences)) < 100 and len(sentences) > 3:
        key_sentences = sentences[:5]

    # Remove hashtags and mentions for cleaner prompt
    key_content = '. '.join(key_sentences)
    key_content = re.sub(r'#\w+', '', key_content)  # Remove hashtags
    key_content = re.sub(r'@\w+', '', key_content)  # Remove mentions
    key_content = re.sub(r'\s+', ' ', key_content).strip()  # Clean whitespace

    return key_content

def _build_base_prompt_instruction(topic: str, platform: Optional[str], company_info: Optional[Dict[str, Any]]) -> str:
    """
    Build sophisticated base prompt instruction following high-quality prompt patterns.
    Enhanced to match professional prompt structures with detailed scene descriptions.
    """
    # Create detailed, scene-setting base instruction
    platform_context = platform if platform else 'social media'

    # Build sophisticated scene description
    if company_info and company_info.get('industry'):
        industry = company_info['industry']
        company_name = company_info.get('name', '')

        # Industry-specific scene building
        if 'beauty' in industry.lower() or 'cosmetic' in industry.lower():
            base = f"Create an elegant, luxurious beauty scene for {company_name if company_name else 'premium beauty'} products. "
            base += f"Show artisanal product arrangements in a sophisticated setting with natural lighting and premium aesthetic elements. "
        elif 'food' in industry.lower() or 'restaurant' in industry.lower():
            base = f"Create an appetizing, artisanal culinary scene for {company_name if company_name else 'gourmet'} featuring {topic}. "
            base += f"Show beautifully arranged ingredients and products in a warm, inviting kitchen or market setting. "
        elif 'tech' in industry.lower() or 'software' in industry.lower():
            base = f"Create a modern, innovative technology scene for {company_name if company_name else 'tech company'} showcasing {topic}. "
            base += f"Show sleek, contemporary elements with clean lines and futuristic aesthetic in a professional environment. "
        elif 'fashion' in industry.lower() or 'clothing' in industry.lower():
            base = f"Create a stylish, fashion-forward scene for {company_name if company_name else 'fashion brand'} featuring {topic}. "
            base += f"Show elegant product arrangements with sophisticated styling and contemporary fashion elements. "
        else:
            base = f"Create a professional, brand-aligned scene for {company_name if company_name else 'the company'} in the {industry} industry about '{topic}'. "
            base += f"Show expertly arranged elements that reflect the brand's expertise and market position. "
    else:
        # Generic but sophisticated base
        base = f"Create a professional, visually compelling scene for a {platform_context} post about '{topic}'. "
        base += f"Show expertly composed elements with sophisticated styling and premium aesthetic appeal. "

    # Add company-specific visual elements with detail
    if company_info:
        company_style = company_info.get('style', 'professional')

        if company_style == 'rustic':
            base += f"Incorporate rustic, artisanal elements with natural textures and handcrafted aesthetic. "
        elif company_style == 'modern':
            base += f"Use clean, contemporary design with minimalist elements and sophisticated styling. "
        elif company_style == 'luxury':
            base += f"Apply luxury aesthetic with premium materials, elegant arrangements, and sophisticated presentation. "
        elif company_style == 'creative':
            base += f"Include creative, artistic elements with innovative visual approaches and dynamic composition. "
        else:
            base += f"Apply {company_style} aesthetic throughout the composition. "

        # Add specific visual elements
        if company_info.get('visual_elements'):
            base += f"Feature {company_info['visual_elements']} as prominent visual elements in the scene. "

    return base

def _build_content_context(key_content: str, topic: str) -> str:
    """
    Build rich content context with detailed scene descriptions following professional prompt patterns.
    """
    if not key_content:
        return f"Arrange elements that visually represent '{topic}' with sophisticated composition, natural lighting, and premium aesthetic appeal."

    # Extract key themes and concepts for visual representation
    content_lower = key_content.lower()
    visual_elements = []

    # Technology themes
    if any(word in content_lower for word in ['ai', 'artificial intelligence', 'technology', 'digital', 'software']):
        visual_elements.append("modern tech elements with clean interfaces and futuristic design")

    # Business themes
    if any(word in content_lower for word in ['business', 'corporate', 'professional', 'strategy']):
        visual_elements.append("professional business elements with sophisticated presentation")

    # Creative themes
    if any(word in content_lower for word in ['creative', 'design', 'art', 'innovation']):
        visual_elements.append("artistic elements with creative flair and dynamic composition")

    # Lifestyle themes
    if any(word in content_lower for word in ['lifestyle', 'wellness', 'health', 'beauty']):
        visual_elements.append("lifestyle elements with natural, organic aesthetic and warm lighting")

    # Food/culinary themes
    if any(word in content_lower for word in ['food', 'cooking', 'recipe', 'culinary']):
        visual_elements.append("artisanal culinary elements with appetizing presentation and natural ingredients")

    # Build enhanced context
    context = f"The content explores: {key_content}. "

    if visual_elements:
        context += f"Incorporate {', '.join(visual_elements[:2])} to visually enhance this narrative. "

    context += "Arrange all elements with sophisticated styling, premium aesthetic appeal, and expert composition that reinforces the message."

    return context

def _build_headline_context(headline: str, platform: Optional[str]) -> str:
    """
    Build headline context with optimal placement instructions.
    """
    context = f"The image will feature this headline text overlay: '{headline}'."

    # Platform-specific headline placement optimization
    if platform == 'pinterest':
        context += " Design with vertical text placement in mind, ensuring the headline is prominently displayed in the upper portion with high contrast."
    elif platform == 'instagram':
        context += " Ensure the headline works well in both square and story formats, with clear readability on mobile devices."
    elif platform == 'linkedin':
        context += " Position the headline for professional presentation, typically in the upper third with corporate-appropriate typography space."
    else:
        context += " Ensure optimal text overlay space with high contrast background, preferably in the top third of the image."

    return context

def _get_enhanced_platform_instructions(platform: str) -> str:
    """
    Enhanced platform-specific instructions with proven optimization patterns.
    """
    platform = platform.lower()

    # Get base platform instructions
    base_instructions = _get_platform_specific_image_instructions(platform)

    # Add enhanced optimization patterns
    enhanced_patterns = {
        'pinterest': " Focus on vertical storytelling with clear visual hierarchy. Use bright, saturated colors that stand out in feeds. Include inspirational or instructional visual elements.",
        'instagram': " Optimize for mobile viewing with bold, clear visuals. Use contemporary aesthetics with strong visual appeal. Ensure the image works well with Instagram's algorithm preferences for engagement.",
        'linkedin': " Emphasize professional credibility and expertise. Use clean, corporate-friendly design with subtle sophistication. Include elements that convey authority and trustworthiness.",
        'twitter': " Design for quick visual impact and shareability. Use bold, simple graphics that communicate the message instantly. Optimize for both light and dark mode viewing.",
        'facebook': " Create emotionally engaging visuals that encourage interaction. Use warm, inviting colors and relatable imagery. Design for both desktop and mobile viewing experiences.",
        'tiktok': " Use dynamic, energetic visuals with bold colors and modern aesthetics. Create images that feel trendy and capture attention quickly in fast-scrolling feeds.",
        'threads': " Design with Instagram-like aesthetics but optimized for text-based conversations. Use clean, modern visuals that complement textual content."
    }

    enhancement = enhanced_patterns.get(platform, " Use versatile design that works across multiple platforms with strong visual appeal and professional quality.")

    return base_instructions + enhancement

def _build_quality_framework(platform: Optional[str], topic: str) -> str:
    """
    Build sophisticated quality and composition framework following professional prompt patterns.
    Enhanced to match high-end commercial photography specifications.
    """
    # Core professional quality specifications
    quality_elements = [
        "Apply professional commercial photography composition with expert lighting and premium aesthetic",
        "Use warm, natural lighting that enhances textures and creates inviting atmosphere",
        "Create sophisticated visual hierarchy with clear focal points and elegant negative space",
        "Implement harmonious color palette that supports brand messaging and visual appeal",
        "Ensure high-resolution, crisp details with magazine-quality finish and professional styling"
    ]

    # Topic-specific visual enhancement patterns
    topic_lower = topic.lower()

    if any(word in topic_lower for word in ['beauty', 'cosmetic', 'skincare', 'luxury']):
        quality_elements.extend([
            "Include elegant product arrangements with premium materials and sophisticated presentation",
            "Add natural elements like flowers, herbs, or organic textures for authentic appeal",
            "Use soft, diffused lighting that highlights product quality and creates luxurious mood"
        ])
    elif any(word in topic_lower for word in ['food', 'culinary', 'restaurant', 'cooking']):
        quality_elements.extend([
            "Feature appetizing food styling with fresh ingredients and artisanal presentation",
            "Include rustic or elegant table settings with complementary props and textures",
            "Apply warm, golden lighting that makes food appear fresh and inviting"
        ])
    elif any(word in topic_lower for word in ['technology', 'ai', 'digital', 'software']):
        quality_elements.extend([
            "Incorporate sleek, modern tech elements with clean interfaces and futuristic design",
            "Use contemporary color schemes with sophisticated gradients and professional styling",
            "Apply clean, minimalist composition with strategic use of negative space"
        ])
    elif any(word in topic_lower for word in ['fashion', 'style', 'clothing', 'accessories']):
        quality_elements.extend([
            "Feature stylish product arrangements with contemporary fashion styling",
            "Include sophisticated backgrounds and complementary accessories",
            "Use professional fashion photography lighting and composition techniques"
        ])
    elif any(word in topic_lower for word in ['business', 'corporate', 'professional']):
        quality_elements.extend([
            "Emphasize corporate sophistication with clean lines and professional presentation",
            "Include subtle authority elements that convey expertise and trustworthiness",
            "Apply business-appropriate color palette with sophisticated styling"
        ])
    else:
        quality_elements.extend([
            "Include contextually relevant elements that enhance the topic's visual narrative",
            "Apply sophisticated styling appropriate for the subject matter and audience"
        ])

    # Add layout and spacing specifications
    quality_elements.append("Leave appropriate space for text overlay with clear contrast and readability")
    quality_elements.append("Ensure all elements are expertly balanced with professional composition standards")

    return " ".join(quality_elements) + "."

def _build_technical_specifications(platform: Optional[str]) -> str:
    """
    Build technical specifications for consistency.
    """
    specs = [
        "Render in high resolution with sharp details",
        "Use consistent lighting and color temperature",
        "Ensure proper contrast ratios for accessibility",
        "Apply professional color grading"
    ]

    # Platform-specific technical requirements
    if platform == 'pinterest':
        specs.append("Optimize for vertical viewing with 2:3 aspect ratio consideration")
    elif platform == 'instagram':
        specs.append("Ensure mobile-first optimization with vibrant colors")
    elif platform == 'linkedin':
        specs.append("Use professional color palette with corporate-appropriate saturation")

    return " ".join(specs) + "."

def _optimize_prompt_structure(prompt: str) -> str:
    """
    Apply prompt optimization and validation for consistency.
    Enhanced to fix grammar and sentence structure issues.
    """
    # Clean up the prompt structure
    optimized = prompt

    # Fix common grammar issues
    optimized = re.sub(r'\s+', ' ', optimized).strip()  # Clean whitespace
    optimized = re.sub(r'\.+', '.', optimized)  # Fix multiple periods
    optimized = re.sub(r'\s+\.', '.', optimized)  # Fix spaces before periods

    # Fix incomplete sentences and grammar
    optimized = re.sub(r'\bThe\s+should\b', 'The image should', optimized)
    optimized = re.sub(r'\bThe\s+will\b', 'The image will', optimized)
    optimized = re.sub(r'\bfor\s+expertise\b', 'for professional expertise', optimized)
    optimized = re.sub(r'\bwith\s+minimal,\s+highly\s+readable\b', 'with minimal, highly readable text', optimized)
    optimized = re.sub(r'\bEmphasize\s+and\b', 'Emphasize professional quality and', optimized)

    # Remove redundant phrases and words
    # Handle multi-word phrases first
    optimized = re.sub(r'\bhigh quality\s+high quality\b', 'high quality', optimized, flags=re.IGNORECASE)
    optimized = re.sub(r'\bprofessional\s+professional\b', 'professional', optimized, flags=re.IGNORECASE)
    optimized = re.sub(r'\bbusiness\s+business\b', 'business', optimized, flags=re.IGNORECASE)
    optimized = re.sub(r'\btechnology\s+technology\b', 'technology', optimized, flags=re.IGNORECASE)

    # Remove redundant consecutive words
    words = optimized.split()
    filtered_words = []
    prev_word = ""

    for word in words:
        word_clean = word.lower().strip('.,!?')
        prev_clean = prev_word.lower().strip('.,!?')

        # Skip if same word appears consecutively (except common words)
        if word_clean == prev_clean and word_clean not in ['the', 'a', 'an', 'and', 'or', 'but', 'with', 'for', 'in', 'on', 'at', 'to', 'of']:
            continue

        filtered_words.append(word)
        prev_word = word

    optimized = ' '.join(filtered_words)

    # Ensure proper sentence structure
    optimized = re.sub(r'\s+', ' ', optimized).strip()

    # Add quality keywords if not present
    quality_keywords = ['professional', 'high-quality', 'visually appealing']
    has_quality = any(keyword in optimized.lower() for keyword in quality_keywords)
    if not has_quality:
        optimized = f"Create a professional, high-quality {optimized}"

    # Ensure prompt ends properly
    if not optimized.endswith('.'):
        optimized += '.'

    # Validate prompt length (DALL-E has limits)
    if len(optimized) > 1000:
        # Truncate while preserving key elements
        sentences = optimized.split('.')
        truncated = []
        current_length = 0
        for sentence in sentences:
            if current_length + len(sentence) + 1 <= 950:  # Leave room for ending
                truncated.append(sentence)
                current_length += len(sentence) + 1
            else:
                break
        optimized = '. '.join(truncated) + '.'

    return optimized

def _get_platform_specific_image_instructions(platform: str) -> str:
    """
    Enhanced platform-specific instructions for image generation with proven optimization patterns.

    Args:
        platform: The social media platform

    Returns:
        Optimized platform-specific image instructions
    """
    platform = platform.lower()

    if platform == "pinterest":
        return """Apply Pinterest optimization: Create vertical 2:3 aspect ratio composition (1000x1500px equivalent).
        Use bright, saturated colors with strong visual hierarchy. Design for inspirational discovery with clear focal points.
        Incorporate text-friendly space in upper portion. Apply clean, minimalist aesthetic with instructional appeal.
        Use warm, inviting color palette that stands out in Pinterest feeds."""

    elif platform == "threads":
        return """Apply Threads optimization: Create square (1:1) or portrait (4:5) composition.
        Use clean, modern aesthetics with Instagram-like visual appeal. Ensure high mobile contrast and readability.
        Design with authentic, conversational feel while maintaining professional quality.
        Apply contemporary, minimalist design principles with vibrant but balanced colors."""

    elif platform == "tiktok":
        return """Apply TikTok optimization: Create vertical 9:16 aspect ratio composition (1080x1920px equivalent).
        Use bold, high-contrast visuals with vibrant colors that pop on mobile screens.
        Design with trendy, dynamic aesthetic and energetic visual elements.
        Center focal points for optimal mobile viewing. Apply contemporary design trends with strong visual impact."""

    elif platform == "linkedin":
        return """Apply LinkedIn optimization: Create horizontal 1.91:1 aspect ratio composition (1200x627px equivalent).
        Use professional, corporate-appropriate design with clean lines and sophisticated color palette.
        Incorporate subtle authority elements and business credibility indicators.
        Design for professional expertise communication with minimal, highly readable text space."""

    elif platform == "twitter":
        return """Apply Twitter optimization: Create horizontal 16:9 aspect ratio composition (1200x675px equivalent).
        Use bold, simple visuals optimized for quick message communication.
        Design with high contrast for mobile viewing and feed visibility.
        Apply colors that stand out in busy social feeds with instant visual impact."""

    elif platform == "facebook":
        return """Apply Facebook optimization: Create horizontal 1.91:1 aspect ratio composition (1200x630px equivalent).
        Design for both light and dark mode compatibility with mobile-first approach.
        Create emotionally engaging visuals that encourage interaction and sharing.
        Use storytelling elements with minimal, mobile-readable text integration."""

    elif platform == "instagram":
        return """Apply Instagram optimization: Create square (1:1) or portrait (4:5) composition (1080x1080 or 1080x1350px equivalent).
        Use high-quality, aesthetically striking imagery with cohesive color harmony.
        Design for strong visual appeal and feed consistency.
        Apply contemporary aesthetics optimized for both feed and stories formats."""

    else:
        return """Apply universal optimization: Create versatile composition with clear focal points and balanced visual hierarchy.
        Use professional color palette that complements the content topic.
        Design with sufficient negative space for text overlay compatibility.
        Apply high-quality, visually appealing aesthetics suitable for multiple platforms."""

def _content_to_response(content: Content) -> ContentResponse:
    """
    Convert a Content model to a ContentResponse.
    """
    return ContentResponse(
        id=str(content.id),
        user_id=str(content.user_id),
        title=content.title,
        headline=content.headline,  # Include the headline in the response
        text_content=content.text_content,
        images=[
            ImageMetadataResponse(
                url=img.url,
                prompt=img.prompt,
                size=img.size,
                style=img.style,
                generated_at=img.generated_at
            ) for img in content.images
        ],
        platforms=content.platforms,
        tags=content.tags,
        status=content.status,
        created_at=content.created_at,
        updated_at=content.updated_at,
        published_at=content.published_at,
        scheduled_at=content.scheduled_at,
        analytics={platform: ContentAnalyticsResponse(**analytics.model_dump()) for platform, analytics in content.analytics.items()},
        metadata=content.metadata
    )

async def build_comprehensive_image_request(
    content_data: Dict[str, Any],
    branding_data: Optional[Dict[str, Any]] = None,
    company_info: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Build a comprehensive image generation request that includes detailed prompt,
    parameters, and branding information.
    """
    # Extract content information
    topic = content_data.get('topic', '')
    text_content = content_data.get('text_content', '')
    headline = content_data.get('headline', '')
    platform = content_data.get('platform', 'general')
    
    # Generate the base prompt
    prompt = _create_image_prompt_from_text(
        topic=topic,
        text_content=text_content,
        headline=headline,
        platform=platform,
        company_info=company_info
    )
    
    # Set default image parameters
    image_request = {
        "prompt": prompt,
        "size": content_data.get('size', "1024x1024"),
        "style": content_data.get('style', "vivid"),
        "quality": content_data.get('quality', "standard"),
        "n": content_data.get('n', 1),
        "headline": headline,
        "cta": content_data.get('cta', "Learn More")
    }
    
    # Add branding if available
    if branding_data:
        image_request["branding"] = branding_data
        # Convert dict to BrandingData for the enhancement function
        branding_obj = BrandingData(**branding_data)
        # Enhance the prompt with branding information and size-aware text margins
        image_request["prompt"] = _enhance_prompt_with_branding(prompt, branding_obj, image_request["size"])
    
    return image_request

async def generate_content_with_image(request: GenerateContentRequest, user_id: str) -> ContentResponse:
    """
    Generate content and associated image with enhanced branding integration.
    """
    # Get policy documents for the user if policy compliance is enabled
    policy_documents = None
    use_policy_compliance = True

    # Check user's policy compliance preference
    auto_response_settings = await get_auto_response_settings_by_user(str(user_id))
    if auto_response_settings:
        use_policy_compliance = auto_response_settings.use_policy_compliance

    # Only get policy documents if compliance is enabled
    if use_policy_compliance:
        policy_documents = await get_brand_guideline_documents_by_user(str(user_id))

    # Create prompt for content generation
    prompt = _create_content_prompt(request, policy_documents, use_policy_compliance)

    # Generate content and ensure it respects platform limitations
    text_content = await generate_content(prompt)
    text_content = truncate_text_for_platform(text_content, request.platform)

    # Process URLs in the content - detect, shorten, and add tracking
    shortened_urls = []
    if request.process_urls:
        text_content, shortened_urls = await detect_and_process_urls(
            content=text_content,
            user_id=str(user_id),
            content_id="temp_id",  # Will be updated after content creation
            platform=request.platform
        )

    # Generate headline for image overlay if requested
    headline = None
    if request.include_headline_on_image:
        headline = await _generate_headline(request.topic, request.platform, text_content)

    # Get user's company information if available
    user = await get_user_by_id(user_id)
    company_info = getattr(user, 'company_info', None)
    
    # Build comprehensive image request
    content_data = {
        'topic': request.topic,
        'text_content': text_content,  # This should be your generated text content
        'headline': headline,  # This should be your generated headline
        'platform': request.platform,
        'size': getattr(request, 'size', "1024x1024"),
        'style': getattr(request, 'style', "vivid"),
        'quality': getattr(request, 'quality', "standard"),
        'n': getattr(request, 'n', 1),
        'cta': getattr(request, 'cta', "Learn More")
    }
    
    # Get branding data from user preferences
    branding_data = None
    if hasattr(user, 'branding_preferences') and user.branding_preferences:
        # Convert BrandingPreferences to dict
        branding_data = user.branding_preferences.model_dump()
    
    # Build the comprehensive image request
    image_request = await build_comprehensive_image_request(
        content_data=content_data,
        branding_data=branding_data,
        company_info=company_info
    )
    
    # Generate image with the enhanced request
    image_urls = await generate_image(
        prompt=image_request["prompt"],
        size=image_request["size"],
        style=image_request["style"],
        n=image_request["n"]
    )

    # Create ImageMetadata objects from URLs
    images = []
    for url in image_urls:
        images.append(ImageMetadata(
            url=url,
            prompt=image_request["prompt"],
            size=image_request["size"],
            style=image_request["style"],
            generated_at=datetime.now(timezone.utc)
        ))

    # Create content object
    content = Content(
        user_id=PyObjectId(user_id),  # Convert ObjectId to PyObjectId
        title=request.topic,
        headline=headline,  # Add the generated headline
        text_content=text_content,
        images=images,
        platforms=[request.platform],
        tags=_extract_hashtags(text_content) if request.include_hashtags else [],
        status="draft",
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
        metadata={
            "generation_params": request.model_dump(),
            "source": "ai_generated",
            "headline_for_image": headline is not None
        }
    )

    # Save to database
    created_content = await create_content_in_db(content)

    # If URLs were processed with a temporary ID, update them with the actual content ID
    if request.process_urls and shortened_urls:
        # Update the content with the actual shortened URLs using the real content ID
        text_content, updated_shortened_urls = await detect_and_process_urls(
            content=text_content,
            user_id=str(user_id),
            content_id=str(created_content.id),
            platform=request.platform
        )

        # Update the content with the processed text that contains shortened URLs
        created_content.text_content = text_content
        created_content.metadata["shortened_urls"] = [url["short_url"] for url in updated_shortened_urls]

        # Update the content in the database
        await update_content_by_id(str(created_content.id), ContentUpdate(
            text_content=text_content,
            metadata=created_content.metadata
        ))

    # Convert to response model
    return _content_to_response(created_content)


def _create_product_context(product_data, request: GenerateContentRequest) -> str:
    """
    Create product context for content generation.

    Args:
        product_data: Product data input
        request: Content generation request

    Returns:
        Formatted product context string
    """
    context_parts = []

    # Basic product information
    context_parts.append(f"\\nProduct Information:")
    context_parts.append(f"- Product: {product_data.title}")

    if product_data.description:
        context_parts.append(f"- Description: {product_data.description}")

    if product_data.vendor:
        context_parts.append(f"- Brand: {product_data.vendor}")

    if product_data.category:
        context_parts.append(f"- Category: {product_data.category}")

    # Pricing information
    if request.include_pricing:
        price_text = f"${product_data.price}"
        if product_data.compare_at_price and product_data.compare_at_price > product_data.price:
            savings = product_data.compare_at_price - product_data.price
            price_text += f" (was ${product_data.compare_at_price}, save ${savings})"
        context_parts.append(f"- Price: {price_text}")

    # Product features/tags
    if product_data.tags:
        context_parts.append(f"- Key features: {', '.join(product_data.tags[:5])}")  # Limit to 5 tags

    # Inventory status
    if product_data.inventory_quantity > 0:
        if product_data.inventory_quantity <= 5:
            context_parts.append(f"- Availability: Limited stock ({product_data.inventory_quantity} remaining)")
        else:
            context_parts.append(f"- Availability: In stock")
    else:
        context_parts.append(f"- Availability: Out of stock")

    # Call to action guidance
    if request.include_call_to_action:
        cta_text = request.call_to_action_text or "Shop now"
        context_parts.append(f"\\nInclude a compelling call-to-action: '{cta_text}'")

        if product_data.product_url:
            context_parts.append(f"- Product link: {product_data.product_url}")

    # Content guidance
    context_parts.append(f"\\nContent Guidelines:")
    context_parts.append(f"- Focus on the product's value proposition and benefits")
    context_parts.append(f"- Highlight what makes this product special or unique")
    context_parts.append(f"- Create urgency if appropriate (limited stock, sale price, etc.)")
    context_parts.append(f"- Make the content compelling and persuasive for potential customers")

    return "\\n".join(context_parts)


def _create_product_image_prompt(product_data, text_content: str, headline: Optional[str] = None) -> str:
    """
    Create an enhanced image prompt for product-based content.

    Args:
        product_data: Product data input
        text_content: Generated text content
        headline: Optional headline

    Returns:
        Enhanced image prompt
    """
    # Base product description
    prompt_parts = [f"Professional product photography of {product_data.title}"]

    # Add product category context
    if product_data.category:
        category_lower = product_data.category.lower()
        if "electronics" in category_lower:
            prompt_parts.append("sleek modern tech aesthetic, clean white background")
        elif "fashion" in category_lower or "clothing" in category_lower:
            prompt_parts.append("stylish fashion photography, lifestyle setting")
        elif "home" in category_lower or "furniture" in category_lower:
            prompt_parts.append("elegant home setting, natural lighting")
        elif "beauty" in category_lower or "cosmetics" in category_lower:
            prompt_parts.append("luxury beauty photography, soft lighting")
        else:
            prompt_parts.append("professional product photography, clean aesthetic")

    # Add brand context if available
    if product_data.vendor:
        prompt_parts.append(f"representing {product_data.vendor} brand quality")

    # Add key features from tags
    if product_data.tags:
        relevant_tags = [tag for tag in product_data.tags[:3] if len(tag) > 2]  # Filter short tags
        if relevant_tags:
            prompt_parts.append(f"highlighting {', '.join(relevant_tags)}")

    # Add composition and quality guidelines
    prompt_parts.extend([
        "high-resolution commercial photography",
        "perfect lighting and composition",
        "marketing-ready image",
        "professional studio quality"
    ])

    return ", ".join(prompt_parts)


async def generate_product_content(
    product_data: Dict[str, Any],
    platform: str,
    content_type: str = "post",
    tone: str = "engaging",
    include_hashtags: bool = True,
    include_cta: bool = True,
    review_insights: Optional[Dict[str, Any]] = None,
    user_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Generate AI-powered content for e-commerce products using OpenAI.

    Args:
        product_data: Product information (name, description, price, etc.)
        platform: Target social media platform
        content_type: Type of content (post, story, ad, etc.)
        tone: Content tone (engaging, professional, casual, etc.)
        include_hashtags: Whether to include hashtags
        include_cta: Whether to include call-to-action
        review_insights: Customer review analysis data
        user_id: User ID for personalization

    Returns:
        Generated content with text, hashtags, and suggestions
    """
    try:
        product_name = product_data.get("name", "this product")
        product_description = product_data.get("description", "")
        product_price = product_data.get("price")
        product_category = product_data.get("category", "")

        # Platform-specific guidelines
        platform_guidelines = {
            "instagram": "Visual-focused, use emojis, engaging captions, story-driven",
            "facebook": "Community-focused, conversational, detailed descriptions",
            "twitter": "Concise, trending topics, quick engagement",
            "linkedin": "Professional, business-focused, value proposition",
            "tiktok": "Trendy, fun, youth-oriented, viral potential"
        }

        prompt = f"""
        Create a {tone} {content_type} for {platform} about this product:

        Product: {product_name}
        Description: {product_description}
        Category: {product_category}
        {f"Price: ${product_price}" if product_price else ""}

        Platform Guidelines: {platform_guidelines.get(platform, "Engaging and authentic")}
        """

        # Add review insights if available
        if review_insights:
            sentiment = review_insights.get("overall_sentiment", 0)
            themes = review_insights.get("key_themes", [])
            praises = review_insights.get("common_praises", [])

            if sentiment > 0.3 and praises:
                prompt += f"\n\nCustomer Love: Incorporate that customers especially praise: {', '.join(praises[:2])}"

            if themes:
                top_themes = [theme.get("theme", "") for theme in themes[:3]]
                prompt += f"\nKey Themes to Highlight: {', '.join(top_themes)}"

        # Add format requirements
        prompt += f"""

        Requirements:
        - Write in {tone} tone
        - Make it authentic and engaging
        - Focus on benefits, not just features
        - Create emotional connection
        {f"- Include relevant hashtags (5-10)" if include_hashtags else ""}
        {f"- Include compelling call-to-action" if include_cta else ""}

        Format your response as JSON:
        {{
            "text": "main content text",
            {"hashtags": ["hashtag1", "hashtag2", ...]," if include_hashtags else ""}
            {"cta": "call to action text"," if include_cta else ""}
            "suggestions": ["optimization tip 1", "optimization tip 2"]
        }}
        """

        # Generate content using OpenAI
        generated_text = await generate_content(
            prompt=prompt,
            max_tokens=800,
            temperature=0.7,
            model="gpt-4o"
        )

        # Parse the generated content
        try:
            import json
            parsed = json.loads(generated_text)
        except json.JSONDecodeError:
            # Fallback parsing
            parsed = {
                "text": generated_text,
                "hashtags": [],
                "cta": None,
                "suggestions": []
            }

        # Build result
        result = {
            "success": True,
            "content": parsed.get("text", generated_text),
            "hashtags": parsed.get("hashtags", []) if include_hashtags else [],
            "call_to_action": parsed.get("cta") if include_cta else None,
            "platform": platform,
            "content_type": content_type,
            "tone": tone,
            "product_id": product_data.get("id"),
            "generated_at": datetime.now(timezone.utc).isoformat(),
            "ai_insights": {
                "confidence_score": 0.85,
                "engagement_prediction": "high",
                "optimization_suggestions": parsed.get("suggestions", [])
            }
        }

        # Add review-based insights if available
        if review_insights:
            result["review_integration"] = {
                "sentiment_influence": review_insights.get("overall_sentiment", 0),
                "themes_highlighted": review_insights.get("key_themes", [])[:3],
                "customer_voice_included": bool(review_insights.get("common_praises"))
            }

        return result

    except Exception as e:
        logger.error(f"Error generating product content: {str(e)}")
        product_name = product_data.get("name", "this amazing product")
        return {
            "success": False,
            "error": str(e),
            "fallback_content": f"Discover {product_name} - the perfect addition to your collection! ✨ #product #shopping"
        }








