import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  LinearProgress,
  Skeleton,
  Alert
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Email as EmailIcon,
  Visibility as VisibilityIcon,
  TouchApp as TouchAppIcon,
  Cancel as UnsubscribeIcon,
  ErrorOutline as BounceIcon
} from '@mui/icons-material';

const TemplateAnalytics = ({ 
  analytics = {}, 
  loading = false 
}) => {
  const [timeRange, setTimeRange] = useState('30d');
  const [templateFilter, setTemplateFilter] = useState('all');

  const mockAnalyticsData = {
    overview: {
      totalSent: 15420,
      totalDelivered: 14890,
      totalOpened: 8934,
      totalClicked: 2145,
      totalBounced: 530,
      totalUnsubscribed: 89,
      openRate: 60.0,
      clickRate: 14.4,
      bounceRate: 3.4,
      unsubscribeRate: 0.6
    },
    topTemplates: [
      {
        id: '1',
        name: 'Welcome Email',
        type: 'transactional',
        sent: 3420,
        opened: 2890,
        clicked: 1245,
        openRate: 84.5,
        clickRate: 36.4
      },
      {
        id: '2',
        name: 'Weekly Newsletter',
        type: 'marketing',
        sent: 2890,
        opened: 1734,
        clicked: 456,
        openRate: 60.0,
        clickRate: 15.8
      },
      {
        id: '3',
        name: 'Password Reset',
        type: 'transactional',
        sent: 1890,
        opened: 1567,
        clicked: 1234,
        openRate: 82.9,
        clickRate: 65.3
      },
      {
        id: '4',
        name: 'Product Update',
        type: 'marketing',
        sent: 2340,
        opened: 1404,
        clicked: 234,
        openRate: 60.0,
        clickRate: 10.0
      }
    ]
  };

  const data = Object.keys(analytics).length > 0 ? analytics : mockAnalyticsData;
  const hasData = data.overview && (data.overview.totalSent > 0 || data.topTemplates?.length > 0);

  const getTypeColor = (type) => {
    switch (type) {
      case 'transactional':
        return 'primary';
      case 'marketing':
        return 'secondary';
      case 'system':
        return 'error';
      case 'notification':
        return 'info';
      default:
        return 'default';
    }
  };

  const renderSkeleton = () => (
    <Box>
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {[...Array(6)].map((_, index) => (
          <Grid item xs={12} sm={6} md={4} lg={2} key={index}>
            <Card variant="glass">
              <CardContent>
                <Skeleton variant="text" width="60%" height={24} />
                <Skeleton variant="text" width="40%" height={40} />
                <Skeleton variant="text" width="80%" height={16} />
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
      <Card variant="glass">
        <CardContent>
          <Skeleton variant="text" width="30%" height={32} sx={{ mb: 2 }} />
          {[...Array(5)].map((_, index) => (
            <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Skeleton variant="text" width="20%" height={24} />
              <Skeleton variant="text" width="15%" height={24} sx={{ mx: 2 }} />
              <Skeleton variant="text" width="15%" height={24} sx={{ mx: 2 }} />
              <Skeleton variant="text" width="15%" height={24} sx={{ mx: 2 }} />
              <Skeleton variant="text" width="15%" height={24} sx={{ mx: 2 }} />
            </Box>
          ))}
        </CardContent>
      </Card>
    </Box>
  );

  const renderOverviewCards = () => (
    <Grid container spacing={3} sx={{ mb: 3 }}>
      <Grid item xs={12} sm={6} md={4} lg={2}>
        <Card variant="glass" sx={{ height: '100%' }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <EmailIcon color="primary" sx={{ mr: 1 }} />
              <Typography variant="h6">Sent</Typography>
            </Box>
            <Typography variant="h4" color="primary">
              {data.overview?.totalSent?.toLocaleString() || '0'}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Total emails sent
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} sm={6} md={4} lg={2}>
        <Card variant="glass" sx={{ height: '100%' }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <VisibilityIcon color="success" sx={{ mr: 1 }} />
              <Typography variant="h6">Opened</Typography>
            </Box>
            <Typography variant="h4" color="success.main">
              {data.overview?.openRate?.toFixed(1) || '0'}%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {data.overview?.totalOpened?.toLocaleString() || '0'} opens
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} sm={6} md={4} lg={2}>
        <Card variant="glass" sx={{ height: '100%' }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <TouchAppIcon color="info" sx={{ mr: 1 }} />
              <Typography variant="h6">Clicked</Typography>
            </Box>
            <Typography variant="h4" color="info.main">
              {data.overview?.clickRate?.toFixed(1) || '0'}%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {data.overview?.totalClicked?.toLocaleString() || '0'} clicks
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} sm={6} md={4} lg={2}>
        <Card variant="glass" sx={{ height: '100%' }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <BounceIcon color="warning" sx={{ mr: 1 }} />
              <Typography variant="h6">Bounced</Typography>
            </Box>
            <Typography variant="h4" color="warning.main">
              {data.overview?.bounceRate?.toFixed(1) || '0'}%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {data.overview?.totalBounced?.toLocaleString() || '0'} bounces
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} sm={6} md={4} lg={2}>
        <Card variant="glass" sx={{ height: '100%' }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <UnsubscribeIcon color="error" sx={{ mr: 1 }} />
              <Typography variant="h6">Unsubscribed</Typography>
            </Box>
            <Typography variant="h4" color="error.main">
              {data.overview?.unsubscribeRate?.toFixed(1) || '0'}%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {data.overview?.totalUnsubscribed?.toLocaleString() || '0'} unsubs
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} sm={6} md={4} lg={2}>
        <Card variant="glass" sx={{ height: '100%' }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <TrendingUpIcon color="secondary" sx={{ mr: 1 }} />
              <Typography variant="h6">Delivered</Typography>
            </Box>
            <Typography variant="h4" color="secondary.main">
              {((data.overview?.totalDelivered / data.overview?.totalSent) * 100)?.toFixed(1) || '0'}%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {data.overview?.totalDelivered?.toLocaleString() || '0'} delivered
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderEmptyAnalytics = () => (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        py: 8,
        textAlign: 'center'
      }}
    >
      {/* Friendly illustration */}
      <Box
        sx={{
          fontSize: '4rem',
          mb: 2,
          opacity: 0.6,
          filter: 'grayscale(20%)'
        }}
      >
        📊
      </Box>

      <Typography variant="h6" color="text.secondary" gutterBottom>
        No analytics data available
      </Typography>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 3, maxWidth: 400 }}>
        Analytics will appear here once you start sending email campaigns.
        Create and send your first template to see performance metrics.
      </Typography>

      <Typography variant="caption" color="text.secondary">
        Metrics include open rates, click rates, bounce rates, and engagement trends.
      </Typography>
    </Box>
  );

  if (loading) {
    return renderSkeleton();
  }

  if (!hasData) {
    return renderEmptyAnalytics();
  }

  return (
    <Box>
      {/* Filters */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Time Range</InputLabel>
          <Select
            value={timeRange}
            label="Time Range"
            onChange={(e) => setTimeRange(e.target.value)}
          >
            <MenuItem value="7d">Last 7 days</MenuItem>
            <MenuItem value="30d">Last 30 days</MenuItem>
            <MenuItem value="90d">Last 90 days</MenuItem>
            <MenuItem value="1y">Last year</MenuItem>
          </Select>
        </FormControl>
        
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Template Type</InputLabel>
          <Select
            value={templateFilter}
            label="Template Type"
            onChange={(e) => setTemplateFilter(e.target.value)}
          >
            <MenuItem value="all">All Types</MenuItem>
            <MenuItem value="transactional">Transactional</MenuItem>
            <MenuItem value="marketing">Marketing</MenuItem>
            <MenuItem value="system">System</MenuItem>
            <MenuItem value="notification">Notification</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Overview Cards */}
      {renderOverviewCards()}

      {/* Top Performing Templates */}
      <Card variant="glass">
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Top Performing Templates
          </Typography>
          
          {data.topTemplates && data.topTemplates.length > 0 ? (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Template</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell align="right">Sent</TableCell>
                    <TableCell align="right">Opened</TableCell>
                    <TableCell align="right">Clicked</TableCell>
                    <TableCell align="right">Open Rate</TableCell>
                    <TableCell align="right">Click Rate</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data.topTemplates.map((template, index) => (
                    <TableRow key={template.id} hover>
                      <TableCell>
                        <Typography variant="subtitle2" fontWeight={600}>
                          {template.name}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={template.type}
                          size="small"
                          color={getTypeColor(template.type)}
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2">
                          {template.sent.toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2">
                          {template.opened.toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2">
                          {template.clicked.toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                          <Typography variant="body2" color="success.main" sx={{ mr: 1 }}>
                            {template.openRate.toFixed(1)}%
                          </Typography>
                          <LinearProgress
                            variant="determinate"
                            value={template.openRate}
                            sx={{ width: 60, height: 4, borderRadius: 2 }}
                            color="success"
                          />
                        </Box>
                      </TableCell>
                      <TableCell align="right">
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                          <Typography variant="body2" color="info.main" sx={{ mr: 1 }}>
                            {template.clickRate.toFixed(1)}%
                          </Typography>
                          <LinearProgress
                            variant="determinate"
                            value={template.clickRate}
                            sx={{ width: 60, height: 4, borderRadius: 2 }}
                            color="info"
                          />
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Alert severity="info">
              No analytics data available for the selected time range.
            </Alert>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default TemplateAnalytics;
