import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Alert,
  LinearProgress,
  Chip,
  IconButton,
  Tooltip,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Grid,
  Card,
  CardContent,
  Checkbox,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  Close as CloseIcon,
  Download as DownloadIcon,
  ContentCopy as CopyIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
} from '@mui/icons-material';
import { validateFormData, generateCouponCode, exportToCSV, copyToClipboard } from '../../utils/couponHelpers';
import api from '../../api';

/**
 * Bulk Coupon Manager Component
 * Allows bulk operations on coupons including generation, activation, and deletion
 */
const BulkCouponManager = ({ 
  open, 
  onClose, 
  coupons = [],
  onCouponsUpdated,
  mode = 'generate', // 'generate' or 'manage'
  ...props 
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [operation, setOperation] = useState('generate');
  const [selectedCoupons, setSelectedCoupons] = useState([]);
  const [formData, setFormData] = useState({
    prefix: '',
    count: 10,
    name: '',
    description: '',
    discount_type: 'percentage',
    discount_value: 0,
    applicable_to: 'all',
    minimum_purchase_amount: 0,
    max_redemptions_per_user: 1,
    start_date: new Date(),
    end_date: null,
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [generatedCoupons, setGeneratedCoupons] = useState([]);
  const [progress, setProgress] = useState(0);
  const [showPreview, setShowPreview] = useState(false);

  const steps = mode === 'generate' ? [
    'Configure Generation',
    'Generate Coupons',
    'Review & Export',
  ] : [
    'Select Operation',
    'Select Coupons',
    'Execute Operation',
  ];

  const bulkOperations = [
    { value: 'activate', label: 'Activate Coupons', description: 'Enable selected coupons' },
    { value: 'deactivate', label: 'Deactivate Coupons', description: 'Disable selected coupons' },
    { value: 'delete', label: 'Delete Coupons', description: 'Permanently remove selected coupons' },
    { value: 'export', label: 'Export Coupons', description: 'Export selected coupons to CSV' },
  ];

  // Form validation rules
  const validationRules = [
    { key: 'name', label: 'Name', type: 'string', minLength: 3, maxLength: 100 },
    { key: 'description', label: 'Description', type: 'string', minLength: 10, maxLength: 500 },
    { key: 'count', label: 'Count', type: 'number', min: 1, max: 1000 },
    { key: 'discount_value', label: 'Discount Value', type: 'number', min: 0 },
  ];

  // Handle form field changes
  const handleFieldChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  // Handle coupon selection
  const handleCouponSelect = (couponId) => {
    setSelectedCoupons(prev => 
      prev.includes(couponId) 
        ? prev.filter(id => id !== couponId)
        : [...prev, couponId]
    );
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedCoupons.length === coupons.length) {
      setSelectedCoupons([]);
    } else {
      setSelectedCoupons(coupons.map(c => c.id));
    }
  };

  // Validate current step
  const validateStep = (step) => {
    if (mode === 'generate' && step === 0) {
      const validation = validateFormData(formData, validationRules);
      setErrors(validation.errors);
      return validation.isValid;
    }
    return true;
  };

  // Handle next step
  const handleNext = () => {
    if (validateStep(activeStep)) {
      if (mode === 'generate' && activeStep === 0) {
        generateCoupons();
      } else if (mode === 'manage' && activeStep === 2) {
        executeBulkOperation();
      } else {
        setActiveStep(prev => prev + 1);
      }
    }
  };

  // Handle previous step
  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  // Generate coupons
  const generateCoupons = async () => {
    setLoading(true);
    setProgress(0);
    setActiveStep(1);
    
    try {
      const coupons = [];
      const batchSize = 50;
      const totalBatches = Math.ceil(formData.count / batchSize);
      
      for (let batch = 0; batch < totalBatches; batch++) {
        const batchStart = batch * batchSize;
        const batchEnd = Math.min(batchStart + batchSize, formData.count);
        const batchCoupons = [];
        
        // Generate coupons for this batch
        for (let i = batchStart; i < batchEnd; i++) {
          const code = generateCouponCode(formData.prefix, 8);
          batchCoupons.push({
            code,
            name: `${formData.name} #${i + 1}`,
            description: formData.description,
            discount_type: formData.discount_type,
            discount_value: formData.discount_value,
            applicable_to: formData.applicable_to,
            minimum_purchase_amount: formData.minimum_purchase_amount,
            max_redemptions_per_user: formData.max_redemptions_per_user,
            start_date: formData.start_date.toISOString(),
            end_date: formData.end_date ? formData.end_date.toISOString() : null,
            is_active: true,
          });
        }
        
        // Send batch to API
        const response = await api.post('/api/coupons/bulk-generate', {
          coupons: batchCoupons,
        });
        
        coupons.push(...response.data.coupons);
        
        // Update progress
        const progressPercent = ((batch + 1) / totalBatches) * 100;
        setProgress(progressPercent);
        
        // Small delay for UX
        if (batch < totalBatches - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }
      
      setGeneratedCoupons(coupons);
      setActiveStep(2);
      
      // Notify parent component
      if (onCouponsUpdated) {
        onCouponsUpdated(coupons);
      }
      
    } catch (error) {
      console.error('Error generating coupons:', error);
      setErrors({ 
        generation: error.response?.data?.detail || 'Failed to generate coupons' 
      });
      setActiveStep(0);
    } finally {
      setLoading(false);
    }
  };

  // Execute bulk operation
  const executeBulkOperation = async () => {
    if (selectedCoupons.length === 0) {
      setErrors({ selection: 'Please select at least one coupon' });
      return;
    }

    setLoading(true);
    setActiveStep(2);
    
    try {
      let response;
      
      switch (operation) {
        case 'activate':
          response = await api.post('/api/coupons/bulk-activate', {
            coupon_ids: selectedCoupons,
          });
          break;
        case 'deactivate':
          response = await api.post('/api/coupons/bulk-deactivate', {
            coupon_ids: selectedCoupons,
          });
          break;
        case 'delete':
          response = await api.post('/api/coupons/bulk-delete', {
            coupon_ids: selectedCoupons,
          });
          break;
        case 'export':
          handleExportSelected();
          return;
        default:
          throw new Error('Invalid operation');
      }
      
      if (onCouponsUpdated) {
        onCouponsUpdated(response.data);
      }
      
      handleClose();
      
    } catch (error) {
      console.error('Error executing bulk operation:', error);
      setErrors({ 
        operation: error.response?.data?.detail || `Failed to ${operation} coupons` 
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle export
  const handleExport = () => {
    const exportColumns = [
      { key: 'code', label: 'Code' },
      { key: 'name', label: 'Name' },
      { key: 'discount_type', label: 'Type' },
      { key: 'discount_value', label: 'Value' },
      { key: 'created_at', label: 'Created At', type: 'date' },
    ];
    
    exportToCSV(
      generatedCoupons, 
      `bulk-coupons-${Date.now()}`, 
      exportColumns
    );
  };

  // Handle export selected
  const handleExportSelected = () => {
    const selectedCouponData = coupons.filter(c => selectedCoupons.includes(c.id));
    const exportColumns = [
      { key: 'code', label: 'Code' },
      { key: 'name', label: 'Name' },
      { key: 'discount_type', label: 'Type' },
      { key: 'discount_value', label: 'Value' },
      { key: 'is_active', label: 'Active', type: 'boolean' },
      { key: 'redemption_count', label: 'Redemptions' },
    ];
    
    exportToCSV(
      selectedCouponData, 
      `selected-coupons-${Date.now()}`, 
      exportColumns
    );
  };

  // Handle copy all codes
  const handleCopyAll = async () => {
    const codesList = generatedCoupons.map(coupon => coupon.code).join('\n');
    const success = await copyToClipboard(codesList);
    
    if (success) {
      console.log('Codes copied to clipboard');
    }
  };

  // Handle close
  const handleClose = () => {
    setActiveStep(0);
    setOperation('generate');
    setSelectedCoupons([]);
    setFormData({
      prefix: '',
      count: 10,
      name: '',
      description: '',
      discount_type: 'percentage',
      discount_value: 0,
      applicable_to: 'all',
      minimum_purchase_amount: 0,
      max_redemptions_per_user: 1,
      start_date: new Date(),
      end_date: null,
    });
    setErrors({});
    setGeneratedCoupons([]);
    setProgress(0);
    setShowPreview(false);
    onClose();
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="lg" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          minHeight: 600,
        }
      }}
      {...props}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">
            {mode === 'generate' ? 'Bulk Coupon Generator' : 'Bulk Coupon Manager'}
          </Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Stepper activeStep={activeStep} orientation="vertical">
          {mode === 'generate' ? (
            <>
              {/* Generate Mode Steps */}
              <Step>
                <StepLabel>Configure Generation</StepLabel>
                <StepContent>
                  <Box display="flex" flexDirection="column" gap={3} mt={2}>
                    {errors.generation && (
                      <Alert severity="error">{errors.generation}</Alert>
                    )}
                    
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <TextField
                          label="Prefix (Optional)"
                          value={formData.prefix}
                          onChange={(e) => handleFieldChange('prefix', e.target.value.toUpperCase())}
                          fullWidth
                          helperText="Code prefix (e.g., SUMMER)"
                          inputProps={{ maxLength: 10 }}
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          label="Number of Coupons"
                          type="number"
                          value={formData.count}
                          onChange={(e) => handleFieldChange('count', parseInt(e.target.value) || 0)}
                          error={!!errors.count}
                          helperText={errors.count || 'Number of coupons to generate (1-1000)'}
                          inputProps={{ min: 1, max: 1000 }}
                          fullWidth
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          label="Campaign Name"
                          value={formData.name}
                          onChange={(e) => handleFieldChange('name', e.target.value)}
                          error={!!errors.name}
                          helperText={errors.name || 'Name for this coupon campaign'}
                          fullWidth
                          required
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          label="Description"
                          value={formData.description}
                          onChange={(e) => handleFieldChange('description', e.target.value)}
                          error={!!errors.description}
                          helperText={errors.description || 'Description for all generated coupons'}
                          fullWidth
                          multiline
                          rows={2}
                          required
                        />
                      </Grid>
                    </Grid>

                    <Box mt={2}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Preview: {formData.prefix ? formData.prefix + '-' : ''}XXXXXXXX
                      </Typography>
                    </Box>
                  </Box>
                </StepContent>
              </Step>

              <Step>
                <StepLabel>Generate Coupons</StepLabel>
                <StepContent>
                  <Box py={3}>
                    <Typography variant="body1" gutterBottom>
                      Generating {formData.count} coupons...
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={progress} 
                      sx={{ mt: 2, height: 8, borderRadius: 4 }}
                    />
                    <Typography variant="body2" color="text.secondary" mt={1}>
                      {Math.round(progress)}% complete
                    </Typography>
                  </Box>
                </StepContent>
              </Step>

              <Step>
                <StepLabel>Review & Export</StepLabel>
                <StepContent>
                  <Box py={2}>
                    <Alert severity="success" sx={{ mb: 2 }}>
                      <Box display="flex" alignItems="center" gap={1}>
                        <CheckCircleIcon />
                        Successfully generated {generatedCoupons.length} coupons
                      </Box>
                    </Alert>

                    <Box display="flex" gap={2} mb={2}>
                      <Button
                        variant="outlined"
                        startIcon={<DownloadIcon />}
                        onClick={handleExport}
                      >
                        Export CSV
                      </Button>
                      <Button
                        variant="outlined"
                        startIcon={<CopyIcon />}
                        onClick={handleCopyAll}
                      >
                        Copy All Codes
                      </Button>
                      <Button
                        variant="outlined"
                        startIcon={showPreview ? <VisibilityOffIcon /> : <VisibilityIcon />}
                        onClick={() => setShowPreview(!showPreview)}
                      >
                        {showPreview ? 'Hide' : 'Show'} Preview
                      </Button>
                    </Box>

                    {showPreview && (
                      <TableContainer component={Paper} sx={{ maxHeight: 300 }}>
                        <Table stickyHeader>
                          <TableHead>
                            <TableRow>
                              <TableCell>Code</TableCell>
                              <TableCell>Name</TableCell>
                              <TableCell>Discount</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {generatedCoupons.slice(0, 10).map((coupon) => (
                              <TableRow key={coupon.code}>
                                <TableCell fontFamily="monospace">{coupon.code}</TableCell>
                                <TableCell>{coupon.name}</TableCell>
                                <TableCell>
                                  {coupon.discount_type === 'percentage' ? 
                                    `${coupon.discount_value}%` : 
                                    `$${coupon.discount_value}`}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    )}

                    <Typography variant="body2" color="text.secondary" mt={2}>
                      Coupons have been saved to the database and are ready for distribution.
                    </Typography>
                  </Box>
                </StepContent>
              </Step>
            </>
          ) : (
            <>
              {/* Manage Mode Steps */}
              <Step>
                <StepLabel>Select Operation</StepLabel>
                <StepContent>
                  <FormControl fullWidth sx={{ mt: 2 }}>
                    <InputLabel>Bulk Operation</InputLabel>
                    <Select
                      value={operation}
                      onChange={(e) => setOperation(e.target.value)}
                      label="Bulk Operation"
                    >
                      {bulkOperations.map((op) => (
                        <MenuItem key={op.value} value={op.value}>
                          <Box>
                            <Typography variant="body2" fontWeight="medium">
                              {op.label}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {op.description}
                            </Typography>
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </StepContent>
              </Step>

              <Step>
                <StepLabel>Select Coupons</StepLabel>
                <StepContent>
                  {errors.selection && (
                    <Alert severity="error" sx={{ mb: 2 }}>{errors.selection}</Alert>
                  )}
                  
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="body2">
                      {selectedCoupons.length} of {coupons.length} coupons selected
                    </Typography>
                    <Button onClick={handleSelectAll} size="small">
                      {selectedCoupons.length === coupons.length ? 'Deselect All' : 'Select All'}
                    </Button>
                  </Box>

                  <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
                    <Table stickyHeader>
                      <TableHead>
                        <TableRow>
                          <TableCell padding="checkbox">
                            <Checkbox
                              checked={selectedCoupons.length === coupons.length && coupons.length > 0}
                              indeterminate={selectedCoupons.length > 0 && selectedCoupons.length < coupons.length}
                              onChange={handleSelectAll}
                            />
                          </TableCell>
                          <TableCell>Code</TableCell>
                          <TableCell>Name</TableCell>
                          <TableCell>Status</TableCell>
                          <TableCell>Redemptions</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {coupons.map((coupon) => (
                          <TableRow key={coupon.id}>
                            <TableCell padding="checkbox">
                              <Checkbox
                                checked={selectedCoupons.includes(coupon.id)}
                                onChange={() => handleCouponSelect(coupon.id)}
                              />
                            </TableCell>
                            <TableCell fontFamily="monospace">{coupon.code}</TableCell>
                            <TableCell>{coupon.name}</TableCell>
                            <TableCell>
                              <Chip 
                                label={coupon.is_active ? 'Active' : 'Inactive'}
                                color={coupon.is_active ? 'success' : 'default'}
                                size="small"
                              />
                            </TableCell>
                            <TableCell>{coupon.redemption_count || 0}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </StepContent>
              </Step>

              <Step>
                <StepLabel>Execute Operation</StepLabel>
                <StepContent>
                  {errors.operation && (
                    <Alert severity="error" sx={{ mb: 2 }}>{errors.operation}</Alert>
                  )}
                  
                  <Typography variant="body1" gutterBottom>
                    {operation === 'delete' ? 'Deleting' : 
                     operation === 'activate' ? 'Activating' : 
                     operation === 'deactivate' ? 'Deactivating' : 'Processing'} {selectedCoupons.length} coupons...
                  </Typography>
                  
                  {loading && (
                    <LinearProgress sx={{ mt: 2, height: 8, borderRadius: 4 }} />
                  )}
                </StepContent>
              </Step>
            </>
          )}
        </Stepper>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>
          {activeStep === (mode === 'generate' ? 2 : 2) ? 'Close' : 'Cancel'}
        </Button>
        {activeStep > 0 && activeStep < (mode === 'generate' ? 2 : 2) && (
          <Button onClick={handleBack} disabled={loading}>
            Back
          </Button>
        )}
        {activeStep < (mode === 'generate' ? 2 : 2) && (
          <Button 
            onClick={handleNext} 
            variant="contained"
            disabled={loading}
          >
            {activeStep === 0 ? 
              (mode === 'generate' ? 'Generate Coupons' : 'Next') : 
              (mode === 'generate' ? 'Next' : `${operation.charAt(0).toUpperCase() + operation.slice(1)} Selected`)}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default BulkCouponManager;
