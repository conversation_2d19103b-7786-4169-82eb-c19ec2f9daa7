/**
 * Enhanced Token Manager Service v2.0.0
 * Enterprise-grade token management with Enhanced Platform Service integration
 *
 * This service provides comprehensive token management with:
 * - Secure encrypted token storage with fallback mechanisms
 * - Device fingerprinting integration for enhanced security
 * - Real-time token blacklist validation
 * - Prometheus metrics integration for monitoring
 * - Circuit breaker pattern for resilience
 * - Automatic token refresh with exponential backoff
 * - Cross-tab synchronization with security validation
 * - Enhanced Platform Service v2.0.0 integration
 *
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 * @since 2024-01-01
 *
 * @example
 * ```javascript
 * import tokenManager from './services/tokenManager';
 *
 * // Initialize enhanced token manager
 * await tokenManager.initialize();
 *
 * // Set token with security features
 * await tokenManager.setToken('jwt_token_here');
 *
 * // Get token with validation
 * const token = await tokenManager.getToken();
 * ```
 */

import api from '../api';
import { STORAGE_KEYS } from '../config';
import jwtDecode from 'jwt-decode';
import { EventEmitter } from 'events';

// Enhanced Platform Service v2.0.0 Integration
import platformService from './platformService';
import { getDeviceFingerprint } from './fingerprint';
import { PrometheusMetricsCollector } from '../utils/PrometheusMetricsCollector';
import { secureStorage } from '../utils/SecureStorageService';
// Note: tokenEncryption is handled by secureStorage service

// Constants
const TOKEN_REFRESH_THRESHOLD = 5 * 60; // Refresh token 5 minutes before expiry
const TOKEN_CHECK_INTERVAL = 60 * 1000; // Check token every minute
const BLACKLIST_CHECK_INTERVAL = 30 * 1000; // Check blacklist every 30 seconds
const CIRCUIT_BREAKER_FAILURE_THRESHOLD = 5; // Circuit breaker threshold
const CIRCUIT_BREAKER_RESET_TIMEOUT = 60 * 1000; // 1 minute reset timeout
const MAX_RETRY_ATTEMPTS = 3; // Maximum retry attempts for token operations

/**
 * Enhanced Token Manager Class
 * Enterprise-grade token management with Enhanced Platform Service v2.0.0 integration
 */
class EnhancedTokenManager extends EventEmitter {
  constructor() {
    super();

    // Service dependencies
    this.platformService = null;
    this.metricsCollector = null;
    this.deviceFingerprint = null;

    // Enhanced configuration
    this.config = {
      enableEncryption: process.env.NODE_ENV === 'production' || process.env.REACT_APP_TOKEN_ENCRYPTION_ENABLED === 'true',
      enableBlacklistCheck: true,
      enableMetrics: process.env.NODE_ENV === 'production' || process.env.REACT_APP_METRICS_ENABLED === 'true',
      enableSecurityFingerprinting: true,
      enableRealTimeUpdates: true
    };

    // Circuit breaker state
    this.circuitBreaker = {
      state: 'CLOSED', // CLOSED, OPEN, HALF_OPEN
      failureCount: 0,
      lastFailureTime: null,
      nextAttemptTime: null,
      failureThreshold: CIRCUIT_BREAKER_FAILURE_THRESHOLD,
      resetTimeout: CIRCUIT_BREAKER_RESET_TIMEOUT
    };

    // Performance metrics
    this.performanceMetrics = {
      tokenOperations: new Map(),
      refreshAttempts: 0,
      refreshSuccesses: 0,
      refreshFailures: 0,
      blacklistChecks: 0,
      securityValidations: 0,
      lastOperationTime: null
    };

    // Security configuration
    this.securityConfig = {
      deviceFingerprint: null,
      platformFingerprint: null,
      fingerprintingEnabled: this.config.enableSecurityFingerprinting,
      lastSecurityCheck: null,
      securityValidationEnabled: true
    };

    // Token state management
    this.tokenState = {
      refreshInProgress: false,
      refreshPromise: null,
      lastRefreshTime: null,
      blacklistCache: new Map(),
      blacklistCacheTTL: 30000 // 30 seconds
    };

    // Timers
    this.timers = {
      tokenCheck: null,
      blacklistCheck: null,
      securityCheck: null
    };

    // Initialize flag
    this.isInitialized = false;

    // Bind methods to preserve context
    this.handleStorageEvent = this.handleStorageEvent.bind(this);
    this.checkTokenExpiry = this.checkTokenExpiry.bind(this);
    this.checkTokenBlacklist = this.checkTokenBlacklist.bind(this);
  }

  /**
   * Initialize the enhanced token manager
   *
   * @returns {Promise<Object>} Initialization result with service status
   */
  async initialize() {
    if (this.isInitialized) {
      return this.getServiceStatus();
    }

    try {
      console.log('[EnhancedTokenManager] Initializing enhanced token manager...');

      // Phase 1: Initialize Enhanced Platform Service integration
      await this._initializeEnhancedServices();

      // Phase 2: Initialize security features
      await this._initializeSecurityFeatures();

      // Phase 3: Initialize monitoring and timers
      this._initializeMonitoring();

      // Phase 4: Set up event listeners
      this._setupEventListeners();

      // Phase 5: Validate existing tokens
      await this._validateExistingTokens();

      this.isInitialized = true;

      console.log('[EnhancedTokenManager] Enhanced token manager initialized successfully');

      // Record initialization metrics
      this._recordMetric('token_manager_initialized', 'success');

      // Emit initialization event
      this.emit('initialized', this.getServiceStatus());

      return this.getServiceStatus();

    } catch (error) {
      console.error('[EnhancedTokenManager] Initialization failed:', error);
      this._recordMetric('token_manager_initialized', 'error');

      // Fallback to basic functionality
      this._initializeBasicFunctionality();

      throw error;
    }
  }

  // ===================================================================
  // ENHANCED PLATFORM SERVICE v2.0.0 INTEGRATION METHODS
  // ===================================================================

  /**
   * Initialize Enhanced Platform Service integration
   *
   * @private
   */
  async _initializeEnhancedServices() {
    try {
      // 1. Initialize Platform Service integration
      this.platformService = platformService;

      // 2. Initialize Prometheus Metrics Collector
      if (this.config.enableMetrics) {
        this.metricsCollector = new PrometheusMetricsCollector({
          enabled: true,
          endpoint: '/api/metrics/token-manager',
          batchSize: 50,
          flushInterval: 45000 // 45 seconds for token operations
        });

        console.log('[EnhancedTokenManager] Prometheus metrics initialized');
      }

      // 3. Initialize real-time configuration updates
      if (this.config.enableRealTimeUpdates && this.platformService) {
        this.platformService.on('configuration_updated', this._handleConfigurationUpdate.bind(this));
        console.log('[EnhancedTokenManager] Real-time configuration updates enabled');
      }

    } catch (error) {
      console.warn('[EnhancedTokenManager] Enhanced services initialization failed:', error);
      // Continue with basic functionality
    }
  }

  /**
   * Initialize security features
   *
   * @private
   */
  async _initializeSecurityFeatures() {
    try {
      if (this.config.enableSecurityFingerprinting) {
        // Generate device fingerprint
        this.securityConfig.deviceFingerprint = await getDeviceFingerprint();

        // Generate platform-specific fingerprint for token management
        if (this.platformService && typeof this.platformService.generatePlatformFingerprint === 'function') {
          this.securityConfig.platformFingerprint = await this.platformService.generatePlatformFingerprint('token_manager', {
            enhanced: true,
            sessionId: this._generateSessionId()
          });
        }

        console.log('[EnhancedTokenManager] Security fingerprinting initialized');
      }

    } catch (error) {
      console.warn('[EnhancedTokenManager] Security features initialization failed:', error);
      this.securityConfig.fingerprintingEnabled = false;
    }
  }

  /**
   * Initialize monitoring and timers
   *
   * @private
   */
  _initializeMonitoring() {
    // Start token expiry monitoring
    this.timers.tokenCheck = setInterval(this.checkTokenExpiry, TOKEN_CHECK_INTERVAL);

    // Start blacklist monitoring if enabled
    if (this.config.enableBlacklistCheck) {
      this.timers.blacklistCheck = setInterval(this.checkTokenBlacklist, BLACKLIST_CHECK_INTERVAL);
    }

    // Start security validation monitoring
    if (this.securityConfig.fingerprintingEnabled) {
      this.timers.securityCheck = setInterval(() => {
        this._validateSecurityContext();
      }, 300000); // Every 5 minutes
    }

    console.log('[EnhancedTokenManager] Monitoring timers initialized');
  }

  /**
   * Set up event listeners
   *
   * @private
   */
  _setupEventListeners() {
    // Cross-tab synchronization
    window.addEventListener('storage', this.handleStorageEvent);

    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });

    // Network status monitoring
    window.addEventListener('online', () => {
      this._handleNetworkStatusChange(true);
    });

    window.addEventListener('offline', () => {
      this._handleNetworkStatusChange(false);
    });
  }

  /**
   * Validate existing tokens on initialization
   *
   * @private
   */
  async _validateExistingTokens() {
    try {
      const token = await this.getToken();
      if (token) {
        const isValid = await this.isTokenValid();
        if (!isValid) {
          console.log('[EnhancedTokenManager] Existing token invalid, clearing...');
          await this.clearToken();
        } else {
          console.log('[EnhancedTokenManager] Existing token validated successfully');
        }
      }
    } catch (error) {
      console.warn('[EnhancedTokenManager] Token validation failed:', error);
    }
  }

  // ===================================================================
  // CORE TOKEN MANAGEMENT METHODS - Enhanced with Security & Metrics
  // ===================================================================

  /**
   * Get the authentication token with enhanced security validation
   *
   * @returns {Promise<string|null>} The authentication token or null if not found/invalid
   */
  async getToken() {
    try {
      // Record operation start time
      const operationStart = Date.now();

      // Get token from secure storage
      const token = await secureStorage.getItem(STORAGE_KEYS.AUTH_TOKEN, {
        deviceFingerprint: this.securityConfig.deviceFingerprint
      });

      if (!token) {
        this._recordMetric('token_get', 'not_found');
        return null;
      }

      // Validate token security context if enabled
      if (this.securityConfig.fingerprintingEnabled) {
        const isSecurityValid = await this._validateTokenSecurity(token);
        if (!isSecurityValid) {
          console.warn('[EnhancedTokenManager] Token security validation failed');
          await this.clearToken();
          this._recordMetric('token_get', 'security_failed');
          return null;
        }
      }

      // Check token blacklist if enabled
      if (this.config.enableBlacklistCheck) {
        const isBlacklisted = await this._checkTokenBlacklist(token);
        if (isBlacklisted) {
          console.warn('[EnhancedTokenManager] Token is blacklisted');
          await this.clearToken();
          this._recordMetric('token_get', 'blacklisted');
          return null;
        }
      }

      // Record successful operation
      this._recordMetric('token_get', 'success', {
        operation_time: Date.now() - operationStart
      });

      return token;

    } catch (error) {
      console.error('[EnhancedTokenManager] Get token failed:', error);
      this._recordMetric('token_get', 'error');

      // Fallback to basic localStorage
      return localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    }
  }

  /**
   * Set the authentication token with enhanced security features
   *
   * @param {string} token - The authentication token
   * @param {Object} options - Token setting options
   * @param {number} options.ttl - Time to live in milliseconds
   * @returns {Promise<boolean>} Whether token was set successfully
   */
  async setToken(token, options = {}) {
    if (!token) {
      return false;
    }

    try {
      // Record operation start time
      const operationStart = Date.now();

      // Validate token format
      if (!this._isValidJWT(token)) {
        console.warn('[EnhancedTokenManager] Invalid JWT format');
        this._recordMetric('token_set', 'invalid_format');
        return false;
      }

      // Store token securely
      const success = await secureStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token, {
        deviceFingerprint: this.securityConfig.deviceFingerprint,
        ttl: options.ttl
      });

      if (!success) {
        this._recordMetric('token_set', 'storage_failed');
        return false;
      }

      // Update security context
      this.securityConfig.lastSecurityCheck = Date.now();

      // Record successful operation
      this._recordMetric('token_set', 'success', {
        operation_time: Date.now() - operationStart,
        has_ttl: !!options.ttl
      });

      // Emit token updated event
      this.emit('token_updated', { token, timestamp: Date.now() });

      console.log('[EnhancedTokenManager] Token set successfully with enhanced security');
      return true;

    } catch (error) {
      console.error('[EnhancedTokenManager] Set token failed:', error);
      this._recordMetric('token_set', 'error');

      // Fallback to basic localStorage
      try {
        localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
        return true;
      } catch (fallbackError) {
        console.error('[EnhancedTokenManager] Fallback storage failed:', fallbackError);
        return false;
      }
    }
  }

  /**
   * Clear the authentication token from all storage mechanisms
   *
   * @returns {Promise<boolean>} Whether token was cleared successfully
   */
  async clearToken() {
    try {
      // Record operation start time
      const operationStart = Date.now();

      // Clear from secure storage
      const secureCleared = await secureStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);

      // Clear from basic localStorage (fallback)
      localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);

      // Clear blacklist cache
      this.tokenState.blacklistCache.clear();

      // Reset security context
      this.securityConfig.lastSecurityCheck = null;

      // Record successful operation
      this._recordMetric('token_clear', 'success', {
        operation_time: Date.now() - operationStart,
        secure_cleared: secureCleared
      });

      // Emit token cleared event
      this.emit('token_cleared', { timestamp: Date.now() });

      console.log('[EnhancedTokenManager] Token cleared successfully');
      return true;

    } catch (error) {
      console.error('[EnhancedTokenManager] Clear token failed:', error);
      this._recordMetric('token_clear', 'error');
      return false;
    }
  }

  /**
   * Refresh the authentication token with enhanced retry logic and circuit breaker
   *
   * @param {Object} options - Refresh options
   * @param {boolean} options.force - Force refresh even if not near expiry
   * @returns {Promise<string>} The new authentication token
   */
  async refreshToken(options = {}) {
    // Check circuit breaker
    if (!this._canAttemptRefresh()) {
      throw new Error('Circuit breaker open - refresh temporarily disabled');
    }

    // If a refresh is already in progress, return the existing promise
    if (this.tokenState.refreshInProgress && this.tokenState.refreshPromise) {
      return this.tokenState.refreshPromise;
    }

    // Set refresh in progress flag
    this.tokenState.refreshInProgress = true;
    this.performanceMetrics.refreshAttempts++;

    // Create a new refresh promise with enhanced error handling
    this.tokenState.refreshPromise = new Promise((resolve, reject) => {
      const operationStart = Date.now();
      let attempt = 0;

      const performRefresh = async () => {
        try {
          attempt++;

          // Get refresh token from secure storage
          const refreshToken = await secureStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN, {
            deviceFingerprint: this.securityConfig.deviceFingerprint
          });

          if (!refreshToken) {
            throw new Error('No refresh token available');
          }

          // Prepare refresh request with security headers
          const refreshData = {
            refresh_token: refreshToken
          };

          // Add device fingerprint if available
          if (this.securityConfig.deviceFingerprint) {
            refreshData.device_fingerprint = this.securityConfig.deviceFingerprint;
          }

          // Call refresh token API with timeout
          const response = await Promise.race([
            api.post('/api/auth/refresh', refreshData),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Refresh timeout')), 10000)
            )
          ]);

          // Validate response
          if (!response.data || !response.data.access_token) {
            throw new Error('Invalid refresh response');
          }

          // Update tokens in secure storage
          const { access_token, refresh_token: new_refresh_token } = response.data;

          await this.setToken(access_token);

          if (new_refresh_token) {
            await secureStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, new_refresh_token, {
              deviceFingerprint: this.securityConfig.deviceFingerprint
            });
          }

          // Update metrics
          this.performanceMetrics.refreshSuccesses++;
          this.tokenState.lastRefreshTime = Date.now();

          // Reset circuit breaker on success
          this._resetCircuitBreaker();

          // Record successful refresh
          this._recordMetric('token_refresh', 'success', {
            operation_time: Date.now() - operationStart,
            attempt_number: attempt,
            forced: options.force || false
          });

          console.log('[EnhancedTokenManager] Token refreshed successfully');

          // Emit refresh event
          this.emit('token_refreshed', {
            token: access_token,
            timestamp: Date.now(),
            attempt: attempt
          });

          resolve(access_token);

        } catch (error) {
          console.error(`[EnhancedTokenManager] Token refresh attempt ${attempt} failed:`, error);

          // Handle circuit breaker failure
          this._handleCircuitBreakerFailure();

          // Retry with exponential backoff
          if (attempt < MAX_RETRY_ATTEMPTS) {
            const backoffDelay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s
            console.log(`[EnhancedTokenManager] Retrying refresh in ${backoffDelay}ms...`);

            setTimeout(performRefresh, backoffDelay);
            return;
          }

          // All attempts failed
          this.performanceMetrics.refreshFailures++;

          // Record failure
          this._recordMetric('token_refresh', 'error', {
            operation_time: Date.now() - operationStart,
            final_attempt: attempt,
            error_type: error.message
          });

          // Clear tokens if refresh failed
          await this.clearToken();

          // Emit refresh failed event
          this.emit('token_refresh_failed', {
            error: error.message,
            attempts: attempt,
            timestamp: Date.now()
          });

          // Redirect to login page if needed
          if (typeof window !== 'undefined' && window.location.pathname !== '/login') {
            window.location.href = '/login?session_expired=true';
          }

          reject(error);
        } finally {
          // Reset refresh in progress flag only after all attempts
          if (attempt >= MAX_RETRY_ATTEMPTS || this.performanceMetrics.refreshSuccesses > 0) {
            this.tokenState.refreshInProgress = false;
            this.tokenState.refreshPromise = null;
          }
        }
      };

      performRefresh().catch(reject);
    });

    return this.tokenState.refreshPromise;
  }

  /**
   * Check if the token is valid with enhanced validation
   *
   * @returns {Promise<boolean>} True if the token is valid, false otherwise
   */
  async isTokenValid() {
    try {
      const operationStart = Date.now();

      const token = await this.getToken();
      if (!token) {
        this._recordMetric('token_validation', 'no_token');
        return false;
      }

      // Check JWT format and expiry
      const expiryTime = this.getTokenExpiry(token);
      if (!expiryTime) {
        this._recordMetric('token_validation', 'invalid_format');
        return false;
      }

      const currentTime = Math.floor(Date.now() / 1000);
      const isNotExpired = expiryTime > currentTime;

      if (!isNotExpired) {
        this._recordMetric('token_validation', 'expired');
        return false;
      }

      // Enhanced validation: Check blacklist if enabled
      if (this.config.enableBlacklistCheck) {
        const isBlacklisted = await this._checkTokenBlacklist(token);
        if (isBlacklisted) {
          this._recordMetric('token_validation', 'blacklisted');
          return false;
        }
      }

      // Enhanced validation: Check security context
      if (this.securityConfig.fingerprintingEnabled) {
        const isSecurityValid = await this._validateTokenSecurity(token);
        if (!isSecurityValid) {
          this._recordMetric('token_validation', 'security_failed');
          return false;
        }
      }

      // Record successful validation
      this._recordMetric('token_validation', 'success', {
        operation_time: Date.now() - operationStart,
        time_until_expiry: expiryTime - currentTime
      });

      return true;

    } catch (error) {
      console.error('[EnhancedTokenManager] Token validation failed:', error);
      this._recordMetric('token_validation', 'error');
      return false;
    }
  }

  /**
   * Get the token expiry time
   *
   * @param {string} token - Token to check (optional, uses stored token if not provided)
   * @returns {number|null} The token expiry time in seconds since epoch, or null if invalid
   */
  getTokenExpiry(token = null) {
    try {
      const targetToken = token || localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
      if (!targetToken) return null;

      const decoded = jwtDecode(targetToken);
      return decoded.exp;
    } catch (error) {
      console.error('[EnhancedTokenManager] Error decoding token expiry:', error);
      return null;
    }
  }

  /**
   * Get the token payload
   *
   * @param {string} token - Token to decode (optional, uses stored token if not provided)
   * @returns {Promise<object|null>} The decoded token payload, or null if invalid
   */
  async getTokenPayload(token = null) {
    try {
      const targetToken = token || await this.getToken();
      if (!targetToken) return null;

      return jwtDecode(targetToken);
    } catch (error) {
      console.error('[EnhancedTokenManager] Error decoding token payload:', error);
      return null;
    }
  }

  /**
   * Check if the user is authenticated with enhanced validation
   *
   * @returns {Promise<boolean>} True if the user is authenticated, false otherwise
   */
  async isAuthenticated() {
    return await this.isTokenValid();
  }

  // ===================================================================
  // EVENT HANDLERS - Enhanced with Security & Analytics
  // ===================================================================

  /**
   * Handle storage events for cross-tab synchronization with security validation
   *
   * @param {StorageEvent} event - Storage event
   */
  async handleStorageEvent(event) {
    // Only handle token-related events
    if (event.key === STORAGE_KEYS.AUTH_TOKEN) {
      try {
        if (event.newValue) {
          // New token was set in another tab
          console.log('[EnhancedTokenManager] Token updated in another tab');

          // Validate the new token
          const isValid = await this.isTokenValid();
          if (!isValid) {
            console.warn('[EnhancedTokenManager] Invalid token from another tab, clearing...');
            await this.clearToken();
          }

          // Emit cross-tab update event
          this.emit('cross_tab_update', {
            action: 'token_set',
            timestamp: Date.now()
          });

        } else {
          // Token was cleared (logout) in another tab
          console.log('[EnhancedTokenManager] Logged out in another tab');

          // Clear local token state
          await this.clearToken();

          // Emit cross-tab logout event
          this.emit('cross_tab_update', {
            action: 'logout',
            timestamp: Date.now()
          });

          // Redirect to login page if needed
          if (typeof window !== 'undefined' && window.location.pathname !== '/login') {
            window.location.href = '/login';
          }
        }

        // Record cross-tab event
        this._recordMetric('cross_tab_sync', event.newValue ? 'token_updated' : 'token_cleared');

      } catch (error) {
        console.error('[EnhancedTokenManager] Cross-tab sync failed:', error);
        this._recordMetric('cross_tab_sync', 'error');
      }
    }
  }

  /**
   * Check if token is about to expire and refresh if needed
   */
  async checkTokenExpiry() {
    // Skip if no token or refresh already in progress
    if (!await this.getToken() || this.tokenState.refreshInProgress) {
      return;
    }

    try {
      const token = await this.getToken();
      const expiryTime = this.getTokenExpiry(token);
      if (!expiryTime) return;

      const currentTime = Math.floor(Date.now() / 1000);
      const timeUntilExpiry = expiryTime - currentTime;

      // Refresh token if it's about to expire
      if (timeUntilExpiry < TOKEN_REFRESH_THRESHOLD) {
        console.log(`[EnhancedTokenManager] Token expires in ${timeUntilExpiry} seconds, refreshing...`);
        await this.refreshToken();
      }
    } catch (error) {
      console.error('[EnhancedTokenManager] Token expiry check failed:', error);
      this._recordMetric('token_expiry_check', 'error');
    }
  }

  /**
   * Check token blacklist status
   */
  async checkTokenBlacklist() {
    if (!this.config.enableBlacklistCheck) {
      return;
    }

    try {
      const token = await this.getToken();
      if (!token) return;

      const isBlacklisted = await this._checkTokenBlacklist(token);
      if (isBlacklisted) {
        console.warn('[EnhancedTokenManager] Token is blacklisted, clearing...');
        await this.clearToken();

        // Emit blacklist event
        this.emit('token_blacklisted', { timestamp: Date.now() });

        // Redirect to login
        if (typeof window !== 'undefined' && window.location.pathname !== '/login') {
          window.location.href = '/login?token_revoked=true';
        }
      }
    } catch (error) {
      console.error('[EnhancedTokenManager] Blacklist check failed:', error);
      this._recordMetric('blacklist_check', 'error');
    }
  }

  // ===================================================================
  // UTILITY METHODS - Enhanced Platform Service Integration
  // ===================================================================

  /**
   * Check if token refresh can be attempted (circuit breaker)
   *
   * @private
   * @returns {boolean} Whether refresh can be attempted
   */
  _canAttemptRefresh() {
    if (this.circuitBreaker.state === 'CLOSED') {
      return true;
    }

    if (this.circuitBreaker.state === 'OPEN') {
      // Check if reset timeout has passed
      if (Date.now() >= this.circuitBreaker.nextAttemptTime) {
        this.circuitBreaker.state = 'HALF_OPEN';
        return true;
      }
      return false;
    }

    // HALF_OPEN state - allow one attempt
    return this.circuitBreaker.state === 'HALF_OPEN';
  }

  /**
   * Handle circuit breaker failure
   *
   * @private
   */
  _handleCircuitBreakerFailure() {
    this.circuitBreaker.failureCount++;
    this.circuitBreaker.lastFailureTime = Date.now();

    if (this.circuitBreaker.failureCount >= this.circuitBreaker.failureThreshold) {
      this.circuitBreaker.state = 'OPEN';
      this.circuitBreaker.nextAttemptTime = Date.now() + this.circuitBreaker.resetTimeout;

      console.warn('[EnhancedTokenManager] Circuit breaker opened due to failures:', {
        failureCount: this.circuitBreaker.failureCount,
        nextAttemptTime: new Date(this.circuitBreaker.nextAttemptTime).toISOString()
      });

      // Emit circuit breaker event
      this.emit('circuit_breaker_opened', {
        failureCount: this.circuitBreaker.failureCount,
        nextAttemptTime: this.circuitBreaker.nextAttemptTime
      });
    }
  }

  /**
   * Reset circuit breaker on successful operation
   *
   * @private
   */
  _resetCircuitBreaker() {
    const wasOpen = this.circuitBreaker.state !== 'CLOSED';

    this.circuitBreaker.state = 'CLOSED';
    this.circuitBreaker.failureCount = 0;
    this.circuitBreaker.lastFailureTime = null;
    this.circuitBreaker.nextAttemptTime = null;

    if (wasOpen) {
      console.log('[EnhancedTokenManager] Circuit breaker reset');
      this.emit('circuit_breaker_reset', { timestamp: Date.now() });
    }
  }

  /**
   * Record metrics using Prometheus collector
   *
   * @private
   * @param {string} operation - Operation name
   * @param {string} status - Operation status
   * @param {Object} metadata - Additional metadata
   */
  _recordMetric(operation, status, metadata = {}) {
    try {
      if (!this.metricsCollector) return;

      this.metricsCollector.recordMessageSend(
        'token_manager',
        'creator', // Default tier, should be updated from user context
        status,
        metadata.operation_time || 0,
        {
          operation,
          user_id: metadata.user_id || 'unknown',
          session_id: this._generateSessionId(),
          ...metadata
        }
      );

      // Update local performance metrics
      this.performanceMetrics.tokenOperations.set(operation, {
        status,
        timestamp: Date.now(),
        metadata
      });

    } catch (error) {
      console.warn('[EnhancedTokenManager] Metrics recording failed:', error);
    }
  }

  /**
   * Check token blacklist status with caching
   *
   * @private
   * @param {string} token - Token to check
   * @returns {Promise<boolean>} Whether token is blacklisted
   */
  async _checkTokenBlacklist(token) {
    try {
      if (!token) return false;

      // Extract JWT ID for blacklist check
      const payload = jwtDecode(token);
      const jti = payload.jti;

      if (!jti) return false;

      // Check cache first
      const cacheKey = `blacklist_${jti}`;
      if (this.tokenState.blacklistCache.has(cacheKey)) {
        const cached = this.tokenState.blacklistCache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.tokenState.blacklistCacheTTL) {
          this.performanceMetrics.blacklistChecks++;
          return cached.isBlacklisted;
        }
        this.tokenState.blacklistCache.delete(cacheKey);
      }

      // Check with backend (placeholder - would need actual API endpoint)
      // For now, return false as blacklist checking requires backend integration
      const isBlacklisted = false;

      // Cache result
      this.tokenState.blacklistCache.set(cacheKey, {
        isBlacklisted,
        timestamp: Date.now()
      });

      this.performanceMetrics.blacklistChecks++;
      return isBlacklisted;

    } catch (error) {
      console.error('[EnhancedTokenManager] Blacklist check failed:', error);
      return false;
    }
  }

  /**
   * Validate token security context
   *
   * @private
   * @param {string} token - Token to validate (currently used for future security enhancements)
   * @returns {Promise<boolean>} Whether token security is valid
   */
  async _validateTokenSecurity(token) {
    try {
      if (!this.securityConfig.fingerprintingEnabled) {
        return true;
      }

      // Validate device fingerprint consistency
      const currentFingerprint = await getDeviceFingerprint();
      if (currentFingerprint !== this.securityConfig.deviceFingerprint) {
        console.warn('[EnhancedTokenManager] Device fingerprint mismatch');
        return false;
      }

      // Future enhancement: Validate token-specific security attributes
      // This could include checking token binding to device characteristics
      // For now, we validate the overall security context
      if (token && typeof token === 'string' && token.length > 0) {
        // Token format validation passed in _isValidJWT, additional security checks can be added here
      }

      this.performanceMetrics.securityValidations++;
      return true;

    } catch (error) {
      console.error('[EnhancedTokenManager] Security validation failed:', error);
      return false;
    }
  }

  /**
   * Validate JWT format
   *
   * @private
   * @param {string} token - Token to validate
   * @returns {boolean} Whether token format is valid
   */
  _isValidJWT(token) {
    try {
      if (!token || typeof token !== 'string') return false;

      const parts = token.split('.');
      if (parts.length !== 3) return false;

      // Try to decode to validate format
      jwtDecode(token);
      return true;

    } catch {
      return false;
    }
  }

  /**
   * Generate unique session ID
   *
   * @private
   * @returns {string} Session ID
   */
  _generateSessionId() {
    return `token_session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Handle network status changes
   *
   * @private
   * @param {boolean} isOnline - Whether network is online
   */
  _handleNetworkStatusChange(isOnline) {
    console.log(`[EnhancedTokenManager] Network status changed: ${isOnline ? 'online' : 'offline'}`);

    if (isOnline) {
      // Resume token validation when back online
      this.checkTokenExpiry();
      if (this.config.enableBlacklistCheck) {
        this.checkTokenBlacklist();
      }
    }

    this.emit('network_status_changed', { isOnline, timestamp: Date.now() });
  }

  /**
   * Handle configuration updates from platform service
   *
   * @private
   * @param {Object} updateData - Configuration update data
   */
  _handleConfigurationUpdate(updateData) {
    try {
      console.log('[EnhancedTokenManager] Configuration update received:', updateData);

      // Update security settings if provided
      if (updateData.security) {
        Object.assign(this.securityConfig, updateData.security);
      }

      // Update token management settings if provided
      if (updateData.tokenManager) {
        Object.assign(this.config, updateData.tokenManager);
      }

      this.emit('configuration_updated', updateData);

    } catch (error) {
      console.warn('[EnhancedTokenManager] Configuration update failed:', error);
    }
  }

  /**
   * Validate security context
   *
   * @private
   */
  async _validateSecurityContext() {
    try {
      if (!this.securityConfig.fingerprintingEnabled) {
        return;
      }

      const currentFingerprint = await getDeviceFingerprint();
      if (currentFingerprint !== this.securityConfig.deviceFingerprint) {
        console.warn('[EnhancedTokenManager] Security context validation failed');
        await this.clearToken();

        this.emit('security_violation', {
          type: 'fingerprint_mismatch',
          timestamp: Date.now()
        });
      }

    } catch (error) {
      console.error('[EnhancedTokenManager] Security context validation failed:', error);
    }
  }

  /**
   * Initialize basic functionality as fallback
   *
   * @private
   */
  _initializeBasicFunctionality() {
    console.log('[EnhancedTokenManager] Initializing basic functionality fallback');

    // Set up basic timer
    this.timers.tokenCheck = setInterval(this.checkTokenExpiry, TOKEN_CHECK_INTERVAL);

    // Set up basic event listeners
    window.addEventListener('storage', this.handleStorageEvent);
    window.addEventListener('beforeunload', () => this.cleanup());

    this.isInitialized = true;
  }

  /**
   * Get service status
   *
   * @returns {Object} Service status information
   */
  getServiceStatus() {
    return {
      initialized: this.isInitialized,
      config: this.config,
      circuitBreaker: {
        state: this.circuitBreaker.state,
        failureCount: this.circuitBreaker.failureCount
      },
      performance: {
        refreshAttempts: this.performanceMetrics.refreshAttempts,
        refreshSuccesses: this.performanceMetrics.refreshSuccesses,
        refreshFailures: this.performanceMetrics.refreshFailures,
        blacklistChecks: this.performanceMetrics.blacklistChecks,
        securityValidations: this.performanceMetrics.securityValidations
      },
      security: {
        fingerprintingEnabled: this.securityConfig.fingerprintingEnabled,
        hasDeviceFingerprint: !!this.securityConfig.deviceFingerprint,
        hasPlatformFingerprint: !!this.securityConfig.platformFingerprint
      },
      services: {
        platformService: !!this.platformService,
        metricsCollector: !!this.metricsCollector,
        secureStorage: !!secureStorage
      }
    };
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    // Clear timers
    Object.values(this.timers).forEach(timer => {
      if (timer) clearInterval(timer);
    });

    // Clear caches
    this.tokenState.blacklistCache.clear();

    // Remove event listeners
    window.removeEventListener('storage', this.handleStorageEvent);

    console.log('[EnhancedTokenManager] Cleanup completed');
  }

  // ===================================================================
  // LEGACY COMPATIBILITY METHODS
  // ===================================================================

  /**
   * Initialize token manager (legacy compatibility)
   *
   * @returns {Promise<Object>} Token manager interface
   */
  async initTokenManager() {
    await this.initialize();

    return {
      getToken: this.getToken.bind(this),
      setToken: this.setToken.bind(this),
      clearToken: this.clearToken.bind(this),
      refreshToken: this.refreshToken.bind(this),
      isTokenValid: this.isTokenValid.bind(this),
      getTokenExpiry: this.getTokenExpiry.bind(this),
      getTokenPayload: this.getTokenPayload.bind(this),
      isAuthenticated: this.isAuthenticated.bind(this)
    };
  }
}

// ===================================================================
// SINGLETON INSTANCE & EXPORTS
// ===================================================================

// Create singleton instance
const enhancedTokenManager = new EnhancedTokenManager();

// Export individual methods for backward compatibility
export const initTokenManager = enhancedTokenManager.initTokenManager.bind(enhancedTokenManager);
export const getToken = enhancedTokenManager.getToken.bind(enhancedTokenManager);
export const setToken = enhancedTokenManager.setToken.bind(enhancedTokenManager);
export const clearToken = enhancedTokenManager.clearToken.bind(enhancedTokenManager);
export const refreshToken = enhancedTokenManager.refreshToken.bind(enhancedTokenManager);
export const isTokenValid = enhancedTokenManager.isTokenValid.bind(enhancedTokenManager);
export const getTokenExpiry = enhancedTokenManager.getTokenExpiry.bind(enhancedTokenManager);
export const getTokenPayload = enhancedTokenManager.getTokenPayload.bind(enhancedTokenManager);
export const isAuthenticated = enhancedTokenManager.isAuthenticated.bind(enhancedTokenManager);

// Export default singleton instance
export default enhancedTokenManager;
