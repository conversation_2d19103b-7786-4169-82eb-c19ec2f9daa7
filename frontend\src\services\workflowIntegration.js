/**
 * Enhanced Workflow Integration Service v2.0.0
 * Enterprise-grade workflow management with Enhanced Platform Service integration
 *
 * This service provides comprehensive workflow integration with:
 * - Enhanced security with device fingerprinting and token validation
 * - Prometheus metrics integration for comprehensive monitoring
 * - Circuit breaker pattern for resilience and fault tolerance
 * - Subscription-based feature gating using ACE Social tier structure
 * - Real-time configuration updates from Enhanced Platform Service v2.0.0
 * - Advanced error handling and recovery mechanisms
 * - Workflow state management with persistence and rollback capabilities
 * - Comprehensive audit logging with security context
 * - Parallel workflow execution and optimization
 * - Integration with existing ACE Social services and content generators
 *
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 * @since 2024-01-01
 *
 * @example
 * ```javascript
 * import workflowIntegration from './services/workflowIntegration';
 *
 * // Initialize enhanced workflow service
 * await workflowIntegration.initialize();
 *
 * // Execute workflow with enhanced security
 * const results = await workflowIntegration.completeWorkflowIntegration(workflowData, userId);
 * ```
 */

import api from '../api';
import { workflowCache, cacheKey } from '../utils/cache';
import { EventEmitter } from 'events';

// Enhanced Platform Service v2.0.0 Integration
import platformService from './platformService';
import { getDeviceFingerprint } from './fingerprint';
import { PrometheusMetricsCollector } from '../utils/PrometheusMetricsCollector';
import tokenManager from './tokenManager';

// Workflow operation types for metrics and logging
export const WorkflowOperationType = {
  SERVICE_CREATION: 'service_creation',
  ICP_CREATION: 'icp_creation',
  CAMPAIGN_CREATION: 'campaign_creation',
  WORKFLOW_COMPLETION: 'workflow_completion',
  ANALYTICS_UPDATE: 'analytics_update',
  CONTENT_GENERATION: 'content_generation'
};

// Workflow error types for enhanced error handling
export const WorkflowErrorType = {
  AUTHENTICATION_FAILED: 'authentication_failed',
  SUBSCRIPTION_ACCESS_DENIED: 'subscription_access_denied',
  SECURITY_VALIDATION_FAILED: 'security_validation_failed',
  SERVICE_UNAVAILABLE: 'service_unavailable',
  WORKFLOW_STEP_FAILED: 'workflow_step_failed',
  CIRCUIT_BREAKER_OPEN: 'circuit_breaker_open',
  VALIDATION_ERROR: 'validation_error'
};

// ACE Social subscription tiers for feature gating
export const SubscriptionTier = {
  CREATOR: 'creator',
  ACCELERATOR: 'accelerator',
  DOMINATOR: 'dominator'
};

/**
 * Enhanced Workflow Integration Service Class
 * Enterprise-grade workflow management with Enhanced Platform Service v2.0.0 integration
 */
class EnhancedWorkflowIntegrationService extends EventEmitter {
  constructor() {
    super();

    // Service dependencies
    this.platformService = null;
    this.metricsCollector = null;
    this.deviceFingerprint = null;

    // Enhanced configuration
    this.config = {
      enableMetrics: process.env.NODE_ENV === 'production' || process.env.REACT_APP_METRICS_ENABLED === 'true',
      enableSecurityFingerprinting: true,
      enableSubscriptionValidation: true,
      enableCircuitBreaker: true,
      enableRealTimeUpdates: true,
      enableWorkflowTemplates: true,
      enableParallelExecution: true,
      enableWorkflowRollback: true
    };

    // Security configuration
    this.securityConfig = {
      deviceFingerprint: null,
      platformFingerprint: null,
      fingerprintingEnabled: this.config.enableSecurityFingerprinting,
      lastSecurityCheck: null,
      securityValidationEnabled: true,
      tokenValidationEnabled: true
    };

    // Circuit breaker configuration
    this.circuitBreaker = {
      state: 'CLOSED', // CLOSED, OPEN, HALF_OPEN
      failureCount: 0,
      failureThreshold: 5,
      resetTimeout: 60000, // 1 minute
      lastFailureTime: null,
      nextAttemptTime: null
    };

    // Performance metrics
    this.performanceMetrics = {
      workflowsExecuted: 0,
      successfulWorkflows: 0,
      failedWorkflows: 0,
      averageExecutionTime: 0,
      securityValidations: 0,
      circuitBreakerTrips: 0,
      parallelExecutions: 0,
      rollbackOperations: 0
    };

    // Workflow state management
    this.activeWorkflows = new Map();
    this.workflowTemplates = new Map();
    this.workflowHistory = new Map();

    // Enhanced workflow configuration
    this.correlationId = null;
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    this.userCachePrefix = 'enhanced_workflow_user_';
    this.sessionId = this._generateSessionId();
    this.subscriptionTier = null;
    this.isInitialized = false;

    // Initialize enhanced services
    this._initializeEnhancedServices();
  }

  // ===================================================================
  // ENHANCED INITIALIZATION & SETUP
  // ===================================================================

  /**
   * Initialize enhanced workflow integration service
   *
   * @returns {Promise<Object>} Service status
   */
  async initialize() {
    try {
      console.log('[EnhancedWorkflowIntegrationService] Initializing enhanced workflow service...');

      // Phase 1: Initialize Enhanced Platform Service integration
      await this._initializeEnhancedServices();

      // Phase 2: Initialize security features
      await this._initializeSecurityFeatures();

      // Phase 3: Initialize monitoring and metrics
      this._initializeMonitoring();

      // Phase 4: Set up event listeners
      this._setupEventListeners();

      // Phase 5: Initialize workflow templates
      this._initializeWorkflowTemplates();

      // Phase 6: Validate subscription tier
      await this._initializeSubscriptionTierDetection();

      this.isInitialized = true;

      console.log('[EnhancedWorkflowIntegrationService] Enhanced workflow service initialized successfully');

      // Record initialization metrics
      this._recordMetric('workflow_service_initialized', 'success', {
        session_id: this.sessionId,
        security_enabled: this.securityConfig.fingerprintingEnabled,
        subscription_tier: this.subscriptionTier
      });

      // Emit initialization event
      this.emit('initialized', this.getServiceStatus());

      return this.getServiceStatus();

    } catch (error) {
      console.error('[EnhancedWorkflowIntegrationService] Initialization failed:', error);

      // Try to initialize basic functionality as fallback
      this._initializeBasicFunctionality();

      throw error;
    }
  }

  /**
   * Initialize Enhanced Platform Service integration
   *
   * @private
   */
  async _initializeEnhancedServices() {
    try {
      // 1. Initialize Platform Service integration
      this.platformService = platformService;

      // 2. Initialize Prometheus Metrics Collector
      if (this.config.enableMetrics) {
        this.metricsCollector = new PrometheusMetricsCollector({
          enabled: true,
          endpoint: '/api/metrics/workflow',
          batchSize: 50,
          flushInterval: 30000 // 30 seconds for workflow operations
        });

        console.log('[EnhancedWorkflowIntegrationService] Prometheus metrics initialized');
      }

      // 3. Set up real-time configuration updates
      if (this.config.enableRealTimeUpdates && this.platformService) {
        this.platformService.on('configuration_updated', this._handleConfigurationUpdate.bind(this));
        console.log('[EnhancedWorkflowIntegrationService] Real-time configuration updates enabled');
      }

    } catch (error) {
      console.warn('[EnhancedWorkflowIntegrationService] Enhanced services initialization failed:', error);
      // Continue with basic functionality
    }
  }

  /**
   * Initialize security features
   *
   * @private
   */
  async _initializeSecurityFeatures() {
    try {
      if (this.config.enableSecurityFingerprinting) {
        // Generate device fingerprint
        this.securityConfig.deviceFingerprint = await getDeviceFingerprint();

        // Generate platform-specific fingerprint for workflow management
        if (this.platformService && typeof this.platformService.generatePlatformFingerprint === 'function') {
          this.securityConfig.platformFingerprint = await this.platformService.generatePlatformFingerprint('workflow', {
            enhanced: true,
            sessionId: this.sessionId
          });
        }

        console.log('[EnhancedWorkflowIntegrationService] Security fingerprinting initialized');
      }

      this.securityConfig.lastSecurityCheck = Date.now();

    } catch (error) {
      console.warn('[EnhancedWorkflowIntegrationService] Security features initialization failed:', error);
      this.securityConfig.fingerprintingEnabled = false;
    }
  }

  /**
   * Initialize monitoring and metrics
   *
   * @private
   */
  _initializeMonitoring() {
    try {
      // Set up performance tracking
      this.performanceMetrics.startTime = Date.now();

      // Initialize workflow state tracking
      this.activeWorkflows.clear();
      this.workflowHistory.clear();

      console.log('[EnhancedWorkflowIntegrationService] Monitoring initialized');

    } catch (error) {
      console.warn('[EnhancedWorkflowIntegrationService] Monitoring initialization failed:', error);
    }
  }

  /**
   * Set up event listeners
   *
   * @private
   */
  _setupEventListeners() {
    try {
      // Listen for network status changes
      if (typeof window !== 'undefined') {
        window.addEventListener('online', this._handleNetworkStatusChange.bind(this, true));
        window.addEventListener('offline', this._handleNetworkStatusChange.bind(this, false));
        window.addEventListener('beforeunload', this.cleanup.bind(this));
      }

      console.log('[EnhancedWorkflowIntegrationService] Event listeners initialized');

    } catch (error) {
      console.warn('[EnhancedWorkflowIntegrationService] Event listeners setup failed:', error);
    }
  }

  /**
   * Initialize workflow templates
   *
   * @private
   */
  _initializeWorkflowTemplates() {
    try {
      if (this.config.enableWorkflowTemplates) {
        // Define standard workflow templates
        this.workflowTemplates.set('standard', {
          name: 'Standard Workflow',
          steps: ['service_creation', 'icp_creation', 'campaign_creation'],
          parallel: false,
          rollbackEnabled: true
        });

        this.workflowTemplates.set('parallel', {
          name: 'Parallel Workflow',
          steps: ['service_creation', 'icp_creation', 'campaign_creation'],
          parallel: true,
          rollbackEnabled: true
        });

        this.workflowTemplates.set('content_focused', {
          name: 'Content-Focused Workflow',
          steps: ['service_creation', 'content_generation', 'campaign_creation'],
          parallel: false,
          rollbackEnabled: true
        });

        console.log('[EnhancedWorkflowIntegrationService] Workflow templates initialized');
      }

    } catch (error) {
      console.warn('[EnhancedWorkflowIntegrationService] Workflow templates initialization failed:', error);
    }
  }

  /**
   * Generate cache key for workflow data with enhanced security context
   */
  generateCacheKey(type, identifier, userId = null) {
    const baseKey = cacheKey(type, identifier);
    const securitySuffix = this.securityConfig.fingerprintingEnabled ?
      `_${this.securityConfig.deviceFingerprint?.slice(0, 8) || 'nosec'}` : '';
    return userId ?
      `${this.userCachePrefix}${userId}_${baseKey}${securitySuffix}` :
      `${baseKey}${securitySuffix}`;
  }

  // ===================================================================
  // ENHANCED AUTHENTICATION & SECURITY VALIDATION
  // ===================================================================

  /**
   * Validate user authentication with enhanced security
   *
   * @private
   * @param {string} userId - User ID to validate
   * @returns {Promise<boolean>} Whether user is authenticated
   */
  async _validateUserAuthentication(userId) {
    try {
      if (!this.config.enableSecurityFingerprinting) {
        return true; // Skip validation if disabled
      }

      // Check if tokenManager is available and initialized
      if (!tokenManager || !tokenManager.isInitialized) {
        console.warn('[EnhancedWorkflowIntegrationService] TokenManager not available for authentication');
        return false;
      }

      // Validate token
      const isTokenValid = await tokenManager.isTokenValid();
      if (!isTokenValid) {
        console.warn('[EnhancedWorkflowIntegrationService] Token validation failed');
        this._recordMetric('authentication_validation', 'failed', {
          user_id: userId,
          reason: 'invalid_token'
        });
        return false;
      }

      // Get token payload to verify user
      const tokenPayload = await tokenManager.getTokenPayload();
      if (!tokenPayload || tokenPayload.sub !== userId) {
        console.warn('[EnhancedWorkflowIntegrationService] User ID mismatch in token');
        this._recordMetric('authentication_validation', 'failed', {
          user_id: userId,
          reason: 'user_mismatch'
        });
        return false;
      }

      // Update performance metrics
      this.performanceMetrics.securityValidations++;

      // Record successful validation
      this._recordMetric('authentication_validation', 'success', {
        user_id: userId
      });

      return true;

    } catch (error) {
      console.error('[EnhancedWorkflowIntegrationService] Authentication validation failed:', error);
      this._recordMetric('authentication_validation', 'error', {
        user_id: userId,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Validate subscription tier access for workflow operations
   *
   * @private
   * @param {string} userId - User ID
   * @param {string} operation - Operation type
   * @returns {Promise<boolean>} Whether user has access
   */
  async _validateSubscriptionAccess(userId, operation) {
    try {
      if (!this.config.enableSubscriptionValidation) {
        return true; // Skip validation if disabled
      }

      // Get user subscription tier
      const userTier = await this._getUserSubscriptionTier(userId);

      // Define operation access requirements
      const operationRequirements = {
        'service_creation': [SubscriptionTier.CREATOR, SubscriptionTier.ACCELERATOR, SubscriptionTier.DOMINATOR],
        'icp_creation': [SubscriptionTier.CREATOR, SubscriptionTier.ACCELERATOR, SubscriptionTier.DOMINATOR],
        'campaign_creation': [SubscriptionTier.ACCELERATOR, SubscriptionTier.DOMINATOR],
        'parallel_execution': [SubscriptionTier.DOMINATOR],
        'workflow_templates': [SubscriptionTier.ACCELERATOR, SubscriptionTier.DOMINATOR],
        'advanced_analytics': [SubscriptionTier.DOMINATOR]
      };

      const requiredTiers = operationRequirements[operation] || [SubscriptionTier.CREATOR];
      const hasAccess = requiredTiers.includes(userTier);

      if (!hasAccess) {
        console.warn('[EnhancedWorkflowIntegrationService] Subscription access denied:', {
          user_id: userId,
          operation,
          user_tier: userTier,
          required_tiers: requiredTiers
        });

        this._recordMetric('subscription_access_check', 'denied', {
          user_id: userId,
          operation,
          user_tier: userTier
        });

        // Emit access denied event for potential upsell
        this.emit('subscription_access_denied', {
          userId,
          operation,
          userTier,
          requiredTiers,
          timestamp: Date.now()
        });
      } else {
        this._recordMetric('subscription_access_check', 'granted', {
          user_id: userId,
          operation,
          user_tier: userTier
        });
      }

      return hasAccess;

    } catch (error) {
      console.error('[EnhancedWorkflowIntegrationService] Subscription validation failed:', error);
      this._recordMetric('subscription_access_check', 'error', {
        user_id: userId,
        operation,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Validate security context for workflow operations
   *
   * @private
   * @returns {Promise<boolean>} Whether security context is valid
   */
  async _validateSecurityContext() {
    try {
      if (!this.securityConfig.fingerprintingEnabled) {
        return true; // Skip validation if disabled
      }

      // Check if security fingerprints are available
      if (!this.securityConfig.deviceFingerprint) {
        console.warn('[EnhancedWorkflowIntegrationService] Device fingerprint not available');
        return false;
      }

      // Validate security context freshness
      const securityAge = Date.now() - this.securityConfig.lastSecurityCheck;
      if (securityAge > 300000) { // 5 minutes
        console.warn('[EnhancedWorkflowIntegrationService] Security context expired');

        // Refresh security context
        await this._refreshSecurityContext();
      }

      return true;

    } catch (error) {
      console.error('[EnhancedWorkflowIntegrationService] Security context validation failed:', error);
      return false;
    }
  }

  /**
   * Cache workflow data with enhanced security context
   */
  cacheWorkflowData(type, identifier, data, userId = null) {
    const key = this.generateCacheKey(type, identifier, userId);
    const cacheData = {
      data,
      timestamp: Date.now(),
      userId,
      correlationId: this.correlationId,
      type,
      identifier,
      sessionId: this.sessionId,
      securityContext: {
        deviceFingerprint: this.securityConfig.deviceFingerprint,
        platformFingerprint: this.securityConfig.platformFingerprint,
        securityValidated: this.securityConfig.fingerprintingEnabled
      }
    };

    workflowCache.set(key, cacheData, this.cacheTimeout);

    console.debug('[EnhancedWorkflowIntegrationService] Cached workflow data:', {
      key,
      type,
      identifier,
      userId,
      correlationId: this.correlationId,
      sessionId: this.sessionId,
      dataSize: JSON.stringify(data).length,
      securityEnabled: this.securityConfig.fingerprintingEnabled
    });

    return key;
  }

  /**
   * Retrieve cached workflow data
   */
  getCachedWorkflowData(type, identifier, userId = null) {
    const key = this.generateCacheKey(type, identifier, userId);
    const cachedData = workflowCache.get(key);

    if (cachedData) {
      console.debug('Retrieved cached workflow data:', {
        key,
        type,
        identifier,
        userId,
        age: Date.now() - cachedData.timestamp
      });

      return cachedData.data;
    }

    return null;
  }

  setCorrelationId(id) {
    this.correlationId = id;
  }

  // ===================================================================
  // ENHANCED WORKFLOW OPERATIONS WITH SECURITY & MONITORING
  // ===================================================================

  /**
   * Save service from workflow to existing service management system with enhanced security
   */
  async saveServiceToSystem(serviceData, userId) {
    // Check circuit breaker
    if (!this._canExecuteOperation()) {
      throw new Error('Circuit breaker open - service creation temporarily disabled');
    }

    const startTime = Date.now();
    const operationId = this._generateOperationId();

    try {
      // Enhanced security validation
      await this._validateUserAuthentication(userId);
      await this._validateSubscriptionAccess(userId, 'service_creation');
      await this._validateSecurityContext();

      // Cache the service data with enhanced metadata
      const cacheKey = this.cacheWorkflowData('service', serviceData.name, serviceData, userId);

      // Enhanced logging with security context
      console.log('[EnhancedWorkflowIntegrationService] Saving service to system:', {
        serviceName: serviceData.name,
        userId,
        correlationId: this.correlationId,
        sessionId: this.sessionId,
        operationId,
        cacheKey,
        securityEnabled: this.securityConfig.fingerprintingEnabled,
        timestamp: new Date().toISOString()
      });

      // Enhanced service payload with security context
      const servicePayload = {
        name: serviceData.name,
        description: serviceData.description,
        value_proposition: serviceData.value_proposition,
        target_industry: serviceData.target_industry,
        pricing_model: serviceData.pricing_model,
        service_level: serviceData.service_level,
        delivery_timeline: serviceData.delivery_timeline,
        target_segments: serviceData.target_segments || [],
        key_differentiators: serviceData.key_differentiators || [],
        // Enhanced user context and workflow metadata
        created_by: userId,
        workflow_correlation_id: this.correlationId,
        workflow_cache_key: cacheKey,
        created_via: 'enhanced_workflow_integration',
        operation_id: operationId,
        session_id: this.sessionId,
        security_context: {
          device_fingerprint: this.securityConfig.deviceFingerprint,
          platform_fingerprint: this.securityConfig.platformFingerprint,
          security_validated: this.securityConfig.fingerprintingEnabled
        },
        user_metadata: {
          user_id: userId,
          creation_timestamp: Date.now(),
          workflow_version: '2.0.0',
          integration_source: 'enhanced_multi_step_workflow',
          subscription_tier: this.subscriptionTier
        },
        // Enhanced fields for existing system compatibility
        sub_services: serviceData.key_differentiators.map(diff => ({
          name: diff,
          description: `Key differentiator: ${diff}`,
          created_by: userId,
          workflow_generated: true,
          operation_id: operationId
        })),
        locations: serviceData.target_segments.map(segment => ({
          name: segment,
          type: 'target_segment',
          created_by: userId,
          workflow_generated: true,
          operation_id: operationId
        }))
      };

      // Enhanced API call with security headers
      const response = await api.post('/api/services', servicePayload, {
        headers: {
          'X-Correlation-ID': this.correlationId,
          'X-User-ID': userId,
          'X-Device-Fingerprint': this.securityConfig.deviceFingerprint,
          'X-Platform-Fingerprint': this.securityConfig.platformFingerprint,
          'X-Operation-ID': operationId,
          'X-Session-ID': this.sessionId,
          'X-Workflow-Cache-Key': cacheKey,
          'X-Subscription-Tier': this.subscriptionTier
        }
      });

      // Record success metrics
      this._recordMetric(WorkflowOperationType.SERVICE_CREATION, 'success', {
        duration: Date.now() - startTime,
        user_id: userId,
        operation_id: operationId,
        service_id: response.data.id
      });

      // Reset circuit breaker on success
      this._resetCircuitBreaker();

      // Update performance metrics
      this.performanceMetrics.successfulWorkflows++;

      // Emit success event
      this.emit('service_created', {
        serviceId: response.data.id,
        userId,
        operationId,
        correlationId: this.correlationId,
        timestamp: Date.now()
      });

      console.log('[EnhancedWorkflowIntegrationService] Service saved to system:', response.data.id);
      return response.data;

    } catch (error) {
      // Handle circuit breaker failure
      this._handleCircuitBreakerFailure();

      // Record failure metrics
      this._recordMetric(WorkflowOperationType.SERVICE_CREATION, 'error', {
        duration: Date.now() - startTime,
        user_id: userId,
        operation_id: operationId,
        error: error.message
      });

      // Update performance metrics
      this.performanceMetrics.failedWorkflows++;

      // Emit error event
      this.emit('service_creation_failed', {
        error: error.message,
        userId,
        operationId,
        correlationId: this.correlationId,
        timestamp: Date.now()
      });

      console.error('[EnhancedWorkflowIntegrationService] Failed to save service to system:', error);
      throw error;
    }
  }

  /**
   * Create campaign from workflow content generation
   */
  async createCampaignFromWorkflow(workflowData, userId) {
    try {
      const { serviceDefinition, selectedICP, strategy, generatedContent } = workflowData;

      // Ensure ICP is saved and get its ID
      let icpId = selectedICP._originalData?.id || selectedICP.id;
      
      if (!icpId || icpId.startsWith('temp_') || icpId.startsWith('fallback_')) {
        // Save ICP first if not already saved
        const savedICP = await this.saveICPToSystem(selectedICP, serviceDefinition, userId);
        icpId = savedICP.id;
      }

      // Create campaign payload
      const campaignPayload = {
        name: `${serviceDefinition.name} - ${selectedICP.title} Campaign`,
        description: `Automated campaign for ${serviceDefinition.name} targeting ${selectedICP.title}`,
        icp_id: icpId,
        status: 'draft',
        goals: [
          {
            name: 'Brand Awareness',
            description: 'Increase brand visibility and recognition',
            target_value: 1000,
            metric_type: 'impressions'
          },
          {
            name: 'Lead Generation',
            description: 'Generate qualified leads',
            target_value: 50,
            metric_type: 'leads'
          }
        ],
        // Map strategy to campaign settings
        content_pillars: strategy?.content_pillars || [],
        posting_schedule: strategy?.posting_schedule || {},
        platform_strategies: strategy?.platform_strategies || [],
        // Include generated content
        content_items: generatedContent?.map(post => ({
          content: post.content,
          content_type: post.content_type,
          platform: post.platform,
          hashtags: post.hashtags,
          scheduled_date: post.scheduled_date,
          status: 'draft'
        })) || []
      };

      const response = await api.post('/api/campaigns', campaignPayload, {
        headers: {
          'X-Correlation-ID': this.correlationId
        }
      });

      console.log('Campaign created:', response.data.id);
      return response.data;
    } catch (error) {
      console.error('Failed to create campaign from workflow:', error);
      throw error;
    }
  }

  /**
   * Save ICP to existing system
   */
  async saveICPToSystem(icpData, serviceData, userId) {
    try {
      // Cache the ICP data before saving
      const icpCacheKey = this.cacheWorkflowData('icp', `${serviceData.name}_icp`, icpData, userId);

      // Log ICP save attempt with user context
      console.log('Saving ICP to system:', {
        icpName: icpData.name || icpData.title,
        serviceName: serviceData.name,
        userId,
        correlationId: this.correlationId,
        cacheKey: icpCacheKey,
        timestamp: new Date().toISOString()
      });

      const icpPayload = {
        name: icpData.name || icpData.title,
        description: icpData.description || `AI-generated ICP for ${serviceData.name}`,
        service_id: serviceData.id || serviceData.service_id,
        // User context and workflow metadata
        created_by: userId,
        workflow_correlation_id: this.correlationId,
        workflow_cache_key: icpCacheKey,
        created_via: 'workflow_integration',
        user_metadata: {
          user_id: userId,
          creation_timestamp: Date.now(),
          workflow_version: '1.0',
          integration_source: 'multi_step_workflow',
          service_context: {
            service_id: serviceData.id || serviceData.service_id,
            service_name: serviceData.name
          }
        },
        demographics: {
          company_size: icpData.demographics.company_size,
          industry: icpData.demographics.industry,
          annual_revenue: icpData.demographics.revenue,
          location: icpData.demographics.location,
          employee_count: icpData.demographics.employee_count || icpData.demographics.company_size,
          // User tracking
          created_by: userId,
          workflow_generated: true
        },
        decision_maker: {
          ...(icpData.decision_maker || {
            title: 'Decision Maker',
            department: 'General',
            seniority_level: 'Mid-Level',
            decision_making_power: 'Influencer'
          }),
          created_by: userId,
          workflow_generated: true
        },
        pain_points: icpData.pain_points.map(pp => ({
          description: typeof pp === 'string' ? pp : pp.description,
          priority: 'medium',
          created_by: userId,
          workflow_generated: true,
          source: 'ai_analysis'
        })),
        goals: icpData.goals.map(g => ({
          description: typeof g === 'string' ? g : g.description,
          priority: 'high',
          created_by: userId,
          workflow_generated: true,
          source: 'ai_analysis'
        })),
        objections: (icpData.objections || []).map(obj => ({
          description: typeof obj === 'string' ? obj : obj.description,
          response_strategy: 'Address with case studies and ROI data',
          created_by: userId,
          workflow_generated: true,
          source: 'ai_analysis'
        })),
        content_preferences: icpData.preferred_channels.map(channel => ({
          platform: channel,
          content_type: 'mixed',
          frequency: 'regular',
          created_by: userId,
          workflow_generated: true
        })),
        buying_process: icpData.buying_process || 'Standard evaluation process',
        success_metrics: icpData.success_metrics || ['Engagement', 'Conversion'],
        is_ai_generated: true
      };

      const response = await api.post('/api/icps', icpPayload, {
        headers: {
          'X-Correlation-ID': this.correlationId
        }
      });

      console.log('ICP saved to system:', response.data.id);
      return response.data;
    } catch (error) {
      console.error('Failed to save ICP to system:', error);
      throw error;
    }
  }

  /**
   * Trigger analytics update for new ICP
   */
  async triggerAnalyticsUpdate(icpId) {
    try {
      await api.post('/api/icp-performance/initialize', {
        icp_id: icpId
      }, {
        headers: {
          'X-Correlation-ID': this.correlationId
        }
      });

      console.log('Analytics initialized for ICP:', icpId);
    } catch (error) {
      console.warn('Failed to initialize analytics for ICP:', error);
      // Non-critical, don't throw
    }
  }

  /**
   * Update existing content generator with new ICP
   */
  async updateContentGeneratorICPs() {
    try {
      // Clear content generator cache to refresh ICP list
      workflowCache.delete('content_generator_icps');
      
      // Trigger refresh of ICP list in content generator
      const event = new CustomEvent('icpListUpdated', {
        detail: { correlationId: this.correlationId }
      });
      window.dispatchEvent(event);

      console.log('Content generator ICP list updated');
    } catch (error) {
      console.warn('Failed to update content generator:', error);
    }
  }

  /**
   * Complete workflow integration
   */
  async completeWorkflowIntegration(workflowData, userId) {
    try {
      const results = {
        service: null,
        icp: null,
        campaign: null,
        errors: []
      };

      // 1. Save service to system
      try {
        if (workflowData.serviceDefinition && !workflowData.serviceDefinition.id) {
          results.service = await this.saveServiceToSystem(workflowData.serviceDefinition, userId);
          // Update workflow data with service ID
          workflowData.serviceDefinition.id = results.service.id;
        }
      } catch (error) {
        results.errors.push({ step: 'service', error: error.message });
      }

      // 2. Save ICP to system
      try {
        if (workflowData.selectedICP && !workflowData.selectedICP._originalData?.id) {
          results.icp = await this.saveICPToSystem(
            workflowData.selectedICP, 
            workflowData.serviceDefinition, 
            userId
          );
          // Update workflow data with ICP ID
          workflowData.selectedICP._originalData = results.icp;
        }
      } catch (error) {
        results.errors.push({ step: 'icp', error: error.message });
      }

      // 3. Create campaign
      try {
        if (workflowData.generatedContent && workflowData.generatedContent.length > 0) {
          results.campaign = await this.createCampaignFromWorkflow(workflowData, userId);
        }
      } catch (error) {
        results.errors.push({ step: 'campaign', error: error.message });
      }

      // 4. Trigger analytics and updates
      if (results.icp) {
        await this.triggerAnalyticsUpdate(results.icp.id);
        await this.updateContentGeneratorICPs();
      }

      console.log('Workflow integration completed:', results);
      return results;
    } catch (error) {
      console.error('Workflow integration failed:', error);
      throw error;
    }
  }

  // ===================================================================
  // UTILITY METHODS - Enhanced Platform Service Integration
  // ===================================================================

  /**
   * Check if operation can be executed (circuit breaker)
   *
   * @private
   * @returns {boolean} Whether operation can be executed
   */
  _canExecuteOperation() {
    if (!this.config.enableCircuitBreaker) {
      return true;
    }

    if (this.circuitBreaker.state === 'CLOSED') {
      return true;
    }

    if (this.circuitBreaker.state === 'OPEN') {
      // Check if reset timeout has passed
      if (Date.now() >= this.circuitBreaker.nextAttemptTime) {
        this.circuitBreaker.state = 'HALF_OPEN';
        return true;
      }
      return false;
    }

    // HALF_OPEN state - allow one attempt
    return this.circuitBreaker.state === 'HALF_OPEN';
  }

  /**
   * Handle circuit breaker failure
   *
   * @private
   */
  _handleCircuitBreakerFailure() {
    if (!this.config.enableCircuitBreaker) {
      return;
    }

    this.circuitBreaker.failureCount++;
    this.circuitBreaker.lastFailureTime = Date.now();

    if (this.circuitBreaker.failureCount >= this.circuitBreaker.failureThreshold) {
      this.circuitBreaker.state = 'OPEN';
      this.circuitBreaker.nextAttemptTime = Date.now() + this.circuitBreaker.resetTimeout;
      this.performanceMetrics.circuitBreakerTrips++;

      console.warn('[EnhancedWorkflowIntegrationService] Circuit breaker opened due to failures:', {
        failureCount: this.circuitBreaker.failureCount,
        nextAttemptTime: new Date(this.circuitBreaker.nextAttemptTime).toISOString(),
        sessionId: this.sessionId
      });

      // Emit circuit breaker event
      this.emit('circuit_breaker_opened', {
        failureCount: this.circuitBreaker.failureCount,
        nextAttemptTime: this.circuitBreaker.nextAttemptTime,
        sessionId: this.sessionId
      });
    }
  }

  /**
   * Reset circuit breaker on successful operation
   *
   * @private
   */
  _resetCircuitBreaker() {
    if (!this.config.enableCircuitBreaker) {
      return;
    }

    const wasOpen = this.circuitBreaker.state !== 'CLOSED';

    this.circuitBreaker.state = 'CLOSED';
    this.circuitBreaker.failureCount = 0;
    this.circuitBreaker.lastFailureTime = null;
    this.circuitBreaker.nextAttemptTime = null;

    if (wasOpen) {
      console.log('[EnhancedWorkflowIntegrationService] Circuit breaker reset');
      this.emit('circuit_breaker_reset', {
        timestamp: Date.now(),
        sessionId: this.sessionId
      });
    }
  }

  /**
   * Record metrics using Prometheus collector
   *
   * @private
   * @param {string} operation - Operation name
   * @param {string} status - Operation status
   * @param {Object} metadata - Additional metadata
   */
  _recordMetric(operation, status, metadata = {}) {
    try {
      if (!this.metricsCollector) return;

      this.metricsCollector.recordWorkflowOperation(
        operation,
        this.subscriptionTier || 'creator',
        status,
        metadata.duration || 0,
        {
          correlation_id: this.correlationId,
          session_id: this.sessionId,
          user_id: metadata.user_id || 'unknown',
          operation_id: metadata.operation_id || 'unknown',
          ...metadata
        }
      );

    } catch (error) {
      console.warn('[EnhancedWorkflowIntegrationService] Metrics recording failed:', error);
    }
  }

  /**
   * Generate unique operation ID
   *
   * @private
   * @returns {string} Operation ID
   */
  _generateOperationId() {
    return `workflow_op_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Generate unique session ID
   *
   * @private
   * @returns {string} Session ID
   */
  _generateSessionId() {
    return `workflow_session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Get user subscription tier
   *
   * @private
   * @param {string} userId - User ID (for future use in user-specific tier lookup)
   * @returns {Promise<string>} Subscription tier
   */
  async _getUserSubscriptionTier(userId) {
    try {
      if (this.subscriptionTier) {
        return this.subscriptionTier;
      }

      // Get user info from token to determine subscription tier
      const userInfo = await tokenManager.getTokenPayload();
      if (userInfo && userInfo.subscription) {
        this.subscriptionTier = userInfo.subscription.plan || 'creator';
      } else {
        this.subscriptionTier = 'creator'; // Default fallback
      }

      // Future enhancement: Use userId for user-specific tier lookup
      // This parameter is reserved for future implementation
      if (userId && typeof userId === 'string' && userId.length > 0) {
        // Placeholder for future user-specific tier lookup
        console.debug('[EnhancedWorkflowIntegrationService] User-specific tier lookup placeholder for:', userId);
      }

      return this.subscriptionTier;
    } catch (error) {
      console.warn('[EnhancedWorkflowIntegrationService] Subscription tier detection failed:', error);
      return 'creator'; // Default fallback
    }
  }

  /**
   * Initialize subscription tier detection
   *
   * @private
   */
  async _initializeSubscriptionTierDetection() {
    try {
      await this._getUserSubscriptionTier();
    } catch (error) {
      console.warn('[EnhancedWorkflowIntegrationService] Subscription tier initialization failed:', error);
    }
  }

  /**
   * Refresh security context
   *
   * @private
   */
  async _refreshSecurityContext() {
    try {
      if (this.config.enableSecurityFingerprinting) {
        // Regenerate device fingerprint
        this.securityConfig.deviceFingerprint = await getDeviceFingerprint();

        // Update security check timestamp
        this.securityConfig.lastSecurityCheck = Date.now();

        console.log('[EnhancedWorkflowIntegrationService] Security context refreshed');
      }
    } catch (error) {
      console.warn('[EnhancedWorkflowIntegrationService] Security context refresh failed:', error);
    }
  }

  /**
   * Handle network status changes
   *
   * @private
   * @param {boolean} isOnline - Whether network is online
   */
  _handleNetworkStatusChange(isOnline) {
    console.log(`[EnhancedWorkflowIntegrationService] Network status changed: ${isOnline ? 'online' : 'offline'}`);

    this.emit('network_status_changed', {
      isOnline,
      timestamp: Date.now(),
      sessionId: this.sessionId
    });
  }

  /**
   * Handle configuration updates from platform service
   *
   * @private
   * @param {Object} updateData - Configuration update data
   */
  _handleConfigurationUpdate(updateData) {
    try {
      console.log('[EnhancedWorkflowIntegrationService] Configuration update received:', updateData);

      // Update workflow settings if provided
      if (updateData.workflow) {
        Object.assign(this.config, updateData.workflow);
      }

      // Update security settings if provided
      if (updateData.security) {
        Object.assign(this.securityConfig, updateData.security);
      }

      this.emit('configuration_updated', updateData);

    } catch (error) {
      console.warn('[EnhancedWorkflowIntegrationService] Configuration update failed:', error);
    }
  }

  /**
   * Initialize basic functionality as fallback
   *
   * @private
   */
  _initializeBasicFunctionality() {
    console.log('[EnhancedWorkflowIntegrationService] Initializing basic functionality fallback');

    // Disable enhanced features
    this.config.enableMetrics = false;
    this.config.enableSecurityFingerprinting = false;
    this.config.enableSubscriptionValidation = false;
    this.config.enableCircuitBreaker = false;

    this.isInitialized = true;
  }

  /**
   * Get service status
   *
   * @returns {Object} Service status information
   */
  getServiceStatus() {
    return {
      initialized: this.isInitialized,
      config: this.config,
      circuitBreaker: {
        state: this.circuitBreaker.state,
        failureCount: this.circuitBreaker.failureCount
      },
      performance: {
        workflowsExecuted: this.performanceMetrics.workflowsExecuted,
        successfulWorkflows: this.performanceMetrics.successfulWorkflows,
        failedWorkflows: this.performanceMetrics.failedWorkflows,
        averageExecutionTime: this.performanceMetrics.averageExecutionTime,
        securityValidations: this.performanceMetrics.securityValidations,
        circuitBreakerTrips: this.performanceMetrics.circuitBreakerTrips
      },
      security: {
        fingerprintingEnabled: this.securityConfig.fingerprintingEnabled,
        hasDeviceFingerprint: !!this.securityConfig.deviceFingerprint,
        hasPlatformFingerprint: !!this.securityConfig.platformFingerprint
      },
      subscriptionTier: this.subscriptionTier,
      sessionId: this.sessionId,
      correlationId: this.correlationId,
      activeWorkflows: this.activeWorkflows.size,
      workflowTemplates: this.workflowTemplates.size
    };
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    // Clear active workflows
    this.activeWorkflows.clear();

    // Clear workflow history
    this.workflowHistory.clear();

    // Remove event listeners
    if (typeof window !== 'undefined') {
      window.removeEventListener('online', this._handleNetworkStatusChange);
      window.removeEventListener('offline', this._handleNetworkStatusChange);
      window.removeEventListener('beforeunload', this.cleanup);
    }

    console.log('[EnhancedWorkflowIntegrationService] Cleanup completed');
  }
}

// Create singleton instance
const workflowIntegration = new EnhancedWorkflowIntegrationService();

// Utility functions
export const integrateWorkflowWithSystem = async (workflowData, userId, correlationId) => {
  workflowIntegration.setCorrelationId(correlationId);
  return await workflowIntegration.completeWorkflowIntegration(workflowData, userId);
};

// Export singleton instance as default
export default workflowIntegration;

// Also export as named export for backward compatibility
export { workflowIntegration };
