import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';

const CustomerAnalytics = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Mock data for demonstration
  const customerSegments = [
    { name: 'High Value', value: 150, color: '#0088FE' },
    { name: 'Medium Value', value: 300, color: '#00C49F' },
    { name: 'Low Value', value: 400, color: '#FFBB28' },
    { name: 'At Risk', value: 150, color: '#FF8042' },
  ];

  const ltvData = [
    { segment: 'Creator', ltv: 1200, customers: 400, churn: 0.08 },
    { segment: 'Accelerator', ltv: 2800, customers: 300, churn: 0.04 },
    { segment: 'Dominator', ltv: 5200, customers: 150, churn: 0.02 },
  ];

  const cohortData = [
    { month: 'Jan 2024', month0: 100, month1: 85, month3: 70, month6: 60 },
    { month: 'Feb 2024', month0: 120, month1: 95, month3: 78, month6: null },
    { month: 'Mar 2024', month0: 110, month1: 88, month3: 72, month6: null },
    { month: 'Apr 2024', month0: 130, month1: 102, month3: null, month6: null },
    { month: 'May 2024', month0: 125, month1: 98, month3: null, month6: null },
    { month: 'Jun 2024', month0: 140, month1: null, month3: null, month6: null },
  ];

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={3}>
          {/* Customer Segmentation */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Customer Segmentation" />
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={customerSegments}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {customerSegments.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
                <Box mt={2}>
                  {customerSegments.map((segment) => (
                    <Box key={segment.name} display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Box display="flex" alignItems="center">
                        <Box
                          sx={{
                            width: 12,
                            height: 12,
                            borderRadius: '50%',
                            backgroundColor: segment.color,
                            mr: 1,
                          }}
                        />
                        <Typography variant="body2">{segment.name}</Typography>
                      </Box>
                      <Typography variant="body2" fontWeight="bold">
                        {segment.value} customers
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Lifetime Value by Plan */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Customer Lifetime Value by Plan" />
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={ltvData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="segment" />
                    <YAxis tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`} />
                    <Tooltip formatter={(value) => formatCurrency(value)} />
                    <Bar dataKey="ltv" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* LTV Summary Table */}
          <Grid item xs={12}>
            <Card>
              <CardHeader title="Customer Metrics by Plan" />
              <CardContent>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Plan</TableCell>
                        <TableCell align="right">Customers</TableCell>
                        <TableCell align="right">Average LTV</TableCell>
                        <TableCell align="right">Churn Rate</TableCell>
                        <TableCell align="right">Health Score</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {ltvData.map((row) => (
                        <TableRow key={row.segment}>
                          <TableCell component="th" scope="row">
                            <Typography variant="body2" fontWeight="bold">
                              {row.segment}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">{row.customers}</TableCell>
                          <TableCell align="right">
                            <Typography variant="body2" fontWeight="bold">
                              {formatCurrency(row.ltv)}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            <Chip
                              label={formatPercentage(row.churn)}
                              color={row.churn <= 0.05 ? "success" : row.churn <= 0.08 ? "warning" : "error"}
                              size="small"
                            />
                          </TableCell>
                          <TableCell align="right">
                            <Box display="flex" alignItems="center" gap={1}>
                              <LinearProgress
                                variant="determinate"
                                value={Math.max(0, 100 - (row.churn * 1000))}
                                sx={{ width: 60, height: 8, borderRadius: 4 }}
                                color={row.churn <= 0.05 ? "success" : row.churn <= 0.08 ? "warning" : "error"}
                              />
                              <Typography variant="caption">
                                {Math.max(0, 100 - (row.churn * 1000)).toFixed(0)}%
                              </Typography>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Cohort Analysis */}
          <Grid item xs={12}>
            <Card>
              <CardHeader 
                title="Cohort Retention Analysis" 
                subheader="Customer retention by signup month"
              />
              <CardContent>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Cohort</TableCell>
                        <TableCell align="center">Month 0</TableCell>
                        <TableCell align="center">Month 1</TableCell>
                        <TableCell align="center">Month 3</TableCell>
                        <TableCell align="center">Month 6</TableCell>
                        <TableCell align="center">Retention Rate</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {cohortData.map((cohort) => {
                        const retentionRate = cohort.month6 ? cohort.month6 / cohort.month0 : 
                                            cohort.month3 ? cohort.month3 / cohort.month0 :
                                            cohort.month1 ? cohort.month1 / cohort.month0 : 1;
                        
                        return (
                          <TableRow key={cohort.month}>
                            <TableCell component="th" scope="row">
                              <Typography variant="body2" fontWeight="bold">
                                {cohort.month}
                              </Typography>
                            </TableCell>
                            <TableCell align="center">
                              <Chip label={cohort.month0} color="primary" size="small" />
                            </TableCell>
                            <TableCell align="center">
                              {cohort.month1 ? (
                                <Box>
                                  <Typography variant="body2">{cohort.month1}</Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    {formatPercentage(cohort.month1 / cohort.month0)}
                                  </Typography>
                                </Box>
                              ) : (
                                <Typography variant="body2" color="text.secondary">-</Typography>
                              )}
                            </TableCell>
                            <TableCell align="center">
                              {cohort.month3 ? (
                                <Box>
                                  <Typography variant="body2">{cohort.month3}</Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    {formatPercentage(cohort.month3 / cohort.month0)}
                                  </Typography>
                                </Box>
                              ) : (
                                <Typography variant="body2" color="text.secondary">-</Typography>
                              )}
                            </TableCell>
                            <TableCell align="center">
                              {cohort.month6 ? (
                                <Box>
                                  <Typography variant="body2">{cohort.month6}</Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    {formatPercentage(cohort.month6 / cohort.month0)}
                                  </Typography>
                                </Box>
                              ) : (
                                <Typography variant="body2" color="text.secondary">-</Typography>
                              )}
                            </TableCell>
                            <TableCell align="center">
                              <Chip
                                label={formatPercentage(retentionRate)}
                                color={retentionRate >= 0.6 ? "success" : retentionRate >= 0.4 ? "warning" : "error"}
                                size="small"
                              />
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Key Customer Insights */}
          <Grid item xs={12}>
            <Card>
              <CardHeader title="Key Customer Insights" />
              <CardContent>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h4" color="primary">
                        $2,400
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Average Customer LTV
                      </Typography>
                      <Typography variant="body2" color="success.main">
                        +12% vs last quarter
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h4" color="primary">
                        68%
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        6-Month Retention
                      </Typography>
                      <Typography variant="body2" color="success.main">
                        Above industry average
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h4" color="primary">
                        4.2
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Average Months to Churn
                      </Typography>
                      <Typography variant="body2" color="warning.main">
                        Monitor closely
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h4" color="primary">
                        150
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        At-Risk Customers
                      </Typography>
                      <Typography variant="body2" color="error.main">
                        Requires attention
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default CustomerAnalytics;
