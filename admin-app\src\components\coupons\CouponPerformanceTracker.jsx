import React, { useState, useMemo } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Alert,
  Skeleton,
  LinearProgress,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Timeline as TimelineIcon,
  LocalOffer as CouponIcon,
} from '@mui/icons-material';
import { 
  formatDate, 
  formatDateTime, 
  getRelativeTime, 
  exportToCSV, 
  debounce,
  formatDiscount,
  getCouponStatus,
  formatCurrency,
} from '../../utils/couponHelpers';

/**
 * Coupon Performance Tracker Component
 * Displays detailed performance metrics and tracking for coupons
 */
const CouponPerformanceTracker = ({ 
  data, 
  loading, 
  error,
  onRefresh,
  className,
  ...props 
}) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    status: 'all',
    type: 'all',
    performance: 'all',
  });
  const [filterMenuAnchor, setFilterMenuAnchor] = useState(null);

  // Debounced search function
  const debouncedSearch = useMemo(
    () => debounce((term) => {
      setSearchTerm(term);
      setPage(0);
    }, 300),
    []
  );

  // Calculate performance metrics for each coupon
  const couponsWithMetrics = useMemo(() => {
    if (!data?.coupons || !Array.isArray(data.coupons)) {
      return [];
    }

    return data.coupons.map(coupon => {
      const redemptions = coupon.redemption_count || 0;
      const maxRedemptions = coupon.max_redemptions || Infinity;
      const usageRate = maxRedemptions === Infinity ? redemptions : (redemptions / maxRedemptions) * 100;
      
      // Calculate estimated revenue impact
      const avgOrderValue = 100; // Assume $100 average order
      const discountAmount = coupon.discount_type === 'percentage' ? 
        (coupon.discount_value / 100) * avgOrderValue :
        coupon.discount_value;
      const revenueImpact = redemptions * discountAmount;
      
      // Performance rating
      let performanceRating = 'low';
      if (usageRate > 75 || redemptions > 50) {
        performanceRating = 'high';
      } else if (usageRate > 25 || redemptions > 10) {
        performanceRating = 'medium';
      }
      
      return {
        ...coupon,
        metrics: {
          usageRate,
          revenueImpact,
          performanceRating,
          redemptions,
        },
      };
    });
  }, [data?.coupons]);

  // Filter and search coupons
  const filteredCoupons = useMemo(() => {
    let filtered = [...couponsWithMetrics];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(coupon => 
        coupon.code?.toLowerCase().includes(term) ||
        coupon.name?.toLowerCase().includes(term) ||
        coupon.description?.toLowerCase().includes(term)
      );
    }

    // Apply status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(coupon => {
        const status = getCouponStatus(coupon);
        return status.status === filters.status;
      });
    }

    // Apply type filter
    if (filters.type !== 'all') {
      filtered = filtered.filter(coupon => coupon.discount_type === filters.type);
    }

    // Apply performance filter
    if (filters.performance !== 'all') {
      filtered = filtered.filter(coupon => coupon.metrics.performanceRating === filters.performance);
    }

    // Sort by performance (redemptions desc)
    filtered.sort((a, b) => b.metrics.redemptions - a.metrics.redemptions);

    return filtered;
  }, [couponsWithMetrics, searchTerm, filters]);

  // Get unique types for filter dropdown
  const availableTypes = useMemo(() => {
    if (!data?.coupons) return [];
    const types = [...new Set(data.coupons.map(c => c.discount_type).filter(Boolean))];
    return types.sort();
  }, [data?.coupons]);

  // Handle pagination
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle search
  const handleSearchChange = (event) => {
    debouncedSearch(event.target.value);
  };

  // Handle filter changes
  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({ ...prev, [filterType]: value }));
    setPage(0);
    setFilterMenuAnchor(null);
  };

  // Handle export
  const handleExport = () => {
    const exportColumns = [
      { key: 'code', label: 'Code' },
      { key: 'name', label: 'Name' },
      { key: 'discount_type', label: 'Type' },
      { key: 'discount_value', label: 'Value', type: 'discount' },
      { key: 'metrics.redemptions', label: 'Redemptions' },
      { key: 'metrics.usageRate', label: 'Usage Rate (%)' },
      { key: 'metrics.revenueImpact', label: 'Revenue Impact', type: 'currency' },
      { key: 'metrics.performanceRating', label: 'Performance' },
      { key: 'created_at', label: 'Created At', type: 'date' },
    ];
    
    exportToCSV(
      filteredCoupons, 
      `coupon-performance-${formatDate(new Date())}`, 
      exportColumns
    );
  };

  // Get performance color
  const getPerformanceColor = (rating) => {
    switch (rating) {
      case 'high':
        return 'success';
      case 'medium':
        return 'warning';
      case 'low':
        return 'error';
      default:
        return 'default';
    }
  };

  // Render loading skeleton
  if (loading && !data?.coupons) {
    return (
      <Box className={className} {...props}>
        <Card variant="glass">
          <CardContent>
            <Skeleton variant="text" width="40%" height={32} sx={{ mb: 2 }} />
            {[1, 2, 3, 4, 5].map((item) => (
              <Skeleton key={item} variant="rectangular" height={60} sx={{ mb: 1 }} />
            ))}
          </CardContent>
        </Card>
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Box className={className} {...props}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  // Render empty state
  if (!data?.coupons || data.coupons.length === 0) {
    return (
      <Box className={className} {...props}>
        <Card variant="glass">
          <CardContent sx={{ textAlign: 'center', py: 6 }}>
            <CouponIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No Coupons Found
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Performance data will appear here once you have active coupons.
            </Typography>
          </CardContent>
        </Card>
      </Box>
    );
  }

  const paginatedCoupons = filteredCoupons.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  return (
    <Box className={className} {...props}>
      <Card variant="glass">
        <CardContent>
          {/* Header */}
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TimelineIcon />
              Coupon Performance Tracker
            </Typography>
            <Box display="flex" gap={1}>
              <Tooltip title="Export Data">
                <IconButton onClick={handleExport} disabled={filteredCoupons.length === 0}>
                  <DownloadIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Filters">
                <IconButton onClick={(e) => setFilterMenuAnchor(e.currentTarget)}>
                  <FilterIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* Search and Filters */}
          <Box display="flex" gap={2} mb={3}>
            <TextField
              placeholder="Search by code, name, or description..."
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ flexGrow: 1 }}
            />
          </Box>

          {/* Results Summary */}
          <Typography variant="body2" color="text.secondary" mb={2}>
            Showing {paginatedCoupons.length} of {filteredCoupons.length} coupons
          </Typography>

          {/* Performance Table */}
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Code</TableCell>
                  <TableCell>Name</TableCell>
                  <TableCell>Discount</TableCell>
                  <TableCell>Redemptions</TableCell>
                  <TableCell>Usage Rate</TableCell>
                  <TableCell>Revenue Impact</TableCell>
                  <TableCell>Performance</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedCoupons.map((coupon) => {
                  const status = getCouponStatus(coupon);
                  return (
                    <TableRow key={coupon.id} hover>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace" fontWeight="medium">
                          {coupon.code}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {coupon.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {coupon.description?.substring(0, 50)}...
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {formatDiscount(coupon)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography variant="body2" fontWeight="medium">
                            {coupon.metrics.redemptions}
                          </Typography>
                          {coupon.max_redemptions && (
                            <Typography variant="caption" color="text.secondary">
                              / {coupon.max_redemptions}
                            </Typography>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <LinearProgress
                            variant="determinate"
                            value={Math.min(coupon.metrics.usageRate, 100)}
                            sx={{ width: 80, height: 6, borderRadius: 3, mb: 0.5 }}
                            color={getPerformanceColor(coupon.metrics.performanceRating)}
                          />
                          <Typography variant="caption" color="text.secondary">
                            {coupon.metrics.usageRate.toFixed(1)}%
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {formatCurrency(coupon.metrics.revenueImpact)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={coupon.metrics.performanceRating.charAt(0).toUpperCase() + coupon.metrics.performanceRating.slice(1)}
                          color={getPerformanceColor(coupon.metrics.performanceRating)}
                          size="small"
                          icon={coupon.metrics.performanceRating === 'high' ? <TrendingUpIcon /> : 
                                coupon.metrics.performanceRating === 'low' ? <TrendingDownIcon /> : undefined}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={status.status.charAt(0).toUpperCase() + status.status.slice(1)}
                          color={status.color}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Tooltip title="View Details">
                          <IconButton size="small">
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          <TablePagination
            component="div"
            count={filteredCoupons.length}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={[10, 25, 50, 100]}
          />
        </CardContent>
      </Card>

      {/* Filter Menu */}
      <Menu
        anchorEl={filterMenuAnchor}
        open={Boolean(filterMenuAnchor)}
        onClose={() => setFilterMenuAnchor(null)}
      >
        <MenuItem disabled>
          <Typography variant="subtitle2">Filter Options</Typography>
        </MenuItem>
        
        <MenuItem>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              label="Status"
            >
              <MenuItem value="all">All Statuses</MenuItem>
              <MenuItem value="active">Active</MenuItem>
              <MenuItem value="inactive">Inactive</MenuItem>
              <MenuItem value="expired">Expired</MenuItem>
              <MenuItem value="exhausted">Exhausted</MenuItem>
            </Select>
          </FormControl>
        </MenuItem>

        <MenuItem>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Type</InputLabel>
            <Select
              value={filters.type}
              onChange={(e) => handleFilterChange('type', e.target.value)}
              label="Type"
            >
              <MenuItem value="all">All Types</MenuItem>
              {availableTypes.map((type) => (
                <MenuItem key={type} value={type}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </MenuItem>

        <MenuItem>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Performance</InputLabel>
            <Select
              value={filters.performance}
              onChange={(e) => handleFilterChange('performance', e.target.value)}
              label="Performance"
            >
              <MenuItem value="all">All Performance</MenuItem>
              <MenuItem value="high">High</MenuItem>
              <MenuItem value="medium">Medium</MenuItem>
              <MenuItem value="low">Low</MenuItem>
            </Select>
          </FormControl>
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default CouponPerformanceTracker;
