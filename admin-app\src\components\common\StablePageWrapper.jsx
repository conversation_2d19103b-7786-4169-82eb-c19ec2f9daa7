import React from 'react';
import { Box, Container, Paper } from '@mui/material';
import { useTheme } from '@mui/material/styles';

/**
 * StablePageWrapper component for consistent layout across admin pages.
 * Implements Material-UI glass morphism styling with 8px grid spacing.
 * Ensures WCAG 2.1 AA compliance and responsive design.
 */
const StablePageWrapper = ({ 
  children, 
  maxWidth = "xl", 
  disableGutters = false,
  enableGlassMorphism = true,
  sx = {} 
}) => {
  const theme = useTheme();

  const glassMorphismStyles = enableGlassMorphism ? {
    background: `linear-gradient(135deg, 
      ${theme.palette.background.paper}80 0%, 
      ${theme.palette.background.default}40 100%)`,
    backdropFilter: 'blur(10px)',
    border: `1px solid ${theme.palette.divider}30`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${theme.palette.common.black}10`,
  } : {};

  return (
    <Container 
      maxWidth={maxWidth} 
      disableGutters={disableGutters}
      sx={{
        py: theme.spacing(3), // 24px vertical padding (8px grid)
        px: theme.spacing(2), // 16px horizontal padding (8px grid)
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        ...sx
      }}
    >
      <Paper
        elevation={0}
        sx={{
          flex: 1,
          p: theme.spacing(3), // 24px padding (8px grid)
          ...glassMorphismStyles,
          // WCAG 2.1 AA compliance
          '&:focus-visible': {
            outline: `2px solid ${theme.palette.primary.main}`,
            outlineOffset: '2px',
          },
          // Responsive design
          [theme.breakpoints.down('sm')]: {
            p: theme.spacing(2), // 16px padding on mobile
            mx: theme.spacing(1), // 8px margin on mobile
          },
          // High contrast mode support
          '@media (prefers-contrast: high)': {
            border: `2px solid ${theme.palette.text.primary}`,
            background: theme.palette.background.paper,
            backdropFilter: 'none',
          },
          // Reduced motion support
          '@media (prefers-reduced-motion: reduce)': {
            transition: 'none',
          },
          // Print styles
          '@media print': {
            background: 'white',
            backdropFilter: 'none',
            boxShadow: 'none',
            border: '1px solid black',
          }
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: theme.spacing(3), // 24px gap (8px grid)
            minHeight: '100%',
          }}
        >
          {children}
        </Box>
      </Paper>
    </Container>
  );
};

export default StablePageWrapper;
