import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Typography,
  Box,
  Chip,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel,
  Divider
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Security as SecurityIcon,
  Settings as SettingsIcon,
  MonitorHeart as HealthIcon
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';

import { externalApiService } from '../../services/externalApiService';

const ApiConfigurationForm = ({ 
  open, 
  onClose, 
  onSave, 
  config = null, 
  mode = 'create' // 'create' or 'edit'
}) => {
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    provider: '',
    description: '',
    base_url: '',
    version: '',
    environment: 'production',
    auth_type: 'api_key',
    credentials: {
      api_key: '',
      api_secret: '',
      access_token: '',
      refresh_token: '',
      client_id: '',
      client_secret: '',
      custom_headers: {},
      additional_config: {}
    },
    status: 'active',
    rate_limit: {
      requests_per_period: 1000,
      period: 'hour',
      burst_limit: null
    },
    health_check: {
      enabled: true,
      endpoint: '',
      method: 'GET',
      expected_status_codes: [200, 201, 204],
      timeout_seconds: 10,
      interval_minutes: 5,
      failure_threshold: 3,
      success_threshold: 2
    },
    tags: []
  });
  const [tagInput, setTagInput] = useState('');

  // Initialize form data when config changes
  useEffect(() => {
    if (config && mode === 'edit') {
      setFormData({
        name: config.name || '',
        provider: config.provider || '',
        description: config.description || '',
        base_url: config.base_url || '',
        version: config.version || '',
        environment: config.environment || 'production',
        auth_type: config.auth_type || 'api_key',
        credentials: {
          api_key: '',
          api_secret: '',
          access_token: '',
          refresh_token: '',
          client_id: '',
          client_secret: '',
          custom_headers: {},
          additional_config: {}
        },
        status: config.status || 'active',
        rate_limit: config.rate_limit || {
          requests_per_period: 1000,
          period: 'hour',
          burst_limit: null
        },
        health_check: config.health_check || {
          enabled: true,
          endpoint: '',
          method: 'GET',
          expected_status_codes: [200, 201, 204],
          timeout_seconds: 10,
          interval_minutes: 5,
          failure_threshold: 3,
          success_threshold: 2
        },
        tags: config.tags || []
      });
    } else if (mode === 'create') {
      // Reset form for create mode
      setFormData({
        name: '',
        provider: '',
        description: '',
        base_url: '',
        version: '',
        environment: 'production',
        auth_type: 'api_key',
        credentials: {
          api_key: '',
          api_secret: '',
          access_token: '',
          refresh_token: '',
          client_id: '',
          client_secret: '',
          custom_headers: {},
          additional_config: {}
        },
        status: 'active',
        rate_limit: {
          requests_per_period: 1000,
          period: 'hour',
          burst_limit: null
        },
        health_check: {
          enabled: true,
          endpoint: '',
          method: 'GET',
          expected_status_codes: [200, 201, 204],
          timeout_seconds: 10,
          interval_minutes: 5,
          failure_threshold: 3,
          success_threshold: 2
        },
        tags: []
      });
    }
  }, [config, mode]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNestedInputChange = (section, field, value) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSubmit = async () => {
    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!formData.name || !formData.provider || !formData.base_url) {
        throw new Error('Name, Provider, and Base URL are required');
      }

      // Clean up credentials - only include non-empty values
      const cleanCredentials = {};
      Object.entries(formData.credentials).forEach(([key, value]) => {
        if (value && value !== '') {
          cleanCredentials[key] = value;
        }
      });

      const submitData = {
        ...formData,
        credentials: cleanCredentials
      };

      let result;
      if (mode === 'create') {
        result = await externalApiService.createApiConfiguration(submitData);
      } else {
        result = await externalApiService.updateApiConfiguration(config.id, submitData);
      }

      onSave(result);
      onClose();
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const renderCredentialsSection = () => {
    const authType = formData.auth_type;
    
    return (
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box display="flex" alignItems="center" gap={1}>
            <SecurityIcon color="primary" />
            <Typography variant="h6">Authentication & Credentials</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Authentication Type</InputLabel>
                <Select
                  value={authType}
                  onChange={(e) => handleInputChange('auth_type', e.target.value)}
                  label="Authentication Type"
                >
                  {externalApiService.getAuthenticationTypes().map(type => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {(authType === 'api_key' || authType === 'bearer_token') && (
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="API Key"
                  type="password"
                  value={formData.credentials.api_key}
                  onChange={(e) => handleNestedInputChange('credentials', 'api_key', e.target.value)}
                  placeholder="Enter your API key"
                />
              </Grid>
            )}

            {authType === 'oauth2' && (
              <>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Client ID"
                    value={formData.credentials.client_id}
                    onChange={(e) => handleNestedInputChange('credentials', 'client_id', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Client Secret"
                    type="password"
                    value={formData.credentials.client_secret}
                    onChange={(e) => handleNestedInputChange('credentials', 'client_secret', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Access Token"
                    type="password"
                    value={formData.credentials.access_token}
                    onChange={(e) => handleNestedInputChange('credentials', 'access_token', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Refresh Token"
                    type="password"
                    value={formData.credentials.refresh_token}
                    onChange={(e) => handleNestedInputChange('credentials', 'refresh_token', e.target.value)}
                  />
                </Grid>
              </>
            )}

            {authType === 'basic_auth' && (
              <>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Username"
                    value={formData.credentials.client_id}
                    onChange={(e) => handleNestedInputChange('credentials', 'client_id', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Password"
                    type="password"
                    value={formData.credentials.client_secret}
                    onChange={(e) => handleNestedInputChange('credentials', 'client_secret', e.target.value)}
                  />
                </Grid>
              </>
            )}
          </Grid>
        </AccordionDetails>
      </Accordion>
    );
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          background: `linear-gradient(135deg, 
            ${theme.palette.background.paper}80 0%, 
            ${theme.palette.background.default}40 100%)`,
          backdropFilter: 'blur(10px)',
          border: `1px solid ${theme.palette.divider}30`,
        }
      }}
    >
      <DialogTitle>
        <Typography variant="h5">
          {mode === 'create' ? 'Add New API Configuration' : 'Edit API Configuration'}
        </Typography>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              Basic Information
            </Typography>
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="API Name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required>
              <InputLabel>Provider</InputLabel>
              <Select
                value={formData.provider}
                onChange={(e) => handleInputChange('provider', e.target.value)}
                label="Provider"
              >
                {externalApiService.getSupportedProviders().map(provider => (
                  <MenuItem key={provider.value} value={provider.value}>
                    {provider.label} ({provider.category})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Description"
              multiline
              rows={2}
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
            />
          </Grid>

          <Grid item xs={12} sm={8}>
            <TextField
              fullWidth
              label="Base URL"
              value={formData.base_url}
              onChange={(e) => handleInputChange('base_url', e.target.value)}
              required
              placeholder="https://api.example.com/v1"
            />
          </Grid>

          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Version"
              value={formData.version}
              onChange={(e) => handleInputChange('version', e.target.value)}
              placeholder="v1"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Environment</InputLabel>
              <Select
                value={formData.environment}
                onChange={(e) => handleInputChange('environment', e.target.value)}
                label="Environment"
              >
                <MenuItem value="development">Development</MenuItem>
                <MenuItem value="staging">Staging</MenuItem>
                <MenuItem value="production">Production</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
                label="Status"
              >
                {externalApiService.getStatusOptions().map(status => (
                  <MenuItem key={status.value} value={status.value}>
                    {status.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Tags */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>
              Tags
            </Typography>
            <Box display="flex" gap={1} flexWrap="wrap" mb={1}>
              {formData.tags.map((tag, index) => (
                <Chip
                  key={index}
                  label={tag}
                  onDelete={() => handleRemoveTag(tag)}
                  size="small"
                />
              ))}
            </Box>
            <Box display="flex" gap={1}>
              <TextField
                size="small"
                label="Add Tag"
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
              />
              <Button onClick={handleAddTag} variant="outlined" size="small">
                Add
              </Button>
            </Box>
          </Grid>

          {/* Credentials Section */}
          <Grid item xs={12}>
            {renderCredentialsSection()}
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          {loading ? 'Saving...' : (mode === 'create' ? 'Create' : 'Update')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ApiConfigurationForm;
