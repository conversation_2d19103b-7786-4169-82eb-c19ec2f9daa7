{"name": "ace-social-admin", "version": "1.0.0", "description": "Admin Panel for ACE Social", "private": true, "type": "module", "scripts": {"dev": "vite --port 3001", "dev:stable": "cross-env DISABLE_WEBSOCKETS=true vite --port 3001 --host", "build": "vite build", "build:production": "vite build --mode production", "preview": "vite preview --port 3001", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@mui/x-date-pickers": "^6.18.2", "axios": "^1.6.2", "date-fns": "^2.30.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "recharts": "^2.8.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "cross-env": "^7.0.3", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "vite": "^5.0.0", "vitest": "^1.0.0"}}